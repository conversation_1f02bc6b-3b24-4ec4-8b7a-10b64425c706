package consts

// 文章语言类型
const (
	ArticleLanguageZh = iota
	ArticleLanguageEn
	ArticleLanguageId
)

// 定义常量
const (
	Zero = iota
	One
	Two
	Three
	Four
	Five
	Six
	Seven
	Eight
	Nine
	Ten
)

// 文章状态:待发布、已发布、已下线
const (
	ArticleStatus          = iota // 待发布
	ArticleStatusPublished        // 已发布
	ArticleStatusOffline          // 已下线
)

// 文章状态文案
func GetArticleStatusText(status int) string {
	switch status {
	case ArticleStatus:
		return "待发布"
	case ArticleStatusPublished:
		return "已发布"
	case ArticleStatusOffline:
		return "已下线"
	default:
		return "未知"
	}
}

// 是否加入头条
const (
	ArticleIsNotTop = iota
	ArticleIsTop
)

// 是否加入头条文案
func GetArticleIsTopText(isTop int) string {
	switch isTop {
	case ArticleIsNotTop:
		return "否"
	case ArticleIsTop:
		return "是"
	default:
		return "未知"
	}
}

// 是否推荐
const (
	ArticleIsNotRecommend = iota
	ArticleIsRecommend
)

// 是否推荐文案
func GetArticleIsRecommendText(isRecommend int) string {
	switch isRecommend {
	case ArticleIsNotRecommend:
		return "否"
	case ArticleIsRecommend:
		return "是"
	default:
		return "未知"
	}
}
