package consts

// Prefix/uuid.FileSuffix
// 例子 pub/3hn1300cyj7r4t47ugp100t6jmkszl1y.jpg
const (
	FileDefaultBucketName = "catalog" // 上传文件的bucket name
	FilePrefixPublicDir   = "pub"     // 公开目录
	FilePrefixTmpDir      = "tmp"     // 临时目录
	FilePrefixInterDir    = "in"      // 内部目录-暂时没有用上
)

const (
	FileTypeImage = "img"
	FileTypeVideo = "v"
	FileTypeBin   = "f"
)

// PrefixName 前缀
var PrefixName = map[string]struct{}{
	FilePrefixPublicDir: {},
	FilePrefixInterDir:  {},
}

const (
	FileTypeS3Minio = "minio"
	FileTypeS3Aws   = "aws"
	FileTypeS3Obs   = "obs"
)
