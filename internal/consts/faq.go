package consts

// 反馈结果: 1 无效反馈 2有效反馈
const (
	FeedbackResult        = iota // 0 待处理
	FeedbackResultInvalid        // 1 无效反馈
	FeedbackResultValid          // 2 有效反馈
)

// 反馈状态 1 未处理 2已处理
const (
	FeedbackStatus          = iota //   0 待处理
	FeedbackStatusUntreated        // 1 待处理
	FeedbackStatusProcessed        // 2 已处理
)

// 获取反馈状态文案
func GetFeedbackStatusText(status int) string {
	switch status {
	case FeedbackStatus:
		return "待处理"
	case FeedbackStatusUntreated:
		return "待处理"
	case FeedbackStatusProcessed:
		return "已处理"
	default:
		return "未知"
	}
}

// 获取反馈结果文案
func GetFeedbackResultText(status int) string {
	switch status {
	case FeedbackResult:
		return "待处理"
	case FeedbackResultInvalid:
		return "无效反馈"
	case FeedbackResultValid:
		return "有效反馈"
	default:
		return "未知"
	}
}

// faq状态:待发布、已发布、已下线
const (
	FaqStatus          = iota // 待发布
	FaqStatusPublished        // 已发布
	FaqStatusOffline          // 已下线
)

// faq状态文案
func GetFaqStatusText(status int) string {
	switch status {
	case FaqStatus:
		return "待发布"
	case FaqStatusPublished:
		return "已发布"
	case FaqStatusOffline:
		return "已下线"
	default:
		return "未知"
	}
}

// 名言状态:待发布、已发布、已下线
const (
	WisdomStatus          = iota // 待发布
	WisdomStatusPublished        // 已发布
	WisdomStatusOffline          // 已下线
)

// 名言状态文案
func GetWisdomStatusText(status int) string {
	switch status {
	case WisdomStatus:
		return "待发布"
	case WisdomStatusPublished:
		return "已发布"
	case WisdomStatusOffline:
		return "已下线"
	default:
		return "未知"
	}
}
