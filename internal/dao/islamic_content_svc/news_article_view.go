// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// newsArticleViewDao is the data access object for the table news_article_view.
// You can define custom methods on it to extend its functionality as needed.
type newsArticleViewDao struct {
	*internal.NewsArticleViewDao
}

var (
	// NewsArticleView is a globally accessible object for table news_article_view operations.
	NewsArticleView = newsArticleViewDao{internal.NewNewsArticleViewDao()}
)

// Add your custom methods and functionality below.
