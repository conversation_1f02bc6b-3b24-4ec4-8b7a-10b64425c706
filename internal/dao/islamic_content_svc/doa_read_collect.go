// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// doaReadCollectDao is the data access object for the table doa_read_collect.
// You can define custom methods on it to extend its functionality as needed.
type doaReadCollectDao struct {
	*internal.DoaReadCollectDao
}

var (
	// DoaReadCollect is a globally accessible object for table doa_read_collect operations.
	DoaReadCollect = doaReadCollectDao{internal.NewDoaReadCollectDao()}
)

// Add your custom methods and functionality below.
