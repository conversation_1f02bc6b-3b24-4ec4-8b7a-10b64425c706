// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// hajiLandmarkLanguagesDao is the data access object for the table haji_landmark_languages.
// You can define custom methods on it to extend its functionality as needed.
type hajiLandmarkLanguagesDao struct {
	*internal.HajiLandmarkLanguagesDao
}

var (
	// HajiLandmarkLanguages is a globally accessible object for table haji_landmark_languages operations.
	HajiLandmarkLanguages = hajiLandmarkLanguagesDao{internal.NewHajiLandmarkLanguagesDao()}
)

// Add your custom methods and functionality below.
