// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// newsTahlilDao is the data access object for the table news_tahlil.
// You can define custom methods on it to extend its functionality as needed.
type newsTahlilDao struct {
	*internal.NewsTahlilDao
}

var (
	// NewsTahlil is a globally accessible object for table news_tahlil operations.
	NewsTahlil = newsTahlilDao{internal.NewNewsTahlilDao()}
)

// Add your custom methods and functionality below.
