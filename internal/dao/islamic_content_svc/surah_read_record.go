// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// surahReadRecordDao is the data access object for the table surah_read_record.
// You can define custom methods on it to extend its functionality as needed.
type surahReadRecordDao struct {
	*internal.SurahReadRecordDao
}

var (
	// SurahReadRecord is a globally accessible object for table surah_read_record operations.
	SurahReadRecord = surahReadRecordDao{internal.NewSurahReadRecordDao()}
)

// Add your custom methods and functionality below.
