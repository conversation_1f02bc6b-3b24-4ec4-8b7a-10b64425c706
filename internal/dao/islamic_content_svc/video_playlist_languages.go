// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// videoPlaylistLanguagesDao is the data access object for the table video_playlist_languages.
// You can define custom methods on it to extend its functionality as needed.
type videoPlaylistLanguagesDao struct {
	*internal.VideoPlaylistLanguagesDao
}

var (
	// VideoPlaylistLanguages is a globally accessible object for table video_playlist_languages operations.
	VideoPlaylistLanguages = videoPlaylistLanguagesDao{internal.NewVideoPlaylistLanguagesDao()}
)

// Add your custom methods and functionality below.
