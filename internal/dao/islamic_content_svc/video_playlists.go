// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// videoPlaylistsDao is the data access object for the table video_playlists.
// You can define custom methods on it to extend its functionality as needed.
type videoPlaylistsDao struct {
	*internal.VideoPlaylistsDao
}

var (
	// VideoPlaylists is a globally accessible object for table video_playlists operations.
	VideoPlaylists = videoPlaylistsDao{internal.NewVideoPlaylistsDao()}
)

// Add your custom methods and functionality below.
