// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// videoCategoriesDao is the data access object for the table video_categories.
// You can define custom methods on it to extend its functionality as needed.
type videoCategoriesDao struct {
	*internal.VideoCategoriesDao
}

var (
	// VideoCategories is a globally accessible object for table video_categories operations.
	VideoCategories = videoCategoriesDao{internal.NewVideoCategoriesDao()}
)

// Add your custom methods and functionality below.
