// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// newsTopicArticleDao is the data access object for the table news_topic_article.
// You can define custom methods on it to extend its functionality as needed.
type newsTopicArticleDao struct {
	*internal.NewsTopicArticleDao
}

var (
	// NewsTopicArticle is a globally accessible object for table news_topic_article operations.
	NewsTopicArticle = newsTopicArticleDao{internal.NewNewsTopicArticleDao()}
)

// Add your custom methods and functionality below.
