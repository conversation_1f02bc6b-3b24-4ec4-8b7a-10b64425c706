// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// surahReadCollectDao is the data access object for the table surah_read_collect.
// You can define custom methods on it to extend its functionality as needed.
type surahReadCollectDao struct {
	*internal.SurahReadCollectDao
}

var (
	// SurahReadCollect is a globally accessible object for table surah_read_collect operations.
	SurahReadCollect = surahReadCollectDao{internal.NewSurahReadCollectDao()}
)

// Add your custom methods and functionality below.
