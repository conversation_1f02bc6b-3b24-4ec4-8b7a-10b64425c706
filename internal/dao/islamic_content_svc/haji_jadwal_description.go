// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// hajiJadwalDescriptionDao is the data access object for the table haji_jadwal_description.
// You can define custom methods on it to extend its functionality as needed.
type hajiJadwalDescriptionDao struct {
	*internal.HajiJadwalDescriptionDao
}

var (
	// HajiJadwalDescription is a globally accessible object for table haji_jadwal_description operations.
	HajiJadwalDescription = hajiJadwalDescriptionDao{internal.NewHajiJadwalDescriptionDao()}
)

// Add your custom methods and functionality below.
