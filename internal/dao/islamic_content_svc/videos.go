// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// videosDao is the data access object for the table videos.
// You can define custom methods on it to extend its functionality as needed.
type videosDao struct {
	*internal.VideosDao
}

var (
	// Videos is a globally accessible object for table videos operations.
	Videos = videosDao{internal.NewVideosDao()}
)

// Add your custom methods and functionality below.
