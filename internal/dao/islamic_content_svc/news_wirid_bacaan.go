// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// newsWiridBacaanDao is the data access object for the table news_wirid_bacaan.
// You can define custom methods on it to extend its functionality as needed.
type newsWiridBacaanDao struct {
	*internal.NewsWiridBacaanDao
}

var (
	// NewsWiridBacaan is a globally accessible object for table news_wirid_bacaan operations.
	NewsWiridBacaan = newsWiridBacaanDao{internal.NewNewsWiridBacaanDao()}
)

// Add your custom methods and functionality below.
