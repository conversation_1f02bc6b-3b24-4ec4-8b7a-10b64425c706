// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// hajiDoaRingkasDao is the data access object for the table haji_doa_ringkas.
// You can define custom methods on it to extend its functionality as needed.
type hajiDoaRingkasDao struct {
	*internal.HajiDoaRingkasDao
}

var (
	// HajiDoaRingkas is a globally accessible object for table haji_doa_ringkas operations.
	HajiDoaRingkas = hajiDoaRingkasDao{internal.NewHajiDoaRingkasDao()}
)

// Add your custom methods and functionality below.
