// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// hajiLandmarkDao is the data access object for the table haji_landmark.
// You can define custom methods on it to extend its functionality as needed.
type hajiLandmarkDao struct {
	*internal.HajiLandmarkDao
}

var (
	// HajiLandmark is a globally accessible object for table haji_landmark operations.
	HajiLandmark = hajiLandmarkDao{internal.NewHajiLandmarkDao()}
)

// Add your custom methods and functionality below.
