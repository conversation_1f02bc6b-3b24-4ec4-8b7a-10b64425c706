// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// hajiHikmahLanguagesDao is the data access object for the table haji_hikmah_languages.
// You can define custom methods on it to extend its functionality as needed.
type hajiHikmahLanguagesDao struct {
	*internal.HajiHikmahLanguagesDao
}

var (
	// HajiHikmahLanguages is a globally accessible object for table haji_hikmah_languages operations.
	HajiHikmahLanguages = hajiHikmahLanguagesDao{internal.NewHajiHikmahLanguagesDao()}
)

// Add your custom methods and functionality below.
