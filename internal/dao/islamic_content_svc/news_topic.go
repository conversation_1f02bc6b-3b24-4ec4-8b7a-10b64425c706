// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// newsTopicDao is the data access object for the table news_topic.
// You can define custom methods on it to extend its functionality as needed.
type newsTopicDao struct {
	*internal.NewsTopicDao
}

var (
	// NewsTopic is a globally accessible object for table news_topic operations.
	NewsTopic = newsTopicDao{internal.NewNewsTopicDao()}
)

// Add your custom methods and functionality below.
