// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// newsCategoryDao is the data access object for the table news_category.
// You can define custom methods on it to extend its functionality as needed.
type newsCategoryDao struct {
	*internal.NewsCategoryDao
}

var (
	// NewsCategory is a globally accessible object for table news_category operations.
	NewsCategory = newsCategoryDao{internal.NewNewsCategoryDao()}
)

// Add your custom methods and functionality below.
