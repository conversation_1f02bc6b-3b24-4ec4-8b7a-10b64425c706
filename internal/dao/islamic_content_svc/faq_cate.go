// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// faqCateDao is the data access object for the table faq_cate.
// You can define custom methods on it to extend its functionality as needed.
type faqCateDao struct {
	*internal.FaqCateDao
}

var (
	// FaqCate is a globally accessible object for table faq_cate operations.
	FaqCate = faqCateDao{internal.NewFaqCateDao()}
)

// Add your custom methods and functionality below.
