// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// hajiLandmarkTypeDao is the data access object for the table haji_landmark_type.
// You can define custom methods on it to extend its functionality as needed.
type hajiLandmarkTypeDao struct {
	*internal.HajiLandmarkTypeDao
}

var (
	// HajiLandmarkType is a globally accessible object for table haji_landmark_type operations.
	HajiLandmarkType = hajiLandmarkTypeDao{internal.NewHajiLandmarkTypeDao()}
)

// Add your custom methods and functionality below.
