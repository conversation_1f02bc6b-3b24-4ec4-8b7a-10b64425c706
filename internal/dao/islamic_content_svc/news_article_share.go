// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// newsArticleShareDao is the data access object for the table news_article_share.
// You can define custom methods on it to extend its functionality as needed.
type newsArticleShareDao struct {
	*internal.NewsArticleShareDao
}

var (
	// NewsArticleShare is a globally accessible object for table news_article_share operations.
	NewsArticleShare = newsArticleShareDao{internal.NewNewsArticleShareDao()}
)

// Add your custom methods and functionality below.
