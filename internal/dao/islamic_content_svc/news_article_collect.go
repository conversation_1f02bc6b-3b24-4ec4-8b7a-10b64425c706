// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// newsArticleCollectDao is the data access object for the table news_article_collect.
// You can define custom methods on it to extend its functionality as needed.
type newsArticleCollectDao struct {
	*internal.NewsArticleCollectDao
}

var (
	// NewsArticleCollect is a globally accessible object for table news_article_collect operations.
	NewsArticleCollect = newsArticleCollectDao{internal.NewNewsArticleCollectDao()}
)

// Add your custom methods and functionality below.
