// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// VideoCategoryLanguagesDao is the data access object for the table video_category_languages.
type VideoCategoryLanguagesDao struct {
	table    string                        // table is the underlying table name of the DAO.
	group    string                        // group is the database configuration group name of the current DAO.
	columns  VideoCategoryLanguagesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler            // handlers for customized model modification.
}

// VideoCategoryLanguagesColumns defines and stores column names for the table video_category_languages.
type VideoCategoryLanguagesColumns struct {
	Id          string // 主键ID
	CategoryId  string // 分类ID
	LanguageId  string // 语言ID：0-中文，1-英文，2-印尼语
	Name        string // 分类名称
	Description string // 分类描述
	CreateTime  string // 创建时间(毫秒时间戳)
	UpdateTime  string // 更新时间(毫秒时间戳)
	DeleteTime  string // 删除时间
}

// videoCategoryLanguagesColumns holds the columns for the table video_category_languages.
var videoCategoryLanguagesColumns = VideoCategoryLanguagesColumns{
	Id:          "id",
	CategoryId:  "category_id",
	LanguageId:  "language_id",
	Name:        "name",
	Description: "description",
	CreateTime:  "create_time",
	UpdateTime:  "update_time",
	DeleteTime:  "delete_time",
}

// NewVideoCategoryLanguagesDao creates and returns a new DAO object for table data access.
func NewVideoCategoryLanguagesDao(handlers ...gdb.ModelHandler) *VideoCategoryLanguagesDao {
	return &VideoCategoryLanguagesDao{
		group:    "islamic_content_svc",
		table:    "video_category_languages",
		columns:  videoCategoryLanguagesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *VideoCategoryLanguagesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *VideoCategoryLanguagesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *VideoCategoryLanguagesDao) Columns() VideoCategoryLanguagesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *VideoCategoryLanguagesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *VideoCategoryLanguagesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *VideoCategoryLanguagesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
