// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HajiLandmarkTypeLanguagesDao is the data access object for the table haji_landmark_type_languages.
type HajiLandmarkTypeLanguagesDao struct {
	table    string                           // table is the underlying table name of the DAO.
	group    string                           // group is the database configuration group name of the current DAO.
	columns  HajiLandmarkTypeLanguagesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler               // handlers for customized model modification.
}

// HajiLandmarkTypeLanguagesColumns defines and stores column names for the table haji_landmark_type_languages.
type HajiLandmarkTypeLanguagesColumns struct {
	Id         string // 主键ID
	TypeId     string // 地标类型ID，关联haji_landmark_type.id
	LanguageId string // 语言ID：0-中文，1-英文，2-印尼语
	TypeName   string // 类型名称
	CreateTime string // 创建时间（毫秒时间戳）
	UpdateTime string // 更新时间（毫秒时间戳）
}

// hajiLandmarkTypeLanguagesColumns holds the columns for the table haji_landmark_type_languages.
var hajiLandmarkTypeLanguagesColumns = HajiLandmarkTypeLanguagesColumns{
	Id:         "id",
	TypeId:     "type_id",
	LanguageId: "language_id",
	TypeName:   "type_name",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewHajiLandmarkTypeLanguagesDao creates and returns a new DAO object for table data access.
func NewHajiLandmarkTypeLanguagesDao(handlers ...gdb.ModelHandler) *HajiLandmarkTypeLanguagesDao {
	return &HajiLandmarkTypeLanguagesDao{
		group:    "islamic_content_svc",
		table:    "haji_landmark_type_languages",
		columns:  hajiLandmarkTypeLanguagesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HajiLandmarkTypeLanguagesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HajiLandmarkTypeLanguagesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HajiLandmarkTypeLanguagesDao) Columns() HajiLandmarkTypeLanguagesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HajiLandmarkTypeLanguagesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HajiLandmarkTypeLanguagesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HajiLandmarkTypeLanguagesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
