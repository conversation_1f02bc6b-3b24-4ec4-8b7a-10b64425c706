// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// VideosDao is the data access object for the table videos.
type VideosDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  VideosColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// VideosColumns defines and stores column names for the table videos.
type VideosColumns struct {
	Id               string // 主键ID
	CategoryId       string // 分类ID
	VideoUrl         string // 视频文件URL
	VideoSize        string // 视频文件大小(字节)
	VideoDuration    string // 视频时长(秒)
	VideoFormat      string // 视频格式：mp4, mov等
	VideoCoverUrl    string // 视频封面图片URL
	ViewCount        string // 播放次数
	ShareCount       string // 分享次数
	CollectCount     string // 收藏次数
	CreatorName      string // 创建者姓名
	Author           string // 视频作者
	AuthorLogo       string // 作者头像URL
	AuthorAuthStatus string // 作者认证状态：0-未认证，1-已认证
	PublishState     string // 发布状态：0-待发布，1-已发布，2-已下线
	IsRecommended    string // 是否推荐，0-否，1-是
	IsDraft          string // 是否草稿状态，1是，0否
	CreateTime       string // 创建时间(毫秒时间戳)
	PublishTime      string // 发布时间(毫秒时间戳)
	UpdateTime       string // 更新时间(毫秒时间戳)
	DeleteTime       string // 删除时间(毫秒时间戳)
}

// videosColumns holds the columns for the table videos.
var videosColumns = VideosColumns{
	Id:               "id",
	CategoryId:       "category_id",
	VideoUrl:         "video_url",
	VideoSize:        "video_size",
	VideoDuration:    "video_duration",
	VideoFormat:      "video_format",
	VideoCoverUrl:    "video_cover_url",
	ViewCount:        "view_count",
	ShareCount:       "share_count",
	CollectCount:     "collect_count",
	CreatorName:      "creator_name",
	Author:           "author",
	AuthorLogo:       "author_logo",
	AuthorAuthStatus: "author_auth_status",
	PublishState:     "publish_state",
	IsRecommended:    "is_recommended",
	IsDraft:          "is_draft",
	CreateTime:       "create_time",
	PublishTime:      "publish_time",
	UpdateTime:       "update_time",
	DeleteTime:       "delete_time",
}

// NewVideosDao creates and returns a new DAO object for table data access.
func NewVideosDao(handlers ...gdb.ModelHandler) *VideosDao {
	return &VideosDao{
		group:    "islamic_content_svc",
		table:    "videos",
		columns:  videosColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *VideosDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *VideosDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *VideosDao) Columns() VideosColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *VideosDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *VideosDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *VideosDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
