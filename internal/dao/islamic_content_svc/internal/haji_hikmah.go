// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HajiHikmahDao is the data access object for the table haji_hikmah.
type HajiHikmahDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  HajiHikmahColumns  // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// HajiHikmahColumns defines and stores column names for the table haji_hikmah.
type HajiHikmahColumns struct {
	Id         string // 主键ID
	ArticleId  string // 文章ID，关联news_article.id
	SortOrder  string // 排序值，数字越小排序越靠前
	CreateTime string // 创建时间（毫秒时间戳）
	UpdateTime string // 更新时间（毫秒时间戳）
}

// hajiHikmahColumns holds the columns for the table haji_hikmah.
var hajiHikmahColumns = HajiHikmahColumns{
	Id:         "id",
	ArticleId:  "article_id",
	SortOrder:  "sort_order",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewHajiHikmahDao creates and returns a new DAO object for table data access.
func NewHajiHikmahDao(handlers ...gdb.ModelHandler) *HajiHikmahDao {
	return &HajiHikmahDao{
		group:    "islamic_content_svc",
		table:    "haji_hikmah",
		columns:  hajiHikmahColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HajiHikmahDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HajiHikmahDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HajiHikmahDao) Columns() HajiHikmahColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HajiHikmahDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HajiHikmahDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HajiHikmahDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
