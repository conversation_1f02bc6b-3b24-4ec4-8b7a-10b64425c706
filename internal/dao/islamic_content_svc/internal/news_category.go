// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsCategoryDao is the data access object for the table news_category.
type NewsCategoryDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  NewsCategoryColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// NewsCategoryColumns defines and stores column names for the table news_category.
type NewsCategoryColumns struct {
	Id         string //
	ParentId   string // 上级id，0表示顶级
	IsZh       string // 是否中文，0-否，1-是
	IsEn       string // 是否英文，0-否，1-是
	IsId       string // 是否印尼文，0-否，1-是
	Status     string // 状态，1启用，0关闭
	Sort       string // 排序，数字越小，排序越靠前
	AdminId    string // 分类负责人id
	CoverImgs  string // 封面图
	Remark     string // 备注
	Creater    string // 创建者id
	CreateName string // 创建者
	CreateTime string // 创建时间
	UpdateTime string // 修改时间
	DeleteTime string // 删除时间
	IsAppShow  string // 是否app展示，1：是 0：否
}

// newsCategoryColumns holds the columns for the table news_category.
var newsCategoryColumns = NewsCategoryColumns{
	Id:         "id",
	ParentId:   "parent_id",
	IsZh:       "is_zh",
	IsEn:       "is_en",
	IsId:       "is_id",
	Status:     "status",
	Sort:       "sort",
	AdminId:    "admin_id",
	CoverImgs:  "cover_imgs",
	Remark:     "remark",
	Creater:    "creater",
	CreateName: "create_name",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	DeleteTime: "delete_time",
	IsAppShow:  "is_app_show",
}

// NewNewsCategoryDao creates and returns a new DAO object for table data access.
func NewNewsCategoryDao(handlers ...gdb.ModelHandler) *NewsCategoryDao {
	return &NewsCategoryDao{
		group:    "islamic_content_svc",
		table:    "news_category",
		columns:  newsCategoryColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *NewsCategoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *NewsCategoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *NewsCategoryDao) Columns() NewsCategoryColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *NewsCategoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *NewsCategoryDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *NewsCategoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
