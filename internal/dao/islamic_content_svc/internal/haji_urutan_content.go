// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HajiUrutanContentDao is the data access object for the table haji_urutan_content.
type HajiUrutanContentDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  HajiUrutanContentColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// HajiUrutanContentColumns defines and stores column names for the table haji_urutan_content.
type HajiUrutanContentColumns struct {
	Id            string // 主键ID
	UrutanId      string // 朝觐顺序ID，关联haji_urutan.id
	LanguageId    string // 语言ID: 0-中文, 1-英文, 2-印尼语
	UrutanName    string // 朝觐仪式名称（最多60个字符）
	UrutanTime    string // 仪式时间
	UrutanContent string // 仪式内容描述（富文本）
	CreateTime    string // 创建时间（毫秒时间戳）
	UpdateTime    string // 更新时间（毫秒时间戳）
}

// hajiUrutanContentColumns holds the columns for the table haji_urutan_content.
var hajiUrutanContentColumns = HajiUrutanContentColumns{
	Id:            "id",
	UrutanId:      "urutan_id",
	LanguageId:    "language_id",
	UrutanName:    "urutan_name",
	UrutanTime:    "urutan_time",
	UrutanContent: "urutan_content",
	CreateTime:    "create_time",
	UpdateTime:    "update_time",
}

// NewHajiUrutanContentDao creates and returns a new DAO object for table data access.
func NewHajiUrutanContentDao(handlers ...gdb.ModelHandler) *HajiUrutanContentDao {
	return &HajiUrutanContentDao{
		group:    "islamic_content_svc",
		table:    "haji_urutan_content",
		columns:  hajiUrutanContentColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HajiUrutanContentDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HajiUrutanContentDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HajiUrutanContentDao) Columns() HajiUrutanContentColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HajiUrutanContentDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HajiUrutanContentDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HajiUrutanContentDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
