// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// VideoPlaylistsDao is the data access object for the table video_playlists.
type VideoPlaylistsDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  VideoPlaylistsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// VideoPlaylistsColumns defines and stores column names for the table video_playlists.
type VideoPlaylistsColumns struct {
	Id           string // 主键ID
	CoverUrl     string // 专题封面图片链接
	IsVisible    string // 是否显示，0-隐藏，1-显示
	SortOrder    string // 排序权重，数字越小越靠前
	VideoCount   string // 播放列表下视频数量
	ViewCount    string // 播放列表浏览次数
	ShareCount   string // 播放列表分享次数
	CollectCount string // 播放列表收藏次数
	CreateTime   string // 创建时间(毫秒时间戳)
	UpdateTime   string // 更新时间(毫秒时间戳)
	Creater      string // 创建者id
	CreateName   string // 创建者
	DeleteTime   string // 删除时间
}

// videoPlaylistsColumns holds the columns for the table video_playlists.
var videoPlaylistsColumns = VideoPlaylistsColumns{
	Id:           "id",
	CoverUrl:     "cover_url",
	IsVisible:    "is_visible",
	SortOrder:    "sort_order",
	VideoCount:   "video_count",
	ViewCount:    "view_count",
	ShareCount:   "share_count",
	CollectCount: "collect_count",
	CreateTime:   "create_time",
	UpdateTime:   "update_time",
	Creater:      "creater",
	CreateName:   "create_name",
	DeleteTime:   "delete_time",
}

// NewVideoPlaylistsDao creates and returns a new DAO object for table data access.
func NewVideoPlaylistsDao(handlers ...gdb.ModelHandler) *VideoPlaylistsDao {
	return &VideoPlaylistsDao{
		group:    "islamic_content_svc",
		table:    "video_playlists",
		columns:  videoPlaylistsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *VideoPlaylistsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *VideoPlaylistsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *VideoPlaylistsDao) Columns() VideoPlaylistsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *VideoPlaylistsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *VideoPlaylistsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *VideoPlaylistsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
