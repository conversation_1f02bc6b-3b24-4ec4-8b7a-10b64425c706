// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HajiLandmarkDao is the data access object for the table haji_landmark.
type HajiLandmarkDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  HajiLandmarkColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// HajiLandmarkColumns defines and stores column names for the table haji_landmark.
type HajiLandmarkColumns struct {
	Id         string // 主键ID
	TypeId     string // 地标类型ID，关联haji_landmark_type.id
	InnerType  string // 内部类型: (destinasi, tokoh)
	Latitude   string // 纬度
	Longitude  string // 经度
	ImageUrl   string // 图片URL
	SortOrder  string // 排序值，数字越小排序越靠前
	CreateTime string // 创建时间（毫秒时间戳）
	UpdateTime string // 更新时间（毫秒时间戳）
}

// hajiLandmarkColumns holds the columns for the table haji_landmark.
var hajiLandmarkColumns = HajiLandmarkColumns{
	Id:         "id",
	TypeId:     "type_id",
	InnerType:  "inner_type",
	Latitude:   "latitude",
	Longitude:  "longitude",
	ImageUrl:   "image_url",
	SortOrder:  "sort_order",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewHajiLandmarkDao creates and returns a new DAO object for table data access.
func NewHajiLandmarkDao(handlers ...gdb.ModelHandler) *HajiLandmarkDao {
	return &HajiLandmarkDao{
		group:    "islamic_content_svc",
		table:    "haji_landmark",
		columns:  hajiLandmarkColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HajiLandmarkDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HajiLandmarkDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HajiLandmarkDao) Columns() HajiLandmarkColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HajiLandmarkDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HajiLandmarkDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HajiLandmarkDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
