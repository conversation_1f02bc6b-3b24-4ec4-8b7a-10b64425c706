// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DoaReadCollectDao is the data access object for the table doa_read_collect.
type DoaReadCollectDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  DoaReadCollectColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// DoaReadCollectColumns defines and stores column names for the table doa_read_collect.
type DoaReadCollectColumns struct {
	Id         string //
	UserId     string // 用户id
	Types      string // 类型 1-doa,2-wirid
	PId        string // 父级id
	BaccanId   string // baccan_id
	PName      string // 父级名称
	BaccanName string // 名称
	CreateTime string // 创建时间（注册时间）
	UpdateTime string // 更新时间，0代表创建后未更新
}

// doaReadCollectColumns holds the columns for the table doa_read_collect.
var doaReadCollectColumns = DoaReadCollectColumns{
	Id:         "id",
	UserId:     "user_id",
	Types:      "types",
	PId:        "p_id",
	BaccanId:   "baccan_id",
	PName:      "p_name",
	BaccanName: "baccan_name",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewDoaReadCollectDao creates and returns a new DAO object for table data access.
func NewDoaReadCollectDao(handlers ...gdb.ModelHandler) *DoaReadCollectDao {
	return &DoaReadCollectDao{
		group:    "islamic_content_svc",
		table:    "doa_read_collect",
		columns:  doaReadCollectColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DoaReadCollectDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DoaReadCollectDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DoaReadCollectDao) Columns() DoaReadCollectColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DoaReadCollectDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DoaReadCollectDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DoaReadCollectDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
