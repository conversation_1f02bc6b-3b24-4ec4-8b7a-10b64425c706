// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WisdomDao is the data access object for the table wisdom.
type WisdomDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  WisdomColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// WisdomColumns defines and stores column names for the table wisdom.
type WisdomColumns struct {
	Id            string //
	WisdomCateId  string //
	IsOpen        string // 状态 [ 1 启用  2 禁用]
	Sort          string // 排序
	Views         string // 浏览量
	ArticleId     string // 文章id
	CreateAccount string // 创建者
	UpdateAccount string // 更新者
	CreateTime    string //
	UpdateTime    string //
	DeleteTime    string //
}

// wisdomColumns holds the columns for the table wisdom.
var wisdomColumns = WisdomColumns{
	Id:            "id",
	WisdomCateId:  "wisdom_cate_id",
	IsOpen:        "is_open",
	Sort:          "sort",
	Views:         "views",
	ArticleId:     "article_id",
	CreateAccount: "create_account",
	UpdateAccount: "update_account",
	CreateTime:    "create_time",
	UpdateTime:    "update_time",
	DeleteTime:    "delete_time",
}

// NewWisdomDao creates and returns a new DAO object for table data access.
func NewWisdomDao(handlers ...gdb.ModelHandler) *WisdomDao {
	return &WisdomDao{
		group:    "islamic_content_svc",
		table:    "wisdom",
		columns:  wisdomColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *WisdomDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *WisdomDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *WisdomDao) Columns() WisdomColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *WisdomDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *WisdomDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *WisdomDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
