// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WisdomLanguageDao is the data access object for the table wisdom_language.
type WisdomLanguageDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  WisdomLanguageColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// WisdomLanguageColumns defines and stores column names for the table wisdom_language.
type WisdomLanguageColumns struct {
	Id         string //
	WisdomId   string //
	LanguageId string // 语言
	Title      string // 名言标题
	ImageUrl   string // 名言图片url
	Desc       string // 详情
}

// wisdomLanguageColumns holds the columns for the table wisdom_language.
var wisdomLanguageColumns = WisdomLanguageColumns{
	Id:         "id",
	WisdomId:   "wisdom_id",
	LanguageId: "language_id",
	Title:      "title",
	ImageUrl:   "image_url",
	Desc:       "desc",
}

// NewWisdomLanguageDao creates and returns a new DAO object for table data access.
func NewWisdomLanguageDao(handlers ...gdb.ModelHandler) *WisdomLanguageDao {
	return &WisdomLanguageDao{
		group:    "islamic_content_svc",
		table:    "wisdom_language",
		columns:  wisdomLanguageColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *WisdomLanguageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *WisdomLanguageDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *WisdomLanguageDao) Columns() WisdomLanguageColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *WisdomLanguageDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *WisdomLanguageDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *WisdomLanguageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
