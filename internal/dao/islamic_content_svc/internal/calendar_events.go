// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CalendarEventsDao is the data access object for the table calendar_events.
type CalendarEventsDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  CalendarEventsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// CalendarEventsColumns defines and stores column names for the table calendar_events.
type CalendarEventsColumns struct {
	Id             string // 主键ID
	EventType      string // 事件类型：HARI_BESAR-重大节日，LIBUR_NASIONAL-国定假日，PUASA-斋戒
	Title          string // 事件标题
	Description    string // 事件描述
	GregorianYear  string // 公历年
	GregorianMonth string // 公历月
	GregorianDay   string // 公历日
	JumpUrl        string // 点击跳转链接
	DataSource     string // 数据来源：MANUAL-人工录入，CRAWLER-爬虫获取
	IsActive       string // 是否启用：0-禁用，1-启用
	CreateTime     string // 创建时间(毫秒时间戳)
	UpdateTime     string // 更新时间(毫秒时间戳)
}

// calendarEventsColumns holds the columns for the table calendar_events.
var calendarEventsColumns = CalendarEventsColumns{
	Id:             "id",
	EventType:      "event_type",
	Title:          "title",
	Description:    "description",
	GregorianYear:  "gregorian_year",
	GregorianMonth: "gregorian_month",
	GregorianDay:   "gregorian_day",
	JumpUrl:        "jump_url",
	DataSource:     "data_source",
	IsActive:       "is_active",
	CreateTime:     "create_time",
	UpdateTime:     "update_time",
}

// NewCalendarEventsDao creates and returns a new DAO object for table data access.
func NewCalendarEventsDao(handlers ...gdb.ModelHandler) *CalendarEventsDao {
	return &CalendarEventsDao{
		group:    "islamic_content_svc",
		table:    "calendar_events",
		columns:  calendarEventsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CalendarEventsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CalendarEventsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CalendarEventsDao) Columns() CalendarEventsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CalendarEventsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CalendarEventsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CalendarEventsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
