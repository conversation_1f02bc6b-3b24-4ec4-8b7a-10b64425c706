// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsTopicLanguageDao is the data access object for the table news_topic_language.
type NewsTopicLanguageDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  NewsTopicLanguageColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// NewsTopicLanguageColumns defines and stores column names for the table news_topic_language.
type NewsTopicLanguageColumns struct {
	Id         string //
	TopicId    string // 专题id
	LanguageId string // 语言id,0-中文，1-英文，2-印尼语
	Name       string // 名称
	ShortName  string // 名称
	CreateTime string // 创建时间
	UpdateTime string // 修改时间
	DeleteTime string // 删除时间
}

// newsTopicLanguageColumns holds the columns for the table news_topic_language.
var newsTopicLanguageColumns = NewsTopicLanguageColumns{
	Id:         "id",
	TopicId:    "topic_id",
	LanguageId: "language_id",
	Name:       "name",
	ShortName:  "short_name",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	DeleteTime: "delete_time",
}

// NewNewsTopicLanguageDao creates and returns a new DAO object for table data access.
func NewNewsTopicLanguageDao(handlers ...gdb.ModelHandler) *NewsTopicLanguageDao {
	return &NewsTopicLanguageDao{
		group:    "islamic_content_svc",
		table:    "news_topic_language",
		columns:  newsTopicLanguageColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *NewsTopicLanguageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *NewsTopicLanguageDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *NewsTopicLanguageDao) Columns() NewsTopicLanguageColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *NewsTopicLanguageDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *NewsTopicLanguageDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *NewsTopicLanguageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
