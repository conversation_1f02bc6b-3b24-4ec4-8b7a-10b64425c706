// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// calendarEventsDao is the data access object for the table calendar_events.
// You can define custom methods on it to extend its functionality as needed.
type calendarEventsDao struct {
	*internal.CalendarEventsDao
}

var (
	// CalendarEvents is a globally accessible object for table calendar_events operations.
	CalendarEvents = calendarEventsDao{internal.NewCalendarEventsDao()}
)

// Add your custom methods and functionality below.
