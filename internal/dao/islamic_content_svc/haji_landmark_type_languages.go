// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package islamic_content_svc

import (
	"gtcms/internal/dao/islamic_content_svc/internal"
)

// hajiLandmarkTypeLanguagesDao is the data access object for the table haji_landmark_type_languages.
// You can define custom methods on it to extend its functionality as needed.
type hajiLandmarkTypeLanguagesDao struct {
	*internal.HajiLandmarkTypeLanguagesDao
}

var (
	// HajiLandmarkTypeLanguages is a globally accessible object for table haji_landmark_type_languages operations.
	HajiLandmarkTypeLanguages = hajiLandmarkTypeLanguagesDao{internal.NewHajiLandmarkTypeLanguagesDao()}
)

// Add your custom methods and functionality below.
