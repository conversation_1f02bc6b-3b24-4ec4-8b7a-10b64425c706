// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// cmsLoginConfigDao is the data access object for the table cms_login_config.
// You can define custom methods on it to extend its functionality as needed.
type cmsLoginConfigDao struct {
	*internal.CmsLoginConfigDao
}

var (
	// CmsLoginConfig is a globally accessible object for table cms_login_config operations.
	CmsLoginConfig = cmsLoginConfigDao{internal.NewCmsLoginConfigDao()}
)

// Add your custom methods and functionality below.
