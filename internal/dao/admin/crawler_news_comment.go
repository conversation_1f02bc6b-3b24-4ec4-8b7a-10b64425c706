// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// crawlerNewsCommentDao is the data access object for the table crawler_news_comment.
// You can define custom methods on it to extend its functionality as needed.
type crawlerNewsCommentDao struct {
	*internal.CrawlerNewsCommentDao
}

var (
	// CrawlerNewsComment is a globally accessible object for table crawler_news_comment operations.
	CrawlerNewsComment = crawlerNewsCommentDao{internal.NewCrawlerNewsCommentDao()}
)

// Add your custom methods and functionality below.
