// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// accountAuditLogDao is the data access object for the table account_audit_log.
// You can define custom methods on it to extend its functionality as needed.
type accountAuditLogDao struct {
	*internal.AccountAuditLogDao
}

var (
	// AccountAuditLog is a globally accessible object for table account_audit_log operations.
	AccountAuditLog = accountAuditLogDao{internal.NewAccountAuditLogDao()}
)

// Add your custom methods and functionality below.
