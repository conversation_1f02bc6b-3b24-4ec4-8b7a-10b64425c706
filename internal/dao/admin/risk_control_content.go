// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// riskControlContentDao is the data access object for the table risk_control_content.
// You can define custom methods on it to extend its functionality as needed.
type riskControlContentDao struct {
	*internal.RiskControlContentDao
}

var (
	// RiskControlContent is a globally accessible object for table risk_control_content operations.
	RiskControlContent = riskControlContentDao{internal.NewRiskControlContentDao()}
)

// Add your custom methods and functionality below.
