// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// riskControlTabDao is the data access object for the table risk_control_tab.
// You can define custom methods on it to extend its functionality as needed.
type riskControlTabDao struct {
	*internal.RiskControlTabDao
}

var (
	// RiskControlTab is a globally accessible object for table risk_control_tab operations.
	RiskControlTab = riskControlTabDao{internal.NewRiskControlTabDao()}
)

// Add your custom methods and functionality below.
