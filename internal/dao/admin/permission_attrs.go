// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// permissionAttrsDao is the data access object for the table permission_attrs.
// You can define custom methods on it to extend its functionality as needed.
type permissionAttrsDao struct {
	*internal.PermissionAttrsDao
}

var (
	// PermissionAttrs is a globally accessible object for table permission_attrs operations.
	PermissionAttrs = permissionAttrsDao{internal.NewPermissionAttrsDao()}
)

// Add your custom methods and functionality below.
