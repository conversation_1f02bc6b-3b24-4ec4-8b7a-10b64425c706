// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// accountNodeConfigDao is the data access object for the table account_node_config.
// You can define custom methods on it to extend its functionality as needed.
type accountNodeConfigDao struct {
	*internal.AccountNodeConfigDao
}

var (
	// AccountNodeConfig is a globally accessible object for table account_node_config operations.
	AccountNodeConfig = accountNodeConfigDao{internal.NewAccountNodeConfigDao()}
)

// Add your custom methods and functionality below.
