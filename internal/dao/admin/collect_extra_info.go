// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// collectExtraInfoDao is the data access object for the table collect_extra_info.
// You can define custom methods on it to extend its functionality as needed.
type collectExtraInfoDao struct {
	*internal.CollectExtraInfoDao
}

var (
	// CollectExtraInfo is a globally accessible object for table collect_extra_info operations.
	CollectExtraInfo = collectExtraInfoDao{internal.NewCollectExtraInfoDao()}
)

// Add your custom methods and functionality below.
