// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// attachmentDao is the data access object for the table attachment.
// You can define custom methods on it to extend its functionality as needed.
type attachmentDao struct {
	*internal.AttachmentDao
}

var (
	// Attachment is a globally accessible object for table attachment operations.
	Attachment = attachmentDao{internal.NewAttachmentDao()}
)

// Add your custom methods and functionality below.
