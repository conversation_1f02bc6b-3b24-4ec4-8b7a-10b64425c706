// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// collectTaskInstDao is the data access object for the table collect_task_inst.
// You can define custom methods on it to extend its functionality as needed.
type collectTaskInstDao struct {
	*internal.CollectTaskInstDao
}

var (
	// CollectTaskInst is a globally accessible object for table collect_task_inst operations.
	CollectTaskInst = collectTaskInstDao{internal.NewCollectTaskInstDao()}
)

// Add your custom methods and functionality below.
