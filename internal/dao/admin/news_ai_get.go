// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// newsAiGetDao is the data access object for the table news_ai_get.
// You can define custom methods on it to extend its functionality as needed.
type newsAiGetDao struct {
	*internal.NewsAiGetDao
}

var (
	// NewsAiGet is a globally accessible object for table news_ai_get operations.
	NewsAiGet = newsAiGetDao{internal.NewNewsAiGetDao()}
)

// Add your custom methods and functionality below.
