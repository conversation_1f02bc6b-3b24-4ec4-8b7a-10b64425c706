// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// signalClickRecordDao is the data access object for the table signal_click_record.
// You can define custom methods on it to extend its functionality as needed.
type signalClickRecordDao struct {
	*internal.SignalClickRecordDao
}

var (
	// SignalClickRecord is a globally accessible object for table signal_click_record operations.
	SignalClickRecord = signalClickRecordDao{internal.NewSignalClickRecordDao()}
)

// Add your custom methods and functionality below.
