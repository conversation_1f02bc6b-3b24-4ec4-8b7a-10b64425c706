// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// labelDao is the data access object for the table label.
// You can define custom methods on it to extend its functionality as needed.
type labelDao struct {
	*internal.LabelDao
}

var (
	// Label is a globally accessible object for table label operations.
	Label = labelDao{internal.NewLabelDao()}
)

// Add your custom methods and functionality below.
