// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// selectorConfigTabDao is the data access object for the table selector_config_tab.
// You can define custom methods on it to extend its functionality as needed.
type selectorConfigTabDao struct {
	*internal.SelectorConfigTabDao
}

var (
	// SelectorConfigTab is a globally accessible object for table selector_config_tab operations.
	SelectorConfigTab = selectorConfigTabDao{internal.NewSelectorConfigTabDao()}
)

// Add your custom methods and functionality below.
