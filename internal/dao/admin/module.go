// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// moduleDao is the data access object for the table module.
// You can define custom methods on it to extend its functionality as needed.
type moduleDao struct {
	*internal.ModuleDao
}

var (
	// Module is a globally accessible object for table module operations.
	Module = moduleDao{internal.NewModuleDao()}
)

// Add your custom methods and functionality below.
