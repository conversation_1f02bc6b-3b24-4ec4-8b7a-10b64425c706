// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// crawlerNewsRawDao is the data access object for the table crawler_news_raw.
// You can define custom methods on it to extend its functionality as needed.
type crawlerNewsRawDao struct {
	*internal.CrawlerNewsRawDao
}

var (
	// CrawlerNewsRaw is a globally accessible object for table crawler_news_raw operations.
	CrawlerNewsRaw = crawlerNewsRawDao{internal.NewCrawlerNewsRawDao()}
)

// Add your custom methods and functionality below.
