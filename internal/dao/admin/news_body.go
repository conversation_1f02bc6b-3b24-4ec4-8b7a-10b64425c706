// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// newsBodyDao is the data access object for the table news_body.
// You can define custom methods on it to extend its functionality as needed.
type newsBodyDao struct {
	*internal.NewsBodyDao
}

var (
	// NewsBody is a globally accessible object for table news_body operations.
	NewsBody = newsBodyDao{internal.NewNewsBodyDao()}
)

// Add your custom methods and functionality below.
