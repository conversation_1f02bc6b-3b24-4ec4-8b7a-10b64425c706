// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// attachmentCategoryDao is the data access object for the table attachment_category.
// You can define custom methods on it to extend its functionality as needed.
type attachmentCategoryDao struct {
	*internal.AttachmentCategoryDao
}

var (
	// AttachmentCategory is a globally accessible object for table attachment_category operations.
	AttachmentCategory = attachmentCategoryDao{internal.NewAttachmentCategoryDao()}
)

// Add your custom methods and functionality below.
