// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// localImageDao is the data access object for the table local_image.
// You can define custom methods on it to extend its functionality as needed.
type localImageDao struct {
	*internal.LocalImageDao
}

var (
	// LocalImage is a globally accessible object for table local_image operations.
	LocalImage = localImageDao{internal.NewLocalImageDao()}
)

// Add your custom methods and functionality below.
