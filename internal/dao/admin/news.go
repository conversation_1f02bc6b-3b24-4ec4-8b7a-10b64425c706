// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// newsDao is the data access object for the table news.
// You can define custom methods on it to extend its functionality as needed.
type newsDao struct {
	*internal.NewsDao
}

var (
	// News is a globally accessible object for table news operations.
	News = newsDao{internal.NewNewsDao()}
)

// Add your custom methods and functionality below.
