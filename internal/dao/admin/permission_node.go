// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// permissionNodeDao is the data access object for the table permission_node.
// You can define custom methods on it to extend its functionality as needed.
type permissionNodeDao struct {
	*internal.PermissionNodeDao
}

var (
	// PermissionNode is a globally accessible object for table permission_node operations.
	PermissionNode = permissionNodeDao{internal.NewPermissionNodeDao()}
)

// Add your custom methods and functionality below.
