// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// selectorConfigContentDao is the data access object for the table selector_config_content.
// You can define custom methods on it to extend its functionality as needed.
type selectorConfigContentDao struct {
	*internal.SelectorConfigContentDao
}

var (
	// SelectorConfigContent is a globally accessible object for table selector_config_content operations.
	SelectorConfigContent = selectorConfigContentDao{internal.NewSelectorConfigContentDao()}
)

// Add your custom methods and functionality below.
