// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// collectTaskDao is the data access object for the table collect_task.
// You can define custom methods on it to extend its functionality as needed.
type collectTaskDao struct {
	*internal.CollectTaskDao
}

var (
	// CollectTask is a globally accessible object for table collect_task operations.
	CollectTask = collectTaskDao{internal.NewCollectTaskDao()}
)

// Add your custom methods and functionality below.
