// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// tdkTmplDao is the data access object for the table tdk_tmpl.
// You can define custom methods on it to extend its functionality as needed.
type tdkTmplDao struct {
	*internal.TdkTmplDao
}

var (
	// TdkTmpl is a globally accessible object for table tdk_tmpl operations.
	TdkTmpl = tdkTmplDao{internal.NewTdkTmplDao()}
)

// Add your custom methods and functionality below.
