// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// sitemapDao is the data access object for the table sitemap.
// You can define custom methods on it to extend its functionality as needed.
type sitemapDao struct {
	*internal.SitemapDao
}

var (
	// Sitemap is a globally accessible object for table sitemap operations.
	Sitemap = sitemapDao{internal.NewSitemapDao()}
)

// Add your custom methods and functionality below.
