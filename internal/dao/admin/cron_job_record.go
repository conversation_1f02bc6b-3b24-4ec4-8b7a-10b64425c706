// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// cronJobRecordDao is the data access object for the table cron_job_record.
// You can define custom methods on it to extend its functionality as needed.
type cronJobRecordDao struct {
	*internal.CronJobRecordDao
}

var (
	// CronJobRecord is a globally accessible object for table cron_job_record operations.
	CronJobRecord = cronJobRecordDao{internal.NewCronJobRecordDao()}
)

// Add your custom methods and functionality below.
