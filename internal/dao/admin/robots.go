// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// robotsDao is the data access object for the table robots.
// You can define custom methods on it to extend its functionality as needed.
type robotsDao struct {
	*internal.RobotsDao
}

var (
	// Robots is a globally accessible object for table robots operations.
	Robots = robotsDao{internal.NewRobotsDao()}
)

// Add your custom methods and functionality below.
