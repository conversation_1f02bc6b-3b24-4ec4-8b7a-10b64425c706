// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// storeCloudConfigDao is the data access object for the table store_cloud_config.
// You can define custom methods on it to extend its functionality as needed.
type storeCloudConfigDao struct {
	*internal.StoreCloudConfigDao
}

var (
	// StoreCloudConfig is a globally accessible object for table store_cloud_config operations.
	StoreCloudConfig = storeCloudConfigDao{internal.NewStoreCloudConfigDao()}
)

// Add your custom methods and functionality below.
