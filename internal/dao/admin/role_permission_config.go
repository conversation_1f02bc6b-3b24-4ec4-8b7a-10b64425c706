// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// rolePermissionConfigDao is the data access object for the table role_permission_config.
// You can define custom methods on it to extend its functionality as needed.
type rolePermissionConfigDao struct {
	*internal.RolePermissionConfigDao
}

var (
	// RolePermissionConfig is a globally accessible object for table role_permission_config operations.
	RolePermissionConfig = rolePermissionConfigDao{internal.NewRolePermissionConfigDao()}
)

// Add your custom methods and functionality below.
