// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// sensitiveDao is the data access object for the table sensitive.
// You can define custom methods on it to extend its functionality as needed.
type sensitiveDao struct {
	*internal.SensitiveDao
}

var (
	// Sensitive is a globally accessible object for table sensitive operations.
	Sensitive = sensitiveDao{internal.NewSensitiveDao()}
)

// Add your custom methods and functionality below.
