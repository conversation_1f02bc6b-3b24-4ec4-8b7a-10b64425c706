// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// cmsLoginRiskDao is the data access object for the table cms_login_risk.
// You can define custom methods on it to extend its functionality as needed.
type cmsLoginRiskDao struct {
	*internal.CmsLoginRiskDao
}

var (
	// CmsLoginRisk is a globally accessible object for table cms_login_risk operations.
	CmsLoginRisk = cmsLoginRiskDao{internal.NewCmsLoginRiskDao()}
)

// Add your custom methods and functionality below.
