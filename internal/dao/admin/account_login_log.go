// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// accountLoginLogDao is the data access object for the table account_login_log.
// You can define custom methods on it to extend its functionality as needed.
type accountLoginLogDao struct {
	*internal.AccountLoginLogDao
}

var (
	// AccountLoginLog is a globally accessible object for table account_login_log operations.
	AccountLoginLog = accountLoginLogDao{internal.NewAccountLoginLogDao()}
)

// Add your custom methods and functionality below.
