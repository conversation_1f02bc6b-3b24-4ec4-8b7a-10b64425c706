// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// collectMultiLanguageDao is the data access object for the table collect_multi_language.
// You can define custom methods on it to extend its functionality as needed.
type collectMultiLanguageDao struct {
	*internal.CollectMultiLanguageDao
}

var (
	// CollectMultiLanguage is a globally accessible object for table collect_multi_language operations.
	CollectMultiLanguage = collectMultiLanguageDao{internal.NewCollectMultiLanguageDao()}
)

// Add your custom methods and functionality below.
