// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// singlePageDao is the data access object for the table single_page.
// You can define custom methods on it to extend its functionality as needed.
type singlePageDao struct {
	*internal.SinglePageDao
}

var (
	// SinglePage is a globally accessible object for table single_page operations.
	SinglePage = singlePageDao{internal.NewSinglePageDao()}
)

// Add your custom methods and functionality below.
