// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// crawlerNewsRawContentDao is the data access object for the table crawler_news_raw_content.
// You can define custom methods on it to extend its functionality as needed.
type crawlerNewsRawContentDao struct {
	*internal.CrawlerNewsRawContentDao
}

var (
	// CrawlerNewsRawContent is a globally accessible object for table crawler_news_raw_content operations.
	CrawlerNewsRawContent = crawlerNewsRawContentDao{internal.NewCrawlerNewsRawContentDao()}
)

// Add your custom methods and functionality below.
