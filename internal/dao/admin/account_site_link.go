// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// accountSiteLinkDao is the data access object for the table account_site_link.
// You can define custom methods on it to extend its functionality as needed.
type accountSiteLinkDao struct {
	*internal.AccountSiteLinkDao
}

var (
	// AccountSiteLink is a globally accessible object for table account_site_link operations.
	AccountSiteLink = accountSiteLinkDao{internal.NewAccountSiteLinkDao()}
)

// Add your custom methods and functionality below.
