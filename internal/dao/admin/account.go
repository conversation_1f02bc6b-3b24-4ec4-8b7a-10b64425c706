// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// accountDao is the data access object for the table account.
// You can define custom methods on it to extend its functionality as needed.
type accountDao struct {
	*internal.AccountDao
}

var (
	// Account is a globally accessible object for table account operations.
	Account = accountDao{internal.NewAccountDao()}
)

// Add your custom methods and functionality below.
