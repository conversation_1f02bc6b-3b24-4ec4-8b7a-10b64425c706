// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// cronJobDao is the data access object for the table cron_job.
// You can define custom methods on it to extend its functionality as needed.
type cronJobDao struct {
	*internal.CronJobDao
}

var (
	// CronJob is a globally accessible object for table cron_job operations.
	CronJob = cronJobDao{internal.NewCronJobDao()}
)

// Add your custom methods and functionality below.
