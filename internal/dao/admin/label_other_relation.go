// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// labelOtherRelationDao is the data access object for the table label_other_relation.
// You can define custom methods on it to extend its functionality as needed.
type labelOtherRelationDao struct {
	*internal.LabelOtherRelationDao
}

var (
	// LabelOtherRelation is a globally accessible object for table label_other_relation operations.
	LabelOtherRelation = labelOtherRelationDao{internal.NewLabelOtherRelationDao()}
)

// Add your custom methods and functionality below.
