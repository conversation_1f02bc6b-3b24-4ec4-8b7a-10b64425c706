// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// keywordDao is the data access object for the table keyword.
// You can define custom methods on it to extend its functionality as needed.
type keywordDao struct {
	*internal.KeywordDao
}

var (
	// Keyword is a globally accessible object for table keyword operations.
	Keyword = keywordDao{internal.NewKeywordDao()}
)

// Add your custom methods and functionality below.
