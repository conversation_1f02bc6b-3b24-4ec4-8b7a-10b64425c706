// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AttachmentCategoryDao is the data access object for the table attachment_category.
type AttachmentCategoryDao struct {
	table    string                    // table is the underlying table name of the DAO.
	group    string                    // group is the database configuration group name of the current DAO.
	columns  AttachmentCategoryColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler        // handlers for customized model modification.
}

// AttachmentCategoryColumns defines and stores column names for the table attachment_category.
type AttachmentCategoryColumns struct {
	Id            string //
	Pid           string // 父分类
	Type          string // 类型[1  图片 2视频]
	Name          string // 名称
	OrderBy       string // 排序值(值大排前)
	CreateTime    string // 创建时间
	CreateAccount string // 创建者
	UpdateTime    string // 更新时间
	UpdateAccount string // 更新者
	DeleteTime    string // 删除时间
}

// attachmentCategoryColumns holds the columns for the table attachment_category.
var attachmentCategoryColumns = AttachmentCategoryColumns{
	Id:            "id",
	Pid:           "pid",
	Type:          "type",
	Name:          "name",
	OrderBy:       "order_by",
	CreateTime:    "create_time",
	CreateAccount: "create_account",
	UpdateTime:    "update_time",
	UpdateAccount: "update_account",
	DeleteTime:    "delete_time",
}

// NewAttachmentCategoryDao creates and returns a new DAO object for table data access.
func NewAttachmentCategoryDao(handlers ...gdb.ModelHandler) *AttachmentCategoryDao {
	return &AttachmentCategoryDao{
		group:    "admin",
		table:    "attachment_category",
		columns:  attachmentCategoryColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AttachmentCategoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AttachmentCategoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AttachmentCategoryDao) Columns() AttachmentCategoryColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AttachmentCategoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AttachmentCategoryDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AttachmentCategoryDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
