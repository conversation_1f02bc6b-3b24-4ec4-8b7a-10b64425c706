// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// LocalImageDao is the data access object for the table local_image.
type LocalImageDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  LocalImageColumns  // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// LocalImageColumns defines and stores column names for the table local_image.
type LocalImageColumns struct {
	Table      string // 表名
	Id         string // 表中的id
	Column     string // 表中字段名
	CreateTime string //
	Url        string //
	OldUrl     string //
}

// localImageColumns holds the columns for the table local_image.
var localImageColumns = LocalImageColumns{
	Table:      "table",
	Id:         "id",
	Column:     "column",
	CreateTime: "create_time",
	Url:        "url",
	OldUrl:     "old_url",
}

// NewLocalImageDao creates and returns a new DAO object for table data access.
func NewLocalImageDao(handlers ...gdb.ModelHandler) *LocalImageDao {
	return &LocalImageDao{
		group:    "admin",
		table:    "local_image",
		columns:  localImageColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *LocalImageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *LocalImageDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *LocalImageDao) Columns() LocalImageColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *LocalImageDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *LocalImageDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *LocalImageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
