// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TdkTmplDao is the data access object for the table tdk_tmpl.
type TdkTmplDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  TdkTmplColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// TdkTmplColumns defines and stores column names for the table tdk_tmpl.
type TdkTmplColumns struct {
	Id               string //
	Name             string // 规则名称
	Belong           string // 所属(1:分组 2:站点)
	BelongId         string // 所属分组/站点id
	SelfId           string // 自身id(如果belong非0,那这个指向的是id)
	TRemark          string // 标题模版说明
	TConfig          string // 标题模版内容
	KwRemark         string // 关键词模版说明
	KwConfig         string // 关键词模版内容
	DescRemark       string // 描述模版说明
	DescConfig       string // 描述模版内容
	Status           string // 状态(1:启用 2:禁用)
	CreateTime       string // 创建时间
	UpdateTime       string // 修改时间
	DeleteTime       string // 删除时间
	Creater          string // 创建者
	IsDefault        string // 是否默认(1:是 2:否)
	Demo             string // 演示例子
	Language         string // 语言
	OtherLanguageIds string // 其它语言的tdk id
}

// tdkTmplColumns holds the columns for the table tdk_tmpl.
var tdkTmplColumns = TdkTmplColumns{
	Id:               "id",
	Name:             "name",
	Belong:           "belong",
	BelongId:         "belong_id",
	SelfId:           "self_id",
	TRemark:          "t_remark",
	TConfig:          "t_config",
	KwRemark:         "kw_remark",
	KwConfig:         "kw_config",
	DescRemark:       "desc_remark",
	DescConfig:       "desc_config",
	Status:           "status",
	CreateTime:       "create_time",
	UpdateTime:       "update_time",
	DeleteTime:       "delete_time",
	Creater:          "creater",
	IsDefault:        "is_default",
	Demo:             "demo",
	Language:         "language",
	OtherLanguageIds: "other_language_ids",
}

// NewTdkTmplDao creates and returns a new DAO object for table data access.
func NewTdkTmplDao(handlers ...gdb.ModelHandler) *TdkTmplDao {
	return &TdkTmplDao{
		group:    "admin",
		table:    "tdk_tmpl",
		columns:  tdkTmplColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TdkTmplDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TdkTmplDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TdkTmplDao) Columns() TdkTmplColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TdkTmplDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TdkTmplDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TdkTmplDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
