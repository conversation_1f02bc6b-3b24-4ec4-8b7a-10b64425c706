// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SelectorConfigContentDao is the data access object for the table selector_config_content.
type SelectorConfigContentDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  SelectorConfigContentColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// SelectorConfigContentColumns defines and stores column names for the table selector_config_content.
type SelectorConfigContentColumns struct {
	Id            string //
	SelectType    string // 选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]
	Title         string // 选择器类型对应的配置名称
	IsOpen        string // 状态 [ 1 启用  2 禁用]
	Sort          string // 排序
	Remark        string // 备注
	CreateTime    string // 创建时间
	CreateAccount string // 创建者
	UpdateTime    string // 更新时间
	UpdateAccount string // 更新者
	DeleteTime    string // 删除时间
	Creater       string // 创建者
	Extra         string // 额外数据：如注册商底下的平台账号[{"account":"xx"}]
}

// selectorConfigContentColumns holds the columns for the table selector_config_content.
var selectorConfigContentColumns = SelectorConfigContentColumns{
	Id:            "id",
	SelectType:    "select_type",
	Title:         "title",
	IsOpen:        "is_open",
	Sort:          "sort",
	Remark:        "remark",
	CreateTime:    "create_time",
	CreateAccount: "create_account",
	UpdateTime:    "update_time",
	UpdateAccount: "update_account",
	DeleteTime:    "delete_time",
	Creater:       "creater",
	Extra:         "extra",
}

// NewSelectorConfigContentDao creates and returns a new DAO object for table data access.
func NewSelectorConfigContentDao(handlers ...gdb.ModelHandler) *SelectorConfigContentDao {
	return &SelectorConfigContentDao{
		group:    "admin",
		table:    "selector_config_content",
		columns:  selectorConfigContentColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SelectorConfigContentDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SelectorConfigContentDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SelectorConfigContentDao) Columns() SelectorConfigContentColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SelectorConfigContentDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SelectorConfigContentDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SelectorConfigContentDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
