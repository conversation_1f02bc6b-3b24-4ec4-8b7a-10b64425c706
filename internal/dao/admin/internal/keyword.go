// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// KeywordDao is the data access object for the table keyword.
type KeywordDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  KeywordColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// KeywordColumns defines and stores column names for the table keyword.
type KeywordColumns struct {
	Id           string //
	Belong       string // 所属(0:自身 1:分组 2:站点)
	BelongId     string // 分组/站点id
	SelfId       string // 自身id(如果belong非0,那这个指向的是id)
	Name         string // 名称
	Url          string // 链接
	Status       string // 状态(1:启用 2:禁用)
	LinkProperty string // 链接属性(1:新窗口打开 2:nofollow)
	Remark       string // 备注
	CreateTime   string // 创建时间
	UpdateTime   string // 修改时间
	DeleteTime   string // 删除时间
	Creater      string // 创建者
}

// keywordColumns holds the columns for the table keyword.
var keywordColumns = KeywordColumns{
	Id:           "id",
	Belong:       "belong",
	BelongId:     "belong_id",
	SelfId:       "self_id",
	Name:         "name",
	Url:          "url",
	Status:       "status",
	LinkProperty: "link_property",
	Remark:       "remark",
	CreateTime:   "create_time",
	UpdateTime:   "update_time",
	DeleteTime:   "delete_time",
	Creater:      "creater",
}

// NewKeywordDao creates and returns a new DAO object for table data access.
func NewKeywordDao(handlers ...gdb.ModelHandler) *KeywordDao {
	return &KeywordDao{
		group:    "admin",
		table:    "keyword",
		columns:  keywordColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *KeywordDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *KeywordDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *KeywordDao) Columns() KeywordColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *KeywordDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *KeywordDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *KeywordDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
