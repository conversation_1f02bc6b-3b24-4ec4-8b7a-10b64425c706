// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// StoreBaseConfigDao is the data access object for the table store_base_config.
type StoreBaseConfigDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  StoreBaseConfigColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// StoreBaseConfigColumns defines and stores column names for the table store_base_config.
type StoreBaseConfigColumns struct {
	Id                    string //
	Type                  string // 存储类型[local 本地存储, aws AWS亚马逊云存储]
	ThumbnailLargeWidth   string // 缩略大图宽
	ThumbnailLargeHeight  string // 缩略大图高
	ThumbnailMediumWidth  string // 缩略中图宽
	ThumbnailMediumHeight string // 缩略中图高
	ThumbnailSmallWidth   string // 缩略小图宽
	ThumbnailSmallHeight  string // 缩略小图高
	IsOpenWatermark       string // 是否开启水印： 1:开 2:关
	WatermarkType         string // 水印类型： 1:图片 2:文字
	WatermarkContent      string // 水印图片或水印文字
	WatermarkLocation     string // 水印位置
	WatermarkOpacity      string // 水印透明度
	WatermarkRotation     string // 水印倾斜度
	WatermarkHorizontal   string // 水印横坐标偏移量
	WatermarkVertical     string // 水印纵坐标偏移量
	CreateTime            string // 创建时间
	CreateAccount         string // 创建者
	UpdateTime            string // 更新时间
	UpdateAccount         string // 更新者
	DeleteTime            string // 删除时间
}

// storeBaseConfigColumns holds the columns for the table store_base_config.
var storeBaseConfigColumns = StoreBaseConfigColumns{
	Id:                    "id",
	Type:                  "type",
	ThumbnailLargeWidth:   "thumbnail_large_width",
	ThumbnailLargeHeight:  "thumbnail_large_height",
	ThumbnailMediumWidth:  "thumbnail_medium_width",
	ThumbnailMediumHeight: "thumbnail_medium_height",
	ThumbnailSmallWidth:   "thumbnail_small_width",
	ThumbnailSmallHeight:  "thumbnail_small_height",
	IsOpenWatermark:       "is_open_watermark",
	WatermarkType:         "watermark_type",
	WatermarkContent:      "watermark_content",
	WatermarkLocation:     "watermark_location",
	WatermarkOpacity:      "watermark_opacity",
	WatermarkRotation:     "watermark_rotation",
	WatermarkHorizontal:   "watermark_horizontal",
	WatermarkVertical:     "watermark_vertical",
	CreateTime:            "create_time",
	CreateAccount:         "create_account",
	UpdateTime:            "update_time",
	UpdateAccount:         "update_account",
	DeleteTime:            "delete_time",
}

// NewStoreBaseConfigDao creates and returns a new DAO object for table data access.
func NewStoreBaseConfigDao(handlers ...gdb.ModelHandler) *StoreBaseConfigDao {
	return &StoreBaseConfigDao{
		group:    "admin",
		table:    "store_base_config",
		columns:  storeBaseConfigColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *StoreBaseConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *StoreBaseConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *StoreBaseConfigDao) Columns() StoreBaseConfigColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *StoreBaseConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *StoreBaseConfigDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *StoreBaseConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
