// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SitemapDao is the data access object for the table sitemap.
type SitemapDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SitemapColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SitemapColumns defines and stores column names for the table sitemap.
type SitemapColumns struct {
	Id                 string //
	Belong             string // 所属(1:分组 2:站点)
	BelongId           string // 所属分组/站点id
	Format             string // 格式(1:xml地图 2:txt地图 3:html地图)
	CreateType         string // 生成方式(1:手动 2:自动)
	MainRefreshRate    string // 首页更新频率(1:每天 2:每星期 3:每月)
	ListRefreshRate    string // 列表页更新频率(1:每天 2:每星期 3:每月)
	ContentRefreshRate string // 内容页更新频率(1:每天 2:每星期 3:每月)
	MainLevel          string // 首页优先级别
	ListLevel          string // 列表页优先级别
	ContentLevel       string // 内容页优先级别
	Status             string // 状态(1:启用 2:禁用)
	Remark             string // 说明
	CreateTime         string // 创建时间
	UpdateTime         string // 修改时间
	DeleteTime         string // 删除时间
	Creater            string // 创建者
	LinkNum            string // 每个地图文件内链最大数
}

// sitemapColumns holds the columns for the table sitemap.
var sitemapColumns = SitemapColumns{
	Id:                 "id",
	Belong:             "belong",
	BelongId:           "belong_id",
	Format:             "format",
	CreateType:         "create_type",
	MainRefreshRate:    "main_refresh_rate",
	ListRefreshRate:    "list_refresh_rate",
	ContentRefreshRate: "content_refresh_rate",
	MainLevel:          "main_level",
	ListLevel:          "list_level",
	ContentLevel:       "content_level",
	Status:             "status",
	Remark:             "remark",
	CreateTime:         "create_time",
	UpdateTime:         "update_time",
	DeleteTime:         "delete_time",
	Creater:            "creater",
	LinkNum:            "link_num",
}

// NewSitemapDao creates and returns a new DAO object for table data access.
func NewSitemapDao(handlers ...gdb.ModelHandler) *SitemapDao {
	return &SitemapDao{
		group:    "admin",
		table:    "sitemap",
		columns:  sitemapColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SitemapDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SitemapDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SitemapDao) Columns() SitemapColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SitemapDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SitemapDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SitemapDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
