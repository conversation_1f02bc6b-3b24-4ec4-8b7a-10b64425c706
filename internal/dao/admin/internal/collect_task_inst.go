// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CollectTaskInstDao is the data access object for the table collect_task_inst.
type CollectTaskInstDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  CollectTaskInstColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// CollectTaskInstColumns defines and stores column names for the table collect_task_inst.
type CollectTaskInstColumns struct {
	Id         string //
	TaskId     string // 关联 collect_task.id
	Symbol     string // 关联 collect_task.symbol
	InstSymbol string // 这个任务自己的标识
	RetryTimes string // 已经重试几次了
	Status     string // 1: 未完成， 2:已完成且成功， 3:执行失败，
	CreateTime string // 创建时间,时间戳毫秒
	UpdateTime string // 更新时间,时间戳毫秒
	RunStatus  string // 本次任务执行的上下文信息，生成自collect_task.run_status
}

// collectTaskInstColumns holds the columns for the table collect_task_inst.
var collectTaskInstColumns = CollectTaskInstColumns{
	Id:         "id",
	TaskId:     "task_id",
	Symbol:     "symbol",
	InstSymbol: "inst_symbol",
	RetryTimes: "retry_times",
	Status:     "status",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	RunStatus:  "run_status",
}

// NewCollectTaskInstDao creates and returns a new DAO object for table data access.
func NewCollectTaskInstDao(handlers ...gdb.ModelHandler) *CollectTaskInstDao {
	return &CollectTaskInstDao{
		group:    "admin",
		table:    "collect_task_inst",
		columns:  collectTaskInstColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CollectTaskInstDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CollectTaskInstDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CollectTaskInstDao) Columns() CollectTaskInstColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CollectTaskInstDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CollectTaskInstDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CollectTaskInstDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
