// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PermissionNodeDao is the data access object for the table permission_node.
type PermissionNodeDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  PermissionNodeColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// PermissionNodeColumns defines and stores column names for the table permission_node.
type PermissionNodeColumns struct {
	Id      string //
	PId     string // 父权限id（顶级为0）
	Name    string // 菜单编号（权限编号）
	Label   string // 显示名称（中），逗号分隔
	OrderBy string // 排序
}

// permissionNodeColumns holds the columns for the table permission_node.
var permissionNodeColumns = PermissionNodeColumns{
	Id:      "id",
	PId:     "p_id",
	Name:    "name",
	Label:   "label",
	OrderBy: "order_by",
}

// NewPermissionNodeDao creates and returns a new DAO object for table data access.
func NewPermissionNodeDao(handlers ...gdb.ModelHandler) *PermissionNodeDao {
	return &PermissionNodeDao{
		group:    "admin",
		table:    "permission_node",
		columns:  permissionNodeColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *PermissionNodeDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *PermissionNodeDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *PermissionNodeDao) Columns() PermissionNodeColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *PermissionNodeDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *PermissionNodeDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *PermissionNodeDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
