// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SensitiveDao is the data access object for the table sensitive.
type SensitiveDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SensitiveColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SensitiveColumns defines and stores column names for the table sensitive.
type SensitiveColumns struct {
	Id            string //
	ClassId       string // 分类ID
	Word          string // 敏感词
	IsOpen        string // 是否启用：1开 2关
	CreateTime    string // 创建时间
	CreateAccount string // 创建者
	UpdateTime    string // 更新时间
	UpdateAccount string // 更新者
	DeleteTime    string // 删除时间
}

// sensitiveColumns holds the columns for the table sensitive.
var sensitiveColumns = SensitiveColumns{
	Id:            "id",
	ClassId:       "class_id",
	Word:          "word",
	IsOpen:        "is_open",
	CreateTime:    "create_time",
	CreateAccount: "create_account",
	UpdateTime:    "update_time",
	UpdateAccount: "update_account",
	DeleteTime:    "delete_time",
}

// NewSensitiveDao creates and returns a new DAO object for table data access.
func NewSensitiveDao(handlers ...gdb.ModelHandler) *SensitiveDao {
	return &SensitiveDao{
		group:    "admin",
		table:    "sensitive",
		columns:  sensitiveColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SensitiveDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SensitiveDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SensitiveDao) Columns() SensitiveColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SensitiveDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SensitiveDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SensitiveDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
