// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RolePermissionConfigDao is the data access object for the table role_permission_config.
type RolePermissionConfigDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  RolePermissionConfigColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// RolePermissionConfigColumns defines and stores column names for the table role_permission_config.
type RolePermissionConfigColumns struct {
	Id            string //
	Name          string // 角色名称
	RoleLevel     string // 角色层级：1管理员 2站长 3站员
	Label         string // 角色名称（中）
	PermissionSet string // 权限集合：id数组
	MaskedFields  string // 掩码字段 ：map结构{id: {k1:v1, k2:v2}}
	Remark        string // 备注
	OrderBy       string // 排序
	CreateTime    string // 创建时间
	CreateAccount string // 创建者
	UpdateTime    string // 更新时间
	UpdateAccount string // 更新者
	DeleteTime    string // 删除时间
}

// rolePermissionConfigColumns holds the columns for the table role_permission_config.
var rolePermissionConfigColumns = RolePermissionConfigColumns{
	Id:            "id",
	Name:          "name",
	RoleLevel:     "role_level",
	Label:         "label",
	PermissionSet: "permission_set",
	MaskedFields:  "masked_fields",
	Remark:        "remark",
	OrderBy:       "order_by",
	CreateTime:    "create_time",
	CreateAccount: "create_account",
	UpdateTime:    "update_time",
	UpdateAccount: "update_account",
	DeleteTime:    "delete_time",
}

// NewRolePermissionConfigDao creates and returns a new DAO object for table data access.
func NewRolePermissionConfigDao(handlers ...gdb.ModelHandler) *RolePermissionConfigDao {
	return &RolePermissionConfigDao{
		group:    "admin",
		table:    "role_permission_config",
		columns:  rolePermissionConfigColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RolePermissionConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RolePermissionConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RolePermissionConfigDao) Columns() RolePermissionConfigColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RolePermissionConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RolePermissionConfigDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RolePermissionConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
