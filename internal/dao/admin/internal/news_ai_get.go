// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsAiGetDao is the data access object for the table news_ai_get.
type NewsAiGetDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  NewsAiGetColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// NewsAiGetColumns defines and stores column names for the table news_ai_get.
type NewsAiGetColumns struct {
	Id         string //
	Title      string // 标题
	Content    string // 文章正文
	CreateTime string //
	UpdateTime string //
	Type       string // 类型。 1足球，2篮球
	IsPass     string // 审核状态(1通过 2不通过)
}

// newsAiGetColumns holds the columns for the table news_ai_get.
var newsAiGetColumns = NewsAiGetColumns{
	Id:         "id",
	Title:      "title",
	Content:    "content",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	Type:       "type",
	IsPass:     "is_pass",
}

// NewNewsAiGetDao creates and returns a new DAO object for table data access.
func NewNewsAiGetDao(handlers ...gdb.ModelHandler) *NewsAiGetDao {
	return &NewsAiGetDao{
		group:    "admin",
		table:    "news_ai_get",
		columns:  newsAiGetColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *NewsAiGetDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *NewsAiGetDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *NewsAiGetDao) Columns() NewsAiGetColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *NewsAiGetDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *NewsAiGetDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *NewsAiGetDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
