// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// LabelDao is the data access object for the table label.
type LabelDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  LabelColumns       // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// LabelColumns defines and stores column names for the table label.
type LabelColumns struct {
	Id           string //
	BelongId     string // 分组/站点id
	SelfId       string // 自身id(如果belong非0,那这个指向的是id)
	Belong       string // 所属(0:自身 1:分组 2:站点)
	Name         string // 名称
	Clicks       string // 点击数
	RelNewsCount string // 文章数
	Sort         string // 排序
	IsUsed       string // 是否常用(1:是 2:否)
	IsRecommend  string // 是否推荐(1:是 2:否)
	Status       string // 状态(1:显示 2:隐藏)
	SeoTitle     string // seo标题
	SeoKeyword   string // seo关键词
	SeoDesc      string // seo描述
	Url          string // 路由(唯一)
	Thumb        string // 缩略图
	CreateTime   string // 创建时间
	UpdateTime   string // 更新时间
	DeleteTime   string // 删除时间
	Creater      string // 创建者
}

// labelColumns holds the columns for the table label.
var labelColumns = LabelColumns{
	Id:           "id",
	BelongId:     "belong_id",
	SelfId:       "self_id",
	Belong:       "belong",
	Name:         "name",
	Clicks:       "clicks",
	RelNewsCount: "rel_news_count",
	Sort:         "sort",
	IsUsed:       "is_used",
	IsRecommend:  "is_recommend",
	Status:       "status",
	SeoTitle:     "seo_title",
	SeoKeyword:   "seo_keyword",
	SeoDesc:      "seo_desc",
	Url:          "url",
	Thumb:        "thumb",
	CreateTime:   "create_time",
	UpdateTime:   "update_time",
	DeleteTime:   "delete_time",
	Creater:      "creater",
}

// NewLabelDao creates and returns a new DAO object for table data access.
func NewLabelDao(handlers ...gdb.ModelHandler) *LabelDao {
	return &LabelDao{
		group:    "admin",
		table:    "label",
		columns:  labelColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *LabelDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *LabelDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *LabelDao) Columns() LabelColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *LabelDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *LabelDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *LabelDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
