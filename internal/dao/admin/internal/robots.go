// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RobotsDao is the data access object for the table robots.
type RobotsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  RobotsColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// RobotsColumns defines and stores column names for the table robots.
type RobotsColumns struct {
	Id         string //
	Name       string // name
	Content    string // content
	CreateTime string // 创建时间
	UpdateTime string // 修改时间
	DeleteTime string // 删除时间
	Creater    string // 创建者
}

// robotsColumns holds the columns for the table robots.
var robotsColumns = RobotsColumns{
	Id:         "id",
	Name:       "name",
	Content:    "content",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	DeleteTime: "delete_time",
	Creater:    "creater",
}

// NewRobotsDao creates and returns a new DAO object for table data access.
func NewRobotsDao(handlers ...gdb.ModelHandler) *RobotsDao {
	return &RobotsDao{
		group:    "admin",
		table:    "robots",
		columns:  robotsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RobotsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RobotsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RobotsDao) Columns() RobotsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RobotsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RobotsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RobotsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
