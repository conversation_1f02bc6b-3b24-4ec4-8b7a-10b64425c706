// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CollectExtraInfoDao is the data access object for the table collect_extra_info.
type CollectExtraInfoDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  CollectExtraInfoColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// CollectExtraInfoColumns defines and stores column names for the table collect_extra_info.
type CollectExtraInfoColumns struct {
	CategoryId   string // 本seo属于哪个分类 1足球赛事 2足球球队 3足球球员 4篮球赛事 5篮球球队 6篮球球员 ...
	BelongId     string // 属于对应的category中的哪个id的数据。比如当category=1时，它表示足球赛事id
	ViewCount    string // 浏览量
	ReleaseTime  string // 更新时间
	OrderWeight  string // 排序权重
	Introduction string // 简介(联赛、球队、球员)
	IsHot        string // 是否热门(1是 2否)
	IsTop        string // 是否置顶(1是 2否)
}

// collectExtraInfoColumns holds the columns for the table collect_extra_info.
var collectExtraInfoColumns = CollectExtraInfoColumns{
	CategoryId:   "category_id",
	BelongId:     "belong_id",
	ViewCount:    "view_count",
	ReleaseTime:  "release_time",
	OrderWeight:  "order_weight",
	Introduction: "introduction",
	IsHot:        "is_hot",
	IsTop:        "is_top",
}

// NewCollectExtraInfoDao creates and returns a new DAO object for table data access.
func NewCollectExtraInfoDao(handlers ...gdb.ModelHandler) *CollectExtraInfoDao {
	return &CollectExtraInfoDao{
		group:    "admin",
		table:    "collect_extra_info",
		columns:  collectExtraInfoColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CollectExtraInfoDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CollectExtraInfoDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CollectExtraInfoDao) Columns() CollectExtraInfoColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CollectExtraInfoDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CollectExtraInfoDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CollectExtraInfoDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
