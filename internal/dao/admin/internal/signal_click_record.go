// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SignalClickRecordDao is the data access object for the table signal_click_record.
type SignalClickRecordDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  SignalClickRecordColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// SignalClickRecordColumns defines and stores column names for the table signal_click_record.
type SignalClickRecordColumns struct {
	Id        string //
	ProductId string // 直播信号源id
	Clicks    string // 当天点击量
	RecordDay string // 当天日期
}

// signalClickRecordColumns holds the columns for the table signal_click_record.
var signalClickRecordColumns = SignalClickRecordColumns{
	Id:        "id",
	ProductId: "product_id",
	Clicks:    "clicks",
	RecordDay: "record_day",
}

// NewSignalClickRecordDao creates and returns a new DAO object for table data access.
func NewSignalClickRecordDao(handlers ...gdb.ModelHandler) *SignalClickRecordDao {
	return &SignalClickRecordDao{
		group:    "admin",
		table:    "signal_click_record",
		columns:  signalClickRecordColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SignalClickRecordDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SignalClickRecordDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SignalClickRecordDao) Columns() SignalClickRecordColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SignalClickRecordDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SignalClickRecordDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SignalClickRecordDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
