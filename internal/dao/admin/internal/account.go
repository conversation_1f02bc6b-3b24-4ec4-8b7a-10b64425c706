// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AccountDao is the data access object for the table account.
type AccountDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  AccountColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// AccountColumns defines and stores column names for the table account.
type AccountColumns struct {
	Id                  string //
	Account             string // 帐号
	Password            string // 密码
	NickName            string // 昵称
	Contact             string // 联系方式
	Remark              string // 备注
	RoleId              string // 角色id
	AuditPassword       string // 私人密码
	IsOnline            string // 1:在线 2:离线
	IsAffect            string // 1:启用 2:停用
	LastSigninTime      string // 上次登录时间
	CreateTime          string // 创建时间
	CreateAccount       string // 创建者
	UpdateTime          string // 更新时间
	UpdateAccount       string // 更新者
	DeleteTime          string // 删除时间
	Creater             string // 创建者
	IsRequireGoogleAuth string // 是否谷歌验证码登录  1:需要 2:不用
	GoogleAuthSecret    string // 谷歌验证秘钥
	TemplateIds         string // 可访问的模板id集合
}

// accountColumns holds the columns for the table account.
var accountColumns = AccountColumns{
	Id:                  "id",
	Account:             "account",
	Password:            "password",
	NickName:            "nick_name",
	Contact:             "contact",
	Remark:              "remark",
	RoleId:              "role_id",
	AuditPassword:       "audit_password",
	IsOnline:            "is_online",
	IsAffect:            "is_affect",
	LastSigninTime:      "last_signin_time",
	CreateTime:          "create_time",
	CreateAccount:       "create_account",
	UpdateTime:          "update_time",
	UpdateAccount:       "update_account",
	DeleteTime:          "delete_time",
	Creater:             "creater",
	IsRequireGoogleAuth: "is_require_google_auth",
	GoogleAuthSecret:    "google_auth_secret",
	TemplateIds:         "template_ids",
}

// NewAccountDao creates and returns a new DAO object for table data access.
func NewAccountDao(handlers ...gdb.ModelHandler) *AccountDao {
	return &AccountDao{
		group:    "admin",
		table:    "account",
		columns:  accountColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AccountDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AccountDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AccountDao) Columns() AccountColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AccountDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AccountDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AccountDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
