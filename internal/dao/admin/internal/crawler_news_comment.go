// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CrawlerNewsCommentDao is the data access object for the table crawler_news_comment.
type CrawlerNewsCommentDao struct {
	table    string                    // table is the underlying table name of the DAO.
	group    string                    // group is the database configuration group name of the current DAO.
	columns  CrawlerNewsCommentColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler        // handlers for customized model modification.
}

// CrawlerNewsCommentColumns defines and stores column names for the table crawler_news_comment.
type CrawlerNewsCommentColumns struct {
	Id            string //
	Source        string // 来源 （比如新浪体育
	Mid           string // 消息id
	Docid         string // 在原始来源处的唯一id
	Content       string // 评论内容
	CompetitionId string // 关联的赛事id,  为0表示还未关联
	Lid           string // 关联的赛事id,  为0表示还未关联
	CreateTime    string //
	UpdateTime    string //
	Type          string // 类型。 1足球，2篮球
}

// crawlerNewsCommentColumns holds the columns for the table crawler_news_comment.
var crawlerNewsCommentColumns = CrawlerNewsCommentColumns{
	Id:            "id",
	Source:        "source",
	Mid:           "mid",
	Docid:         "docid",
	Content:       "content",
	CompetitionId: "competition_id",
	Lid:           "lid",
	CreateTime:    "create_time",
	UpdateTime:    "update_time",
	Type:          "type",
}

// NewCrawlerNewsCommentDao creates and returns a new DAO object for table data access.
func NewCrawlerNewsCommentDao(handlers ...gdb.ModelHandler) *CrawlerNewsCommentDao {
	return &CrawlerNewsCommentDao{
		group:    "admin",
		table:    "crawler_news_comment",
		columns:  crawlerNewsCommentColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CrawlerNewsCommentDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CrawlerNewsCommentDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CrawlerNewsCommentDao) Columns() CrawlerNewsCommentColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CrawlerNewsCommentDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CrawlerNewsCommentDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CrawlerNewsCommentDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
