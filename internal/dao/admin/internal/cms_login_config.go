// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CmsLoginConfigDao is the data access object for the table cms_login_config.
type CmsLoginConfigDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  CmsLoginConfigColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// CmsLoginConfigColumns defines and stores column names for the table cms_login_config.
type CmsLoginConfigColumns struct {
	Id            string //
	IsOpen        string // 后台开关： 1:开 2:关
	Title         string // 后台名称
	Url           string // 后台网址
	IsIpBind      string // IP绑定： 1:开 2:关
	IsMacBind     string // 机器码绑定： 1:开 2:关
	IsGoogleBind  string // 谷歌验证码绑定： 1:开 2:关
	CreateTime    string // 创建时间
	CreateAccount string // 创建者
	UpdateTime    string // 更新时间
	UpdateAccount string // 更新者
	DeleteTime    string // 删除时间
	Creater       string // 创建者
}

// cmsLoginConfigColumns holds the columns for the table cms_login_config.
var cmsLoginConfigColumns = CmsLoginConfigColumns{
	Id:            "id",
	IsOpen:        "is_open",
	Title:         "title",
	Url:           "url",
	IsIpBind:      "is_ip_bind",
	IsMacBind:     "is_mac_bind",
	IsGoogleBind:  "is_google_bind",
	CreateTime:    "create_time",
	CreateAccount: "create_account",
	UpdateTime:    "update_time",
	UpdateAccount: "update_account",
	DeleteTime:    "delete_time",
	Creater:       "creater",
}

// NewCmsLoginConfigDao creates and returns a new DAO object for table data access.
func NewCmsLoginConfigDao(handlers ...gdb.ModelHandler) *CmsLoginConfigDao {
	return &CmsLoginConfigDao{
		group:    "admin",
		table:    "cms_login_config",
		columns:  cmsLoginConfigColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CmsLoginConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CmsLoginConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CmsLoginConfigDao) Columns() CmsLoginConfigColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CmsLoginConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CmsLoginConfigDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CmsLoginConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
