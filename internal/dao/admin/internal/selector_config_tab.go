// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SelectorConfigTabDao is the data access object for the table selector_config_tab.
type SelectorConfigTabDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  SelectorConfigTabColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// SelectorConfigTabColumns defines and stores column names for the table selector_config_tab.
type SelectorConfigTabColumns struct {
	Id            string //
	SelectType    string // 选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]
	TabName       string // 栏目名称
	IsOpen        string // 状态 [ 1 开 2 关]
	IsList        string // 栏目内容 [ 1 列表  2 功能配置 ]
	CreateTime    string // 创建时间
	CreateAccount string // 创建者
	UpdateTime    string // 更新时间
	UpdateAccount string // 更新者
	DeleteTime    string // 删除时间
	Creater       string // 创建者
}

// selectorConfigTabColumns holds the columns for the table selector_config_tab.
var selectorConfigTabColumns = SelectorConfigTabColumns{
	Id:            "id",
	SelectType:    "select_type",
	TabName:       "tab_name",
	IsOpen:        "is_open",
	IsList:        "is_list",
	CreateTime:    "create_time",
	CreateAccount: "create_account",
	UpdateTime:    "update_time",
	UpdateAccount: "update_account",
	DeleteTime:    "delete_time",
	Creater:       "creater",
}

// NewSelectorConfigTabDao creates and returns a new DAO object for table data access.
func NewSelectorConfigTabDao(handlers ...gdb.ModelHandler) *SelectorConfigTabDao {
	return &SelectorConfigTabDao{
		group:    "admin",
		table:    "selector_config_tab",
		columns:  selectorConfigTabColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SelectorConfigTabDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SelectorConfigTabDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SelectorConfigTabDao) Columns() SelectorConfigTabColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SelectorConfigTabDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SelectorConfigTabDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SelectorConfigTabDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
