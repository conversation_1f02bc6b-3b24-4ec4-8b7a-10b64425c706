// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CollectTaskDao is the data access object for the table collect_task.
type CollectTaskDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  CollectTaskColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// CollectTaskColumns defines and stores column names for the table collect_task.
type CollectTaskColumns struct {
	Id            string //
	Name          string // 任务名，纯用来显示
	Symbol        string // 唯一标识，用来在代码中区分唯一的任务
	RunDuration   string // 任务执行间隔，单位秒， 0表示不可用
	Status        string // 任务状态. 1开启， 2禁用，3因为子任务一直失败而卡住
	MaxRetry      string // 失败后的最多重试次数 (0表示不进行重试
	UpdatedAt     string // 更新时间，时间戳毫秒
	RunStatus     string // 这个任务的当前上下文状态，在代码中自定义，是一个json
	Param         string // 这个任务的一些参数，是一个json
	CurrentInstId string // 当前正在执行的任务实例的id, 每次产生新任务前都要先检查一下当前任务是否已经完成
	RandDelay     string // 任务执行随机延后rand(0,rand_delay) 秒，把任务执行打散，单位秒。
}

// collectTaskColumns holds the columns for the table collect_task.
var collectTaskColumns = CollectTaskColumns{
	Id:            "id",
	Name:          "name",
	Symbol:        "symbol",
	RunDuration:   "run_duration",
	Status:        "status",
	MaxRetry:      "max_retry",
	UpdatedAt:     "updated_at",
	RunStatus:     "runStatus",
	Param:         "param",
	CurrentInstId: "current_inst_id",
	RandDelay:     "rand_delay",
}

// NewCollectTaskDao creates and returns a new DAO object for table data access.
func NewCollectTaskDao(handlers ...gdb.ModelHandler) *CollectTaskDao {
	return &CollectTaskDao{
		group:    "admin",
		table:    "collect_task",
		columns:  collectTaskColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CollectTaskDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CollectTaskDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CollectTaskDao) Columns() CollectTaskColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CollectTaskDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CollectTaskDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CollectTaskDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
