// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CollectMultiLanguageDao is the data access object for the table collect_multi_language.
type CollectMultiLanguageDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  CollectMultiLanguageColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// CollectMultiLanguageColumns defines and stores column names for the table collect_multi_language.
type CollectMultiLanguageColumns struct {
	Table       string // 表名
	Id          string // 表中的id
	Column      string // 表中字段名
	UpdatedAt   string // 更新时间
	NameId      string // 印尼语
	ShortNameId string // 印尼语，去掉()"'"
	NameVi      string // 越南语
	ShortNameVi string // 越南语，去掉()"'"
}

// collectMultiLanguageColumns holds the columns for the table collect_multi_language.
var collectMultiLanguageColumns = CollectMultiLanguageColumns{
	Table:       "table",
	Id:          "id",
	Column:      "column",
	UpdatedAt:   "updated_at",
	NameId:      "name_id",
	ShortNameId: "short_name_id",
	NameVi:      "name_vi",
	ShortNameVi: "short_name_vi",
}

// NewCollectMultiLanguageDao creates and returns a new DAO object for table data access.
func NewCollectMultiLanguageDao(handlers ...gdb.ModelHandler) *CollectMultiLanguageDao {
	return &CollectMultiLanguageDao{
		group:    "admin",
		table:    "collect_multi_language",
		columns:  collectMultiLanguageColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CollectMultiLanguageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CollectMultiLanguageDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CollectMultiLanguageDao) Columns() CollectMultiLanguageColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CollectMultiLanguageDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CollectMultiLanguageDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CollectMultiLanguageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
