// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// LabelOtherRelationDao is the data access object for the table label_other_relation.
type LabelOtherRelationDao struct {
	table    string                    // table is the underlying table name of the DAO.
	group    string                    // group is the database configuration group name of the current DAO.
	columns  LabelOtherRelationColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler        // handlers for customized model modification.
}

// LabelOtherRelationColumns defines and stores column names for the table label_other_relation.
type LabelOtherRelationColumns struct {
	LabelId  string // 标签id
	RelId    string // 关联者id
	RelType  string // 关联者类型(1:新闻资讯 2:足球比赛 3:篮球比赛 4:xxx......)
	Creater  string // 创建者
	BelongId string // 分组/站点id
	Belong   string // 所属(0:自身 1:分组 2:站点)
}

// labelOtherRelationColumns holds the columns for the table label_other_relation.
var labelOtherRelationColumns = LabelOtherRelationColumns{
	LabelId:  "label_id",
	RelId:    "rel_id",
	RelType:  "rel_type",
	Creater:  "creater",
	BelongId: "belong_id",
	Belong:   "belong",
}

// NewLabelOtherRelationDao creates and returns a new DAO object for table data access.
func NewLabelOtherRelationDao(handlers ...gdb.ModelHandler) *LabelOtherRelationDao {
	return &LabelOtherRelationDao{
		group:    "admin",
		table:    "label_other_relation",
		columns:  labelOtherRelationColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *LabelOtherRelationDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *LabelOtherRelationDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *LabelOtherRelationDao) Columns() LabelOtherRelationColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *LabelOtherRelationDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *LabelOtherRelationDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *LabelOtherRelationDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
