// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CrawlerNewsRawDao is the data access object for the table crawler_news_raw.
type CrawlerNewsRawDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  CrawlerNewsRawColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// CrawlerNewsRawColumns defines and stores column names for the table crawler_news_raw.
type CrawlerNewsRawColumns struct {
	Id            string //
	Source        string // 来源 （比如新浪体育
	Docid         string // 在原始来源处的唯一id
	Title         string // 标题
	Url           string // 原始链接
	Intro         string // 摘要
	CompetitionId string // 关联的赛事id,  为0表示还未关联
	SourceCate    string // 在原始来源里的分类标识 （比如新浪就是media_name)
	CreateTime    string //
	UpdateTime    string //
	Content       string // 文章正文
	Type          string // 类型。 1足球，2篮球
	IsVideo       string // 是否视频(1是 2否)
	Language      string // 语言-cn中文 en英文
}

// crawlerNewsRawColumns holds the columns for the table crawler_news_raw.
var crawlerNewsRawColumns = CrawlerNewsRawColumns{
	Id:            "id",
	Source:        "source",
	Docid:         "docid",
	Title:         "title",
	Url:           "url",
	Intro:         "intro",
	CompetitionId: "competition_id",
	SourceCate:    "source_cate",
	CreateTime:    "create_time",
	UpdateTime:    "update_time",
	Content:       "content",
	Type:          "type",
	IsVideo:       "is_video",
	Language:      "language",
}

// NewCrawlerNewsRawDao creates and returns a new DAO object for table data access.
func NewCrawlerNewsRawDao(handlers ...gdb.ModelHandler) *CrawlerNewsRawDao {
	return &CrawlerNewsRawDao{
		group:    "admin",
		table:    "crawler_news_raw",
		columns:  crawlerNewsRawColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CrawlerNewsRawDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CrawlerNewsRawDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CrawlerNewsRawDao) Columns() CrawlerNewsRawColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CrawlerNewsRawDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CrawlerNewsRawDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CrawlerNewsRawDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
