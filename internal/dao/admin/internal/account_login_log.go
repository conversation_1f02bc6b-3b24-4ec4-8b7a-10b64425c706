// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AccountLoginLogDao is the data access object for the table account_login_log.
type AccountLoginLogDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  AccountLoginLogColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// AccountLoginLogColumns defines and stores column names for the table account_login_log.
type AccountLoginLogColumns struct {
	Id          string //
	AccountId   string // 账户id
	AccountName string // 账户名
	SigninTime  string // 登录时间
	Ip          string // 登录ip（ip6长度为39字符）
	IpRegion    string // ip地址位置
	OperType    string // 操作类型 1 登入 2 登出 3 修改密码
	Status      string // 状态 1 成功 2 失败
	DeviceId    string // 设备编号
	DeviceType  string // chrome ie
	Creater     string // 创建者
}

// accountLoginLogColumns holds the columns for the table account_login_log.
var accountLoginLogColumns = AccountLoginLogColumns{
	Id:          "id",
	AccountId:   "account_id",
	AccountName: "account_name",
	SigninTime:  "signin_time",
	Ip:          "ip",
	IpRegion:    "ip_region",
	OperType:    "oper_type",
	Status:      "status",
	DeviceId:    "device_id",
	DeviceType:  "device_type",
	Creater:     "creater",
}

// NewAccountLoginLogDao creates and returns a new DAO object for table data access.
func NewAccountLoginLogDao(handlers ...gdb.ModelHandler) *AccountLoginLogDao {
	return &AccountLoginLogDao{
		group:    "admin",
		table:    "account_login_log",
		columns:  accountLoginLogColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AccountLoginLogDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AccountLoginLogDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AccountLoginLogDao) Columns() AccountLoginLogColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AccountLoginLogDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AccountLoginLogDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AccountLoginLogDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
