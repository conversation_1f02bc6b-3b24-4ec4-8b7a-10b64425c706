// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ToolsDao is the data access object for the table tools.
type ToolsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  ToolsColumns       // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// ToolsColumns defines and stores column names for the table tools.
type ToolsColumns struct {
	Id          string //
	Name        string // 工具名称
	Type        string // 工具类别(关联数据配置)
	Label       string // 工具标签
	Icon        string // 图标
	SeoTitle    string // seo标题
	SeoKeywords string // seo关键字
	SeoDesc     string // seo描述
	Url         string // 自定义url
	CreateTime  string // 创建时间
	UpdateTime  string // 修改时间
	Creator     string // 创建者
	Status      string // 状态(1启用，2禁用)
	SeoList     string // seo集合
	Ui          string // 界面代码
}

// toolsColumns holds the columns for the table tools.
var toolsColumns = ToolsColumns{
	Id:          "id",
	Name:        "name",
	Type:        "type",
	Label:       "label",
	Icon:        "icon",
	SeoTitle:    "seo_title",
	SeoKeywords: "seo_keywords",
	SeoDesc:     "seo_desc",
	Url:         "url",
	CreateTime:  "create_time",
	UpdateTime:  "update_time",
	Creator:     "creator",
	Status:      "status",
	SeoList:     "seo_list",
	Ui:          "ui",
}

// NewToolsDao creates and returns a new DAO object for table data access.
func NewToolsDao(handlers ...gdb.ModelHandler) *ToolsDao {
	return &ToolsDao{
		group:    "admin",
		table:    "tools",
		columns:  toolsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ToolsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ToolsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ToolsDao) Columns() ToolsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ToolsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ToolsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ToolsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
