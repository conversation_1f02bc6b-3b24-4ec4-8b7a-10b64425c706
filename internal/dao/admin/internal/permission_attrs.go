// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PermissionAttrsDao is the data access object for the table permission_attrs.
type PermissionAttrsDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  PermissionAttrsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// PermissionAttrsColumns defines and stores column names for the table permission_attrs.
type PermissionAttrsColumns struct {
	Id           string //
	PermissionId string // 权限id
	UrlPath      string // api接口路径
	MaskedFields string // 脱敏字段配置
	OrderBy      string // 排序
}

// permissionAttrsColumns holds the columns for the table permission_attrs.
var permissionAttrsColumns = PermissionAttrsColumns{
	Id:           "id",
	PermissionId: "permission_id",
	UrlPath:      "url_path",
	MaskedFields: "masked_fields",
	OrderBy:      "order_by",
}

// NewPermissionAttrsDao creates and returns a new DAO object for table data access.
func NewPermissionAttrsDao(handlers ...gdb.ModelHandler) *PermissionAttrsDao {
	return &PermissionAttrsDao{
		group:    "admin",
		table:    "permission_attrs",
		columns:  permissionAttrsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *PermissionAttrsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *PermissionAttrsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *PermissionAttrsDao) Columns() PermissionAttrsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *PermissionAttrsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *PermissionAttrsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *PermissionAttrsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
