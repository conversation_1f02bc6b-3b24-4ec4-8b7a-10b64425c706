// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AccountAuditLogDao is the data access object for the table account_audit_log.
type AccountAuditLogDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  AccountAuditLogColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// AccountAuditLogColumns defines and stores column names for the table account_audit_log.
type AccountAuditLogColumns struct {
	Id          string //
	AdminId     string // 管理员id
	Account     string // 管理员账号
	LoginIp     string // 登录IP
	NodeId      string // 节点ID
	Path        string // 操作路径  eg: 会员管理/会员列表/所有玩家
	Object      string // 操作对象 eg：会员属性 | 黑名单 | 产品名称 |
	ModifyType  string // 修改类型：1新增 2编辑 3删除 4批量编辑 5下载 6上传 7覆盖
	ModifyItem  string // 操作属性  ；eg： 标签 | 状态 | 备注 | 踢下线 | 优质
	ValueBefore string // 修改前记录
	ValueAfter  string // 修改后记录
	DiffOld     string // 修改前变动的值
	DiffNew     string // 修改后变动的值
	ExtendInfo  string // 扩展信息 eg：存放批量编辑影响的会员等
	CreateTime  string // 创建时间
	Creater     string // 创建者
}

// accountAuditLogColumns holds the columns for the table account_audit_log.
var accountAuditLogColumns = AccountAuditLogColumns{
	Id:          "id",
	AdminId:     "admin_id",
	Account:     "account",
	LoginIp:     "login_ip",
	NodeId:      "node_id",
	Path:        "path",
	Object:      "object",
	ModifyType:  "modify_type",
	ModifyItem:  "modify_item",
	ValueBefore: "value_before",
	ValueAfter:  "value_after",
	DiffOld:     "diff_old",
	DiffNew:     "diff_new",
	ExtendInfo:  "extend_info",
	CreateTime:  "create_time",
	Creater:     "creater",
}

// NewAccountAuditLogDao creates and returns a new DAO object for table data access.
func NewAccountAuditLogDao(handlers ...gdb.ModelHandler) *AccountAuditLogDao {
	return &AccountAuditLogDao{
		group:    "admin",
		table:    "account_audit_log",
		columns:  accountAuditLogColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AccountAuditLogDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AccountAuditLogDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AccountAuditLogDao) Columns() AccountAuditLogColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AccountAuditLogDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AccountAuditLogDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AccountAuditLogDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
