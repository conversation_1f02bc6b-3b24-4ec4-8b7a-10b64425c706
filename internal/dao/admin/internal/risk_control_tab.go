// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RiskControlTabDao is the data access object for the table risk_control_tab.
type RiskControlTabDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  RiskControlTabColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// RiskControlTabColumns defines and stores column names for the table risk_control_tab.
type RiskControlTabColumns struct {
	Id            string //
	TabType       string // 栏目分类[1 IP白名单, 2 IP黑名单, 3 UA白名单, 4 UA黑名单,  5 地区屏蔽,  6 CC防御]
	TabName       string // 栏目名称
	IsOpen        string // 状态 [ 1 开 2 关]
	IsPass        string // 类型[ 1 白名单命中通过 2 黑名单命中不通过]
	IsList        string // 栏目内容 [ 1 列表  2 功能配置 ]
	Metas         string // 功能属性：tab_type = 5时 对应 地区屏蔽配置 格式：    {"BlockUserSwitch": 1, "BlockSpiderSwitch": 1}             屏蔽用户：1开 2关     屏蔽蜘蛛：1开 2关 ；  tab_type = 6时 对应 CC防御配置  格式要求如下： {"DefenseSwitch": 1, "TriggerFrequency": 1}    防御开关 ：1开 2关     触发频率：90-150左右
	Priority      string // 访问优先级
	CreateTime    string // 创建时间
	CreateAccount string // 创建者
	UpdateTime    string // 更新时间
	UpdateAccount string // 更新者
	DeleteTime    string // 删除时间
	Creater       string // 创建者
}

// riskControlTabColumns holds the columns for the table risk_control_tab.
var riskControlTabColumns = RiskControlTabColumns{
	Id:            "id",
	TabType:       "tab_type",
	TabName:       "tab_name",
	IsOpen:        "is_open",
	IsPass:        "is_pass",
	IsList:        "is_list",
	Metas:         "metas",
	Priority:      "priority",
	CreateTime:    "create_time",
	CreateAccount: "create_account",
	UpdateTime:    "update_time",
	UpdateAccount: "update_account",
	DeleteTime:    "delete_time",
	Creater:       "creater",
}

// NewRiskControlTabDao creates and returns a new DAO object for table data access.
func NewRiskControlTabDao(handlers ...gdb.ModelHandler) *RiskControlTabDao {
	return &RiskControlTabDao{
		group:    "admin",
		table:    "risk_control_tab",
		columns:  riskControlTabColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RiskControlTabDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RiskControlTabDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RiskControlTabDao) Columns() RiskControlTabColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RiskControlTabDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RiskControlTabDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RiskControlTabDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
