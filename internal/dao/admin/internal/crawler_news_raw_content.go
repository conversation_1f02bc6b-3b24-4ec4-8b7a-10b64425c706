// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CrawlerNewsRawContentDao is the data access object for the table crawler_news_raw_content.
type CrawlerNewsRawContentDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  CrawlerNewsRawContentColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// CrawlerNewsRawContentColumns defines and stores column names for the table crawler_news_raw_content.
type CrawlerNewsRawContentColumns struct {
	Id            string //
	Content       string // 文章正文
	ContentFilter string // 过滤标签后的内容
	IsFilter      string // 是否过滤 0-否1-是
}

// crawlerNewsRawContentColumns holds the columns for the table crawler_news_raw_content.
var crawlerNewsRawContentColumns = CrawlerNewsRawContentColumns{
	Id:            "id",
	Content:       "content",
	ContentFilter: "content_filter",
	IsFilter:      "is_filter",
}

// NewCrawlerNewsRawContentDao creates and returns a new DAO object for table data access.
func NewCrawlerNewsRawContentDao(handlers ...gdb.ModelHandler) *CrawlerNewsRawContentDao {
	return &CrawlerNewsRawContentDao{
		group:    "admin",
		table:    "crawler_news_raw_content",
		columns:  crawlerNewsRawContentColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CrawlerNewsRawContentDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CrawlerNewsRawContentDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CrawlerNewsRawContentDao) Columns() CrawlerNewsRawContentColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CrawlerNewsRawContentDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CrawlerNewsRawContentDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CrawlerNewsRawContentDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
