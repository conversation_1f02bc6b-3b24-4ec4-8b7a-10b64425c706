// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SinglePageDao is the data access object for the table single_page.
type SinglePageDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SinglePageColumns  // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SinglePageColumns defines and stores column names for the table single_page.
type SinglePageColumns struct {
	Id            string //
	BelongGroupId string // 所属分组id
	BelongSiteId  string // 所属站点id
	Name          string // 页面名称
	Url           string // url
	Desc          string // 简介
	Status        string // 状态(1:启用 2:禁用)
	Sort          string // 排序
	SeoTitle      string // seo标题
	SeoKeyword    string // seo关键词
	SeoDesc       string // seo描述
	Thumb         string // 缩略图
	Banner        string // banner图
	Type          string // 类型(1:关于我们 2:联系我们)
	CreateTime    string // 创建时间
	UpdateTime    string // 修改时间
	DeleteTime    string // 删除时间
	Creater       string // 创建者
}

// singlePageColumns holds the columns for the table single_page.
var singlePageColumns = SinglePageColumns{
	Id:            "id",
	BelongGroupId: "belong_group_id",
	BelongSiteId:  "belong_site_id",
	Name:          "name",
	Url:           "url",
	Desc:          "desc",
	Status:        "status",
	Sort:          "sort",
	SeoTitle:      "seo_title",
	SeoKeyword:    "seo_keyword",
	SeoDesc:       "seo_desc",
	Thumb:         "thumb",
	Banner:        "banner",
	Type:          "type",
	CreateTime:    "create_time",
	UpdateTime:    "update_time",
	DeleteTime:    "delete_time",
	Creater:       "creater",
}

// NewSinglePageDao creates and returns a new DAO object for table data access.
func NewSinglePageDao(handlers ...gdb.ModelHandler) *SinglePageDao {
	return &SinglePageDao{
		group:    "admin",
		table:    "single_page",
		columns:  singlePageColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SinglePageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SinglePageDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SinglePageDao) Columns() SinglePageColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SinglePageDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SinglePageDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SinglePageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
