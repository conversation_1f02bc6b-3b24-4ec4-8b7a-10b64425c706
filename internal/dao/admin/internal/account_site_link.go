// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AccountSiteLinkDao is the data access object for the table account_site_link.
type AccountSiteLinkDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  AccountSiteLinkColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// AccountSiteLinkColumns defines and stores column names for the table account_site_link.
type AccountSiteLinkColumns struct {
	Id            string //
	AccountId     string // 账号id
	GroupId       string // 组ID
	SiteId        string // 站点id
	IsAffect      string // 1:启用 2:停用
	CreateTime    string // 创建时间
	CreateAccount string // 创建者
	UpdateTime    string // 更新时间
	UpdateAccount string // 更新者
	Creater       string // 创建者ID
}

// accountSiteLinkColumns holds the columns for the table account_site_link.
var accountSiteLinkColumns = AccountSiteLinkColumns{
	Id:            "id",
	AccountId:     "account_id",
	GroupId:       "group_id",
	SiteId:        "site_id",
	IsAffect:      "is_affect",
	CreateTime:    "create_time",
	CreateAccount: "create_account",
	UpdateTime:    "update_time",
	UpdateAccount: "update_account",
	Creater:       "creater",
}

// NewAccountSiteLinkDao creates and returns a new DAO object for table data access.
func NewAccountSiteLinkDao(handlers ...gdb.ModelHandler) *AccountSiteLinkDao {
	return &AccountSiteLinkDao{
		group:    "admin",
		table:    "account_site_link",
		columns:  accountSiteLinkColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AccountSiteLinkDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AccountSiteLinkDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AccountSiteLinkDao) Columns() AccountSiteLinkColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AccountSiteLinkDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AccountSiteLinkDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AccountSiteLinkDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
