// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CronJobRecordDao is the data access object for the table cron_job_record.
type CronJobRecordDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  CronJobRecordColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// CronJobRecordColumns defines and stores column names for the table cron_job_record.
type CronJobRecordColumns struct {
	Id       string //
	Symbol   string // cron_job表里的标识
	RunAt    string // 执行时间
	Status   string // 成功还是失败。 1成功
	Progress string // 当前执行的状态
	Retry    string // 这是第几次重试
}

// cronJobRecordColumns holds the columns for the table cron_job_record.
var cronJobRecordColumns = CronJobRecordColumns{
	Id:       "id",
	Symbol:   "symbol",
	RunAt:    "run_at",
	Status:   "status",
	Progress: "progress",
	Retry:    "retry",
}

// NewCronJobRecordDao creates and returns a new DAO object for table data access.
func NewCronJobRecordDao(handlers ...gdb.ModelHandler) *CronJobRecordDao {
	return &CronJobRecordDao{
		group:    "admin",
		table:    "cron_job_record",
		columns:  cronJobRecordColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CronJobRecordDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CronJobRecordDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CronJobRecordDao) Columns() CronJobRecordColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CronJobRecordDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CronJobRecordDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CronJobRecordDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
