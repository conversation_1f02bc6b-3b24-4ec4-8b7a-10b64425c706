// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RiskControlContentDao is the data access object for the table risk_control_content.
type RiskControlContentDao struct {
	table    string                    // table is the underlying table name of the DAO.
	group    string                    // group is the database configuration group name of the current DAO.
	columns  RiskControlContentColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler        // handlers for customized model modification.
}

// RiskControlContentColumns defines and stores column names for the table risk_control_content.
type RiskControlContentColumns struct {
	Id            string //
	TabType       string // 分类 [1 IP白名单, 2 IP黑名单, 3 UA白名单, 4 UA黑名单 ； 不包含 5 地区屏蔽,  6 CC防御]
	Content       string // （1） tab_type = 1时 对应 IP白名单地址；  （2） tab_type = 2时 对应 IP黑名单地址；  （3） tab_type = 3时 对应 UA白名单配置；  （4） tab_type = 4时 对应 UA黑名单配置
	IsOpen        string // 状态 [ 1 开 2 关]
	Remark        string // 备注
	CreateTime    string // 创建时间
	CreateAccount string // 创建者
	UpdateTime    string // 更新时间
	UpdateAccount string // 更新者
	DeleteTime    string // 删除时间
	Creater       string // 创建者
}

// riskControlContentColumns holds the columns for the table risk_control_content.
var riskControlContentColumns = RiskControlContentColumns{
	Id:            "id",
	TabType:       "tab_type",
	Content:       "content",
	IsOpen:        "is_open",
	Remark:        "remark",
	CreateTime:    "create_time",
	CreateAccount: "create_account",
	UpdateTime:    "update_time",
	UpdateAccount: "update_account",
	DeleteTime:    "delete_time",
	Creater:       "creater",
}

// NewRiskControlContentDao creates and returns a new DAO object for table data access.
func NewRiskControlContentDao(handlers ...gdb.ModelHandler) *RiskControlContentDao {
	return &RiskControlContentDao{
		group:    "admin",
		table:    "risk_control_content",
		columns:  riskControlContentColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RiskControlContentDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RiskControlContentDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RiskControlContentDao) Columns() RiskControlContentColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RiskControlContentDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RiskControlContentDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RiskControlContentDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
