// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CronJobDao is the data access object for the table cron_job.
type CronJobDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  CronJobColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// CronJobColumns defines and stores column names for the table cron_job.
type CronJobColumns struct {
	Id        string //
	Name      string // 任务名，纯用来显示
	Symbol    string // 唯一标识，用来在代码中区分唯一的任务
	Status    string // 任务状态. 1开启， 2禁用，3因为子任务一直失败而卡住
	MaxRetry  string // 失败后的最多重试次数 (0表示不进行重试
	UpdatedAt string // 更新时间，时间戳毫秒
	Param     string // 这个任务的一些参数，是一个json
	RandDelay string // 任务执行随机延后rand(0,rand_delay) 秒，把任务执行打散，单位秒。
	Cron      string // crontab string
	Progress  string // 当前进度，自定义的json
}

// cronJobColumns holds the columns for the table cron_job.
var cronJobColumns = CronJobColumns{
	Id:        "id",
	Name:      "name",
	Symbol:    "symbol",
	Status:    "status",
	MaxRetry:  "max_retry",
	UpdatedAt: "updated_at",
	Param:     "param",
	RandDelay: "rand_delay",
	Cron:      "cron",
	Progress:  "progress",
}

// NewCronJobDao creates and returns a new DAO object for table data access.
func NewCronJobDao(handlers ...gdb.ModelHandler) *CronJobDao {
	return &CronJobDao{
		group:    "admin",
		table:    "cron_job",
		columns:  cronJobColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CronJobDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CronJobDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CronJobDao) Columns() CronJobColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CronJobDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CronJobDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CronJobDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
