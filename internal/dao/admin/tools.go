// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// toolsDao is the data access object for the table tools.
// You can define custom methods on it to extend its functionality as needed.
type toolsDao struct {
	*internal.ToolsDao
}

var (
	// Tools is a globally accessible object for table tools operations.
	Tools = toolsDao{internal.NewToolsDao()}
)

// Add your custom methods and functionality below.
