// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package admin

import (
	"gtcms/internal/dao/admin/internal"
)

// storeBaseConfigDao is the data access object for the table store_base_config.
// You can define custom methods on it to extend its functionality as needed.
type storeBaseConfigDao struct {
	*internal.StoreBaseConfigDao
}

var (
	// StoreBaseConfig is a globally accessible object for table store_base_config operations.
	StoreBaseConfig = storeBaseConfigDao{internal.NewStoreBaseConfigDao()}
)

// Add your custom methods and functionality below.
