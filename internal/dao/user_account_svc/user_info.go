// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package user_account_svc

import (
	"gtcms/internal/dao/user_account_svc/internal"
)

// userInfoDao is the data access object for the table user_info.
// You can define custom methods on it to extend its functionality as needed.
type userInfoDao struct {
	*internal.UserInfoDao
}

var (
	// UserInfo is a globally accessible object for table user_info operations.
	UserInfo = userInfoDao{internal.NewUserInfoDao()}
)

// Add your custom methods and functionality below.
