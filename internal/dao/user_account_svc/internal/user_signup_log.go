// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserSignupLogDao is the data access object for the table user_signup_log.
type UserSignupLogDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  UserSignupLogColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// UserSignupLogColumns defines and stores column names for the table user_signup_log.
type UserSignupLogColumns struct {
	Id              string //
	UserId          string // 关联 user 表主键
	Ip              string // 注册ip
	IpRegion        string // 注册IP地理区域
	DeviceId        string // 注册设备号（设备指纹）
	DeviceOs        string // 注册设备系统（android,ios,windows,mac,...）
	DeviceOsVersion string // 注册设备系统版本号
	DeviceType      string // 注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）
	AppType         string // 应用类型（1:android  2: ios，3:h5，4:web，5:其他）
	AppVersion      string // 注册应用类型版本号
	CreateTime      string // 创建时间（注册时间）
	UpdateTime      string // 更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）
}

// userSignupLogColumns holds the columns for the table user_signup_log.
var userSignupLogColumns = UserSignupLogColumns{
	Id:              "id",
	UserId:          "user_id",
	Ip:              "ip",
	IpRegion:        "ip_region",
	DeviceId:        "device_id",
	DeviceOs:        "device_os",
	DeviceOsVersion: "device_os_version",
	DeviceType:      "device_type",
	AppType:         "app_type",
	AppVersion:      "app_version",
	CreateTime:      "create_time",
	UpdateTime:      "update_time",
}

// NewUserSignupLogDao creates and returns a new DAO object for table data access.
func NewUserSignupLogDao(handlers ...gdb.ModelHandler) *UserSignupLogDao {
	return &UserSignupLogDao{
		group:    "user_account_svc",
		table:    "user_signup_log",
		columns:  userSignupLogColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserSignupLogDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserSignupLogDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserSignupLogDao) Columns() UserSignupLogColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserSignupLogDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserSignupLogDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserSignupLogDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
