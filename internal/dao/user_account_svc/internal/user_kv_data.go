// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserKvDataDao is the data access object for the table user_kv_data.
type UserKvDataDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UserKvDataColumns  // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UserKvDataColumns defines and stores column names for the table user_kv_data.
type UserKvDataColumns struct {
	Id         string // 唯一标识，自增主键
	UserId     string // 所属用户的唯一 ID
	KeyPath    string // 以 dot 分隔的键路径，例如 "profile.theme.color"
	ValueData  string // 对应键的 JSON 数据值，支持任意结构
	ValueSize  string // value_data 的 UTF-8 字节长度，用于配额限制
	CreateTime string // 创建时间
	UpdateTime string // 最后更新时间
}

// userKvDataColumns holds the columns for the table user_kv_data.
var userKvDataColumns = UserKvDataColumns{
	Id:         "id",
	UserId:     "user_id",
	KeyPath:    "key_path",
	ValueData:  "value_data",
	ValueSize:  "value_size",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewUserKvDataDao creates and returns a new DAO object for table data access.
func NewUserKvDataDao(handlers ...gdb.ModelHandler) *UserKvDataDao {
	return &UserKvDataDao{
		group:    "user_account_svc",
		table:    "user_kv_data",
		columns:  userKvDataColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserKvDataDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserKvDataDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserKvDataDao) Columns() UserKvDataColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserKvDataDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserKvDataDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserKvDataDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
