// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package user_account_svc

import (
	"gtcms/internal/dao/user_account_svc/internal"
)

// userKvDataDao is the data access object for the table user_kv_data.
// You can define custom methods on it to extend its functionality as needed.
type userKvDataDao struct {
	*internal.UserKvDataDao
}

var (
	// UserKvData is a globally accessible object for table user_kv_data operations.
	UserKvData = userKvDataDao{internal.NewUserKvDataDao()}
)

// Add your custom methods and functionality below.
