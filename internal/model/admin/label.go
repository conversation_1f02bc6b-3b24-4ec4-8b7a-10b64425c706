package admin

type LabelRelated struct {
	RelId   uint `v:"required" json:"relId" dc:"关联者id"`
	RelType int  `v:"required" json:"relType" dc:"关联者类型(1:新闻资讯 2:足球比赛 3:篮球比赛 4:xxx......)"`
	Mode    int  `v:"required" json:"mode" dc:"关联方式(1:关联 2:取消关联)"`
}

type GroupSiteAddReqIn struct {
	Belong   int                   `v:"required" json:"belong" dc:"所属功能(1:组 2:站点)"`
	BelongId uint                  `v:"required" json:"belongId" dc:"所属功能id"`
	RelList  []RelatedGroupSiteAdd `json:"relList" dc:"关联者列表"`
}

type GroupSiteConfigReqIn struct {
	Id               uint `v:"required" json:"id"         dc:"分组id"`
	OpenFriendlyLink uint `v:"required" json:"open_friendly_link" dc:"开启手动友链(1是 2否)"`
	FriendlyLinks    uint `v:"required" json:"friendly_links" dc:"友链数量"`
}

type RelatedGroupSiteAdd struct {
	RelId uint `v:"required" json:"relId" dc:"关联者id"`
	Mode  int  `v:"required" json:"mode" dc:"添加/移除(1:添加 2:移除)"`
}
