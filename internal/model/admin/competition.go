package admin

type BestTeamBasketBallItem struct {
	RankId   int    `json:"rankId" dc:"排名"`
	TeamName string `json:"teamName" dc:"球队名称"`
	TeamLogo string `json:"teamLogo" dc:"球队logo"`
	TeamUrl  string `json:"teamUrl" dc:"球队url"`

	Points              float32 `json:"points" dc:"得分"`
	PointsAgainst       float32 `json:"pointsAgainst" dc:"失分"`
	Rebounds            float32 `json:"rebounds" dc:"篮板数"`
	DefensiveRebounds   float32 `json:"defensiveRebounds" dc:"防守篮板数"`
	OffensiveRebounds   float32 `json:"offensiveRebounds" dc:"进攻篮板数"`
	Assists             float32 `json:"assists" dc:"助攻"`
	Steals              float32 `json:"steals" dc:"抢断"`
	Blocks              float32 `json:"blocks" dc:"盖帽"`
	Turnovers           float32 `json:"turnovers" dc:"失误"`
	TotalFouls          float32 `json:"totalFouls" dc:"	犯规"`
	FieldGoalsAccuracy  string  `json:"fieldGoalsAccuracy" dc:"	投篮命中率"`
	ThreePointsTotal    float32 `json:"threePointsTotal" dc:"	三分命中数"`
	ThreePointsAccuracy string  `json:"threePointsAccuracy" dc:"	三分命中率"`
	FreeThrowsScored    float32 `json:"freeThrowsScored" dc:"	罚球命中数"`
	FreeThrowsAccuracy  string  `json:"freeThrowsAccuracy" dc:"	罚球命中率"`
}

type BestPlayerItem struct {
	Name string          `json:"name" dc:""`
	List []*PlayItemList `json:"list" dc:"项目数据"`
}

type PlayItemList struct {
	RankId     int     `json:"rankId" dc:"排名"`
	PlayerName string  `json:"PlayerName" dc:"球员名称"`
	PlayerUrl  string  `json:"PlayerUrl" dc:"球员URL"`
	PlayerLogo string  `json:"PlayerLogo" dc:"球员logo"`
	TeamName   string  `json:"teamName" dc:"球队名称"`
	TeamLogo   string  `json:"teamLogo" dc:"球队logo"`
	TeamUrl    string  `json:"teamUrl" dc:"球队Url"`
	Score      int     `json:"score" dc:"总数"`
	ScoreFloat float32 `json:"ScoreFloat" dc:"总数-篮球数据"`
}

type ScoreDistributionParam struct {
	Matches int     `json:"matches"` // 比赛场次
	Scored  [][]int `json:"scored"`  // 客队进球分布
}

type HistoryMatchParam struct {
	Id            int   `json:"id"`             // 比赛id
	SeasonId      int   `json:"season_id"`      // 赛季id
	CompetitionId int   `json:"competition_id"` // 赛事id
	HomeTeamId    int   `json:"home_team_id"`   // 主队id
	AwayTeamId    int   `json:"away_team_id"`   // 客队id
	MatchTime     int64 `json:"match_time"`     // 比赛时间
	HomeScores    []int `json:"home_scores"`    // 比分字段说明
}

type ScoreDistributionList struct {
	RankId   int    `json:"rankId" dc:"排名"`
	TeamName string `json:"teamName" dc:"球队名称"`
	TeamLogo string `json:"teamLogo" dc:"球队logo"`
	TeamUrl  string `json:"teamUrl" dc:"球队url"`

	Matches int `json:"matches" dc:"场次"`
	Term1   int `json:"term1" dc:"第1段,15分钟为一段，共6段"`
	Term2   int `json:"term2" dc:"第2段"`
	Term3   int `json:"term3" dc:"第3段"`
	Term4   int `json:"term4" dc:"第4段"`
	Term5   int `json:"term5" dc:"第5段"`
	Term6   int `json:"term6" dc:"第6段"`
}

type CompetitionItem struct {
	Id               int                 `json:"id"`
	ShortNameZh      string              `json:"shortNameZh" dc:"中文简称"`
	NameZht          string              `json:"nameZht" dc:"中文繁体"`
	ShortNameZht     string              `json:"shortNameZht" dc:"中文简称繁体"`
	ShortNameEn      string              `json:"shortNameEn" dc:"英文简称"`
	NameEn           string              `json:"nameEn" dc:"英文"`
	CountryId        int                 `json:"countryId" dc:"国家id"`
	NameZh           string              `json:"nameZh" dc:"中文"`
	Logo             string              `json:"logo" dc:"联赛logo"`
	Introduction     string              `json:"introduction" dc:"简介"`
	Router           string              `json:"router" dc:"路由"`
	SportName        string              `json:"sportName" dc:"体育名称,足球/篮球"`
	SportId          int                 `json:"sportId" dc:"1足球 2篮球"`
	CategoryId       int                 `json:"categoryId"`
	Teams            int                 `json:"teams" dc:"球队数量"`
	TotalPlayers     int                 `json:"totalPlayers" dc:"总球员数"`
	ForeignPlayers   int                 `json:"foreignPlayers" dc:"非本土球员数"`
	TeamsMarketValue int                 `json:"teamsMarketValue" dc:"所有球队市值"`
	TeamInfoMap      map[int]interface{} `json:"teamInfoMap" dc:"队伍信息"`

	TeamList               []*CompetitionTeam        `json:"teamList" dc:"球队列表"`
	CompetitionStatsList   []*CompetitionStats       `json:"competitionStatsList" dc:"统计数据"`
	BestTeamList           []*BestPlayerItem         `json:"bestTeamList" dc:"最佳球队列表"`
	BestTeamBasketBallList []*BestTeamBasketBallItem `json:"bestTeamBasketBallItem" dc:"最佳球队列表"`
	BestPlayerList         []*BestPlayerItem         `json:"bestPlayerList" dc:"最佳球员列表"`
	ScoreDistribution      []*ScoreDistributionList  `json:"scoreDistribution" dc:"进球分布"`
	WinDrawLoss            []*WinDrawLossList        `json:"winDrawLoss" dc:"胜平负统计"`
	GoalsTrend             []*GoalsTrendList         `json:"goalsTrend" dc:"进球走势"`
	CornerAndCard          []*CornerAndCardList      `json:"cornerAndCard" dc:"进球罚牌"`
	SingleDouble           []*SingleDoubleList       `json:"singleDouble" dc:"单双"`
}

type SingleDoubleList struct {
	RankId   int    `json:"rankId" dc:"排名"`
	TeamName string `json:"teamName" dc:"球队名称"`
	TeamLogo string `json:"teamLogo" dc:"球队logo"`
	Matches  int    `json:"matches" dc:"场次"`
	TeamUrl  string `json:"teamUrl" dc:"球队url"`

	One   int `json:"one" dc:"1球"`
	Two   int `json:"two" dc:"2球"`
	Three int `json:"three" dc:"3球"`
	Four  int `json:"four" dc:"4球"`
	Five  int `json:"five" dc:"5球"`
	Six   int `json:"six" dc:"6球"`
	Seven int `json:"seven" dc:"7球+"`

	Single      int     `json:"single" dc:"单"`
	Double      int     `json:"double" dc:"双"`
	SingleRatio float32 `json:"singleRatio" dc:"单率"`
	DoubleRatio float32 `json:"doubleRatio" dc:"双率"`
}
type CornerAndCardList struct {
	RankId   int    `json:"rankId" dc:"排名"`
	TeamName string `json:"teamName" dc:"球队名称"`
	TeamLogo string `json:"teamLogo" dc:"球队logo"`
	TeamUrl  string `json:"teamUrl" dc:"球队url"`

	Matches     int     `json:"matches" dc:"场次"`
	CornerKicks int     `json:"cornerKicks" dc:"角球"`
	MeanKicks   float32 `json:"meanKicks" dc:"场均角球"`
	Cards       int     `json:"cards" dc:"罚牌"`
	MeanCards   float32 `json:"meanCards" dc:"场均罚牌"`
}

type GoalsTrendList struct {
	RankId   int    `json:"rankId" dc:"排名"`
	TeamName string `json:"teamName" dc:"球队名称"`
	TeamLogo string `json:"teamLogo" dc:"球队logo"`
	TeamUrl  string `json:"teamUrl" dc:"球队url"`

	Matches   int     `json:"matches" dc:"场次"`
	HasGoal   int     `json:"hasGoal" dc:"有进球"`
	All       int     `json:"all" dc:"总"`
	Home      int     `json:"home" dc:"主"`
	Away      int     `json:"away" dc:"客"`
	MeanGoals float32 `json:"meanGoals" dc:"场均"`
}

type WinDrawLossList struct {
	RankId   int    `json:"rankId" dc:"排名"`
	TeamName string `json:"teamName" dc:"球队名称"`
	TeamLogo string `json:"teamLogo" dc:"球队logo"`
	TeamUrl  string `json:"teamUrl" dc:"球队url"`

	Matches  int     `json:"matches" dc:"场次"`
	Win      int     `json:"win" dc:"胜"`
	Draw     int     `json:"draw" dc:"平"`
	Loss     int     `json:"loss" dc:"负"`
	WinRatio float32 `json:"winRatio" dc:"胜率"`
}

type CompetitionItemRes struct {
	List []*CompetitionItem `json:"list"`
}

// category联赛group
type CategoryCompetitionList struct {
	Name string                       `json:"name" dc:"name"`
	List []SubCategoryCompetitionItem `json:"list" dc:"list"`
}
type SubCategoryCompetitionItem struct {
	SubName string                    `json:"subName"`
	Logo    string                    `json:"logo"`
	SubList []CategoryCompetitionItem `json:"subList" dc:"subList"`
}
type CategoryCompetitionItem struct {
	Id          int    `json:"id"`
	Name        string `json:"name"`
	Url         string `json:"url"`
	CountryId   int    `json:"countryId"`
	Logo        string `json:"Logo"`
	OrderWeight int    `json:"orderWeight"`
}

// 热门联赛
type HotCompetitionItem struct {
	Id      int    `json:"id"`
	Alias   string `json:"alias"`
	Name    string `json:"name"`
	Logo    string `json:"logo"`
	SportId int    `json:"sportId"`
	Matches int    `json:"matches" dc:"今日场次"`
}

type CompetitionTeam struct {
	Id          int    `json:"id"`
	ShortNameZh string `json:"shortNameZh" dc:"中文简称"`
	NameZh      string `json:"nameZh"`
	NameEn      string `json:"nameEn"`
	Url         string `json:"url"`
	Logo        string `json:"logo"`
	IsHot       int    `json:"isHot" dc:"是否热门球队(1是2否)"`
}

type MatchAnalysis struct {
	Id               int                   `json:"id"`
	MatchId          int                   `json:"match_id"`
	SeasonId         int                   `json:"season_id"`
	CompetitionId    int                   `json:"competition_id"`
	TeamId           int                   `json:"team_id"`
	GoalDistribution GoalDistributionParam `json:"goal_distribution"`
	History          []HistoryHomeParam    `json:"history"`
}

type GoalDistributionParam struct {
	Matches  int     `json:"matches"`  // 比赛场次
	Scored   [][]int `json:"scored"`   // 主队进球分布
	Conceded [][]int `json:"conceded"` // 主队失球分布
}

type HistoryHomeParam struct {
	Id            int   `json:"id"`             // 比赛id
	SeasonId      int   `json:"season_id"`      // 赛季id
	CompetitionId int   `json:"competition_id"` // 赛事id
	HomeTeamId    int   `json:"home_team_id"`   // 主队id
	AwayTeamId    int   `json:"away_team_id"`   // 客队id
	MatchTime     int64 `json:"match_time"`     // 比赛时间
	HomeScores    []int `json:"home_scores"`    // 比分字段说明
	AwayScores    []int `json:"away_scores"`    // 比分字段说明
}

type CompetitionStats struct {
	Id           int                `json:"id"`
	SeasonId     int                `json:"season_id"`
	Promotions   []*StatsPromotions `json:"promotions"`
	Tables       []*StatsTables     `json:"tables"`
	Shooters     []*StatsPlayer     `json:"shooters"`
	TeamsStats   []*Teams           `json:"teams_stats"`
	PlayersStats []*Players         `json:"players_stats"`
}

type Players struct {
	PlayerId          int `json:"player_id"`           // 球员id
	TeamId            int `json:"team_id"`             //球队id
	Matches           int `json:"matches"`             //比赛场次
	Court             int `json:"court"`               // 上场场次
	First             int `json:"first"`               // 首发
	Goals             int `json:"goals"`               // 进球
	Penalty           int `json:"penalty"`             // 点球
	Assists           int `json:"assists"`             // 助攻
	MinutesPlayed     int `json:"minutes_played"`      //出场时间(分钟)
	RedCards          int `json:"red_cards"`           //红牌
	YellowCards       int `json:"yellow_cards"`        // 黄牌
	Shots             int `json:"shots"`               // 射门
	ShotsOnTarget     int `json:"shots_on_target"`     // 射正
	Dribble           int `json:"dribble"`             // 过人
	DribbleSucc       int `json:"dribble_succ"`        // 过人成功
	Clearances        int `json:"clearances"`          //解围
	BlockedShots      int `json:"blocked_shots"`       //有效阻挡
	Interceptions     int `json:"interceptions"`       // 拦截
	Tackles           int `json:"tackles"`             // 抢断
	Passes            int `json:"passes"`              // 传球
	PassesAccuracy    int `json:"passes_accuracy"`     // 传球成功
	KeyPasses         int `json:"key_passes"`          // 关键传球
	Crosses           int `json:"crosses"`             //传中球
	CrossesAccuracy   int `json:"crosses_accuracy"`    //传中球成功
	LongBalls         int `json:"long_balls"`          // 长传
	LongBallsAccuracy int `json:"long_balls_accuracy"` // 成功长传
	Duels             int `json:"duels"`               // 1对1拼抢
	DuelsWon          int `json:"duels_won"`           // 1对1拼抢成功
	Dispossessed      int `json:"dispossessed"`        // 传球被断
	Fouls             int `json:"fouls"`               //犯规
	WasFouled         int `json:"was_fouled"`          //被侵犯
	Offsides          int `json:"offsides"`            // 越位
	Yellow2RedCards   int `json:"yellow2red_cards"`    // 两黄变红
	Saves             int `json:"saves"`               // 扑救
	Punches           int `json:"punches"`             // 拳击球
	RunsOut           int `json:"runs_out"`            // 守门员出击
	RunsOutSucc       int `json:"runs_out_succ"`       //守门员出击成功
	GoodHighClaim     int `json:"good_high_claim"`     //高空出击
	Freekicks         int `json:"freekicks"`           // 任意球
	FreekickGoals     int `json:"freekick_goals"`      // 任意球得分
	HitWoodwork       int `json:"hit_woodwork"`        // 击中门框
	Fastbreaks        int `json:"fastbreaks"`          // 快攻
	FastbreakShots    int `json:"fastbreak_shots"`     // 快攻射门
	FastbreakGoals    int `json:"fastbreak_goals"`     // 快攻进球
	PossLosts         int `json:"poss_losts"`          // 丢失球权
	Rating            int `json:"rating"`              // 评分，10为满分

	//篮球字段
	Scope               int     `json:"scope"`                 //统计范围，1-赛季、2-预选赛、3-小组赛、4-季前赛、5-常规赛、6-淘汰赛(季后赛)、7-附加赛
	Points              int     `json:"points"`                // 得分
	FreeThrowsScored    int     `json:"free_throws_scored"`    // 罚球命中数
	FreeThrowsTotal     int     `json:"free_throws_total"`     // 罚球总数
	FreeThrowsAccuracy  string  `json:"free_throws_accuracy"`  // 罚球命中率
	TwoPointsScored     int     `json:"two_points_scored"`     // 两分球命中数
	TwoPointsTotal      int     `json:"two_points_total"`      // 两分球总数
	TwoPointsAccuracy   string  `json:"two_points_accuracy"`   // 两分球命中率
	ThreePointsScored   int     `json:"three_points_scored"`   // 三分球命中数
	ThreePointsTotal    int     `json:"three_points_total"`    // 三分球总数
	ThreePointsAccuracy string  `json:"three_points_accuracy"` // 三分球命中率
	FieldGoalsScored    int     `json:"field_goals_scored"`    // 投篮命中数
	FieldGoalsTotal     int     `json:"field_goals_total"`     // 投篮总数
	FieldGoalsAccuracy  string  `json:"field_goals_accuracy"`  // 投篮命中率
	Rebounds            int     `json:"rebounds"`              // 篮板数
	DefensiveRebounds   int     `json:"defensive_rebounds"`    // 防守篮板数
	OffensiveRebounds   int     `json:"offensive_rebounds"`    // 进攻篮板数
	Turnovers           int     `json:"turnovers"`             // 失误
	Steals              int     `json:"steals"`                // 抢断
	Blocks              int     `json:"blocks"`                // 盖帽
	PersonalFouls       int     `json:"personal_fouls"`        // 个人犯规
	Pace                float32 `json:"pace"`                  // 回合数(场均) (没有数据字段不存在)
	NetRating           float32 `json:"net_rating"`            //净效率(场均) (没有数据字段不存在)
	AssistRatio         float32 `json:"assist_ratio"`          //助攻比率(场均) (没有数据字段不存在)
	DoubleDoubles       int     `json:"double_doubles"`        //两双(总) (没有数据字段不存在)
	TripleDoubles       int     `json:"triple_doubles"`        //三双(总) (没有数据字段不存在)

	DefensiveRating  float32 `json:"defensive_rating"`  // 防守效率(场均) (没有数据字段不存在)
	OffensiveRating  float32 `json:"offensive_rating"`  // 进攻效率(场均) (没有数据字段不存在)
	UsagePercentage  float32 `json:"usage_percentage"`  // 回合占有率(%) (没有数据字段不存在)
	AssistPercentage float32 `json:"assist_percentage"` // 助攻率(%) (没有数据字段不存在)
	FastBreakPoints  float32 `json:"fast_break_points"` // 快攻得分(场均) (没有数据字段不存在)

	PointsInThePaint              float32 `json:"points_in_the_paint"`             //内线得分(场均) (没有数据字段不存在)
	PointsOffTurnovers            float32 `json:"points_off_turnovers"`            //利用失误得分(场均) (没有数据字段不存在)
	SecondChancePoints            float32 `json:"second_chance_points"`            //二次进攻得分(场均) (没有数据字段不存在)
	ReboundingPercentage          float32 `json:"rebounding_percentage"`           //篮板率(%) (没有数据字段不存在)
	PlayerImpactEstimate          float32 `json:"player_impact_estimate"`          //比赛贡献值(场均) (没有数据字段不存在)
	AssistToTurnoverRatio         float32 `json:"assist_to_turnover_ratio"`        //内线得分(场均) (没有数据字段不存在)
	TrueShootingPercentage        float32 `json:"true_shooting_percentage"`        //真实命中率(%) (没有数据字段不存在)
	EffectiveFieldGoalPercentage  float32 `json:"effective_field_goal_percentage"` //有效命中率(%) (没有数据字段不存在)
	DefensiveReboundingPercentage float32 `json:"defensive_rebounding_percentage"` //防守篮板率(%) (没有数据字段不存在)
	OffensiveReboundingPercentage float32 `json:"offensive_rebounding_percentage"` //进攻篮板率(%) (没有数据字段不存在)

}
type Teams struct {
	TeamId            int `json:"team_id"`             //球队id
	Matches           int `json:"matches"`             //比赛场次
	Goals             int `json:"goals"`               //进球
	Penalty           int `json:"penalty"`             //点球
	Assists           int `json:"assists"`             //助攻
	RedCards          int `json:"red_cards"`           //红牌
	YellowCards       int `json:"yellow_cards"`        //黄牌
	Shots             int `json:"shots"`               //射门
	ShotsOnTarget     int `json:"shots_on_target"`     //射正
	Dribble           int `json:"dribble"`             //过人
	DribbleSucc       int `json:"dribble_succ"`        //过人成功
	Clearances        int `json:"clearances"`          //解围
	BlockedShots      int `json:"blocked_shots"`       //有效阻挡
	Tackles           int `json:"tackles"`             //抢断
	Passes            int `json:"passes"`              //传球
	PassesAccuracy    int `json:"passes_accuracy"`     //传球成功
	KeyPasses         int `json:"key_passes"`          //关键传球
	Crosses           int `json:"crosses"`             //传中球
	CrossesAccuracy   int `json:"crosses_accuracy"`    //传中球成功
	LongBalls         int `json:"long_balls"`          //长传
	LongBallsAccuracy int `json:"long_balls_accuracy"` //成功长传
	Duels             int `json:"duels"`               //1对1拼抢
	DuelsWon          int `json:"duels_won"`           //1对1拼抢成功
	Fouls             int `json:"fouls"`               //犯规
	WasFouled         int `json:"was_fouled"`          //被侵犯
	GoalsAgainst      int `json:"goals_against"`       //失球
	Interceptions     int `json:"interceptions"`       //拦截
	Offsides          int `json:"offsides"`            //越位
	Yellow2RedCards   int `json:"yellow2red_cards"`    //两黄变红
	CornerKicks       int `json:"corner_kicks"`        //角球
	BallPossession    int `json:"ball_possession"`     //控球率
	Freekicks         int `json:"freekicks"`           //任意球
	FreekickGoals     int `json:"freekick_goals"`      //任意球得分
	HitWoodwork       int `json:"hit_woodwork"`        //击中门框
	Fastbreaks        int `json:"fastbreaks"`          //快攻
	FastbreakShots    int `json:"fastbreak_shots"`     //快攻射门
	FastbreakGoals    int `json:"fastbreak_goals"`     //快攻进球
	PossLosts         int `json:"poss_losts"`          //丢失球权

	//篮球字段
	Scope                         int     `json:"scope"`                           // 统计范围，1-赛季、2-预选赛、3-小组赛、4-季前赛、5-常规赛、6-淘汰赛(季后赛)、7-附加赛
	Points                        int     `json:"points"`                          // 得分
	PointsAgainst                 int     `json:"points_against"`                  // 失分
	FreeThrowsScored              int     `json:"free_throws_scored"`              // 罚球命中数
	FreeThrowsTotal               int     `json:"free_throws_total"`               // 罚球总数
	FreeThrowsAccuracy            string  `json:"free_throws_accuracy"`            // 罚球命中率
	TwoPointsScored               int     `json:"two_points_scored"`               // 两分球命中数
	TwoPointsTotal                int     `json:"two_points_total"`                // 两分球总数
	TwoPointsAccuracy             string  `json:"two_points_accuracy"`             // 两分球命中率
	ThreePointsScored             int     `json:"three_points_scored"`             // 三分球命中数
	ThreePointsTotal              int     `json:"three_points_total"`              // 三分球总数
	ThreePointsAccuracy           string  `json:"three_points_accuracy"`           // 三分球命中率
	FieldGoalsScored              int     `json:"field_goals_scored"`              // 投篮命中数
	FieldGoalsTotal               int     `json:"field_goals_total"`               // 投篮总数
	FieldGoalsAccuracy            string  `json:"field_goals_accuracy"`            // 投篮命中率
	TotalFouls                    int     `json:"total_fouls"`                     // 犯规
	Rebounds                      int     `json:"rebounds"`                        // 篮板数
	DefensiveRebounds             int     `json:"defensive_rebounds"`              // 防守篮板数
	OffensiveRebounds             int     `json:"offensive_rebounds"`              // 进攻篮板数
	Turnovers                     int     `json:"turnovers"`                       // 失误
	Steals                        int     `json:"steals"`                          // 抢断
	Blocks                        int     `json:"blocks"`                          // 盖帽
	NetRating                     float32 `json:"net_rating"`                      // 净效率(场均) (没有数据字段不存在)
	DefensiveRating               float32 `json:"defensive_rating"`                // 防守效率(场均) (没有数据字段不存在)
	OffensiveRating               float32 `json:"offensive_rating"`                // 进攻效率(场均) (没有数据字段不存在)
	AssistPercentage              float32 `json:"assist_percentage"`               // 助攻率(%) (没有数据字段不存在)
	FastBreakPoints               float32 `json:"fast_break_points"`               // 快攻得分(场均) (没有数据字段不存在)
	PointsInThePaint              float32 `json:"points_in_the_paint"`             // 内线得分(场均) (没有数据字段不存在)
	PointsOffTurnovers            float32 `json:"points_off_turnovers"`            // 利用失误得分(场均) (没有数据字段不存在)
	SecondChancePoints            float32 `json:"second_chance_points"`            // 二次进攻得分(场均) (没有数据字段不存在)
	ReboundingPercentage          float32 `json:"rebounding_percentage"`           // 篮板率(%) (没有数据字段不存在)
	DefensiveReboundingPercentage float32 `json:"defensive_rebounding_percentage"` // 防守篮板率(%) (没有数据字段不存在)
	OffensiveReboundingPercentage float32 `json:"offensive_rebounding_percentage"` // 进攻篮板率(%) (没有数据字段不存在)
}

type StatsPlayer struct {
	Position      int    `json:"position"`
	PlayerId      int    `json:"player_id"`
	TeamId        int    `json:"team_id"`
	PlayerName    string `json:"player_name"`
	PlayerLogo    string `json:"player_logo"`
	TeamName      string `json:"team_name"`
	Goals         int    `json:"goals"`
	Penalty       int    `json:"penalty"`
	Assists       int    `json:"assists"`
	MinutesPlayed int    `json:"minutes_played"`
}

type StatsTables struct {
	Id         int                `json:"id"`
	Conference string             `json:"conference"`
	Group      int                `json:"group"`
	StageId    int                `json:"stage_id"`
	Rows       []*StatsTablesTeam `json:"rows"`
	// 篮球
	Name  string `json:"name"`
	Scope int    `json:"scope" dc:"1-赛季、2-预选赛、3-小组赛、4-季前赛、5-常规赛、6-淘汰赛(季后赛)、0-无"`
}

type StatsPromotions struct {
	Id      int    `json:"id"`
	NameZH  string `json:"name_zh"`
	NameZHT string `json:"name_zht"`
	NameEN  string `json:"name_en"`
	Color   string `json:"color"`
}

type StatsTablesTeam struct {
	TeamId           int    `json:"team_id"`
	TeamName         string `json:"team_name"`
	TeamUrl          string `json:"team_url"`
	TeamLogo         string `json:"team_logo"`
	PromotionId      int    `json:"promotion_id"`
	DeductPoints     int    `json:"deduct_points"`
	Note             string `json:"note"`
	Points           int    `json:"points"`
	Position         int    `json:"position"`
	Total            int    `json:"total"`
	Won              int    `json:"won"`
	Draw             int    `json:"draw"`
	Loss             int    `json:"loss"`
	Goals            int    `json:"goals"`
	GoalsAgainst     int    `json:"goals_against"`
	GoalsDiff        int    `json:"goals_diff"`
	HomePoints       int    `json:"home_points"`
	HomePosition     int    `json:"home_position"`
	HomeTotal        int    `json:"home_total"`
	HomeWon          int    `json:"home_won"`
	HomeDraw         int    `json:"home_draw"`
	HomeLoss         int    `json:"home_loss"`
	HomeGoals        int    `json:"home_goals"`
	HomeGoalsAgainst int    `json:"home_goals_against"`
	HomeGoalsDiff    int    `json:"home_goals_diff"`
	AwayPoints       int    `json:"away_points"`
	AwayPosition     int    `json:"away_position"`
	AwayTotal        int    `json:"away_total"`
	AwayWon          int    `json:"away_won"`
	AwayDraw         int    `json:"away_draw"`
	AwayLoss         int    `json:"away_loss"`
	AwayGoals        int    `json:"away_goals"`
	AwayGoalsAgainst int    `json:"away_goals_against"`
	AwayGoalsDiff    int    `json:"away_goals_diff"`

	// 篮球
	WonRate          float64 `json:"won_rate"`
	GameBack         string  `json:"game_back"`
	PointsAvg        float64 `json:"points_avg"`
	PointsAgainstAvg float64 `json:"points_against_avg"`
	DiffAvg          float64 `json:"diff_avg"`
	Streaks          int     `json:"streaks"`
	Home             string  `json:"home"`
	Away             string  `json:"away"`
	Division         string  `json:"division"`
	Conference       string  `json:"conference"`
	Last10           string  `json:"last_10"`
	PointsFor        int     `json:"points_for"`
	PointsAgt        int     `json:"points_agt"`
}
