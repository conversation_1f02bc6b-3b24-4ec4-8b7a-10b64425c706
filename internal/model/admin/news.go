package admin

import entity "gtcms/internal/model/entity/admin"

type NewsMakeUrl struct {
	Id         uint   `json:"Id" cd:"id"`
	ColumnsId  uint   `json:"columnsId" cd:"栏目id"`
	CreateTime int64  `json:"createTime" cd:"创建时间"`
	ModuleId   string `json:"moduleId" cd:"模块id"`
}

type CrawlerNewsRaw struct {
	entity.CrawlerNewsRaw
}

type AiNewsAddItem struct {
	Title   string `json:"title"       dc:"标题"`
	Content string `json:"content"     dc:"文章正文"`
}
