package admin

import (
	"io"
	"net/url"
)

type SingleUploadInput struct {
	// File       *ghttp.UploadFile // 上传文件对象
	File     io.Reader // 上传文件对象
	NameExt  string    // 文件名后缀(必传)
	Name     string    // 自定义文件名称(一般情况下不传)
	SportImg int       // 体育图片 (1体育图片 0非体育图片 3pdf)
}

type FileUploadInput struct {
	File io.Reader // 上传文件对象
	Name string    // 自定义文件名称, 请注意文件后缀也需要带上
}

type SingleUploadOutput struct {
	ObjectName string `json:"objectName"` // oss对象名称
}

type GetObjectNameInput struct {
	Name    string // 文件名称(可选)
	NameExt string // 文件后缀(上传的),当name有值时,nameExt不生效
	IsTmp   bool   // 是否临时
}

// GetUrlInput 获取文件链接
type GetUrlInput struct {
	ObjectName string // 对象名称
}

// GetUrlOutput 获取文件链接的返回结果
type GetUrlOutput struct {
	Url *url.URL
}
