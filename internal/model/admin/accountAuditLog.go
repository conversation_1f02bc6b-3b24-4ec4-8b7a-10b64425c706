package admin

type AdminModifyLogVo struct {
	Id          uint   `json:"id"                dc:""`
	AdminId     uint   `json:"adminId"           dc:"管理员id"`
	Account     string `json:"account"           dc:"管理员账号"`
	LoginIp     string `json:"loginIp"           dc:"登录IP"`
	NodeId      int    `json:"nodeId"            dc:"节点ID"`
	Path        string `json:"path"              dc:"操作节点 eg: 会员管理/会员列表/所有玩家"`
	Object      string `json:"object"            dc:"操作对象 自定义eg：游戏账号 john 产品名称：Hp Game； 玩家信息；自动刷新开关"`
	ModifyType  int    `json:"modifyType"  dc:"操作类型：1新增 2编辑 3删除 4批量编辑 5下载 6上传 7 覆盖 8 批量删除"`
	ModifyItem  string `json:"modifyItem"  dc:"操作项目 自定义；eg：状态 踢下线 优质 等等"`
	ValueBefore string `json:"valueBefore" dc:"操作前的值"`
	ValueAfter  string `json:"valueAfter"  dc:"操作后的值"`

	DiffOld string `json:"diffOld"     description:"修改前变动的值"`
	DiffNew string `json:"diffNew"     description:"修改后变动的值"`

	ExtendInfo string `json:"extendInfo"        dc:"扩展信息 eg：存放批量编辑影响的会员等"`
	CreateTime int64  `json:"createTime"        dc:"操作时间"`
}

type NodeInfo struct {
	Id       int         `json:"id" dc:"节点ID"`
	Name     string      `json:"name" dc:"节点名称"`
	ParentId int         `json:"parentId" dc:"父节点ID"`
	Level    int         `json:"level" dc:"层级"`
	ApiNode  string      `json:"apiNode"       dc:"对应的api接口名称"`
	Child    []*NodeInfo `json:"child" dc:"子节点ID"`
}

type AdminModifyInput struct {
	//NodeId          uint            `json:"nodeId"  dc:"节点ID eg：传入对应的节点ID"`
	//Object          string          `json:"object"  dc:"操作对象 自定义eg：游戏账号john, 产品名称, 自动刷新开关"`
	ModifyType      AdminAdjustType `json:"modifyType"  dc:"操作类型：1新增 2编辑 3删除 4批量编辑 5下载 6上传"`
	ModifyItem      string          `json:"modifyItem"  dc:"操作项目 单属性直接填值 eg：状态 踢下线 优质 等等"`
	OldRecordOrAttr interface{}     `json:"oldRecordOrAttr" dc:"操作前的值 范围：单值|map|结构体"`
	NewRecordOrAttr interface{}     `json:"newRecordOrAttr"  dc:"操作后的值 范围：单值|map|结构体"`
	UserIDs         []uint          `json:"userIDs"  dc:"修改影响的用户"`
	ExtendInfo      string          `json:"extendInfo"  dc:"扩展信息json格式 eg：批量编辑影响的会员等"`
}

type ChannelElemModifyLog struct {
	AdminId     uint            `json:"adminId"     description:"管理员id"`
	Account     string          `json:"account"     description:"管理员账号"`
	LoginIp     string          `json:"loginIp"     description:"登录IP"`
	NodeId      uint            `json:"nodeId"  dc:"节点ID eg：传入对应的节点ID"`
	Object      string          `json:"object"  dc:"操作对象 自定义eg：游戏账号john, 产品名称, 自动刷新开关"`
	ModifyType  AdminAdjustType `json:"modifyType"  dc:"操作类型：1新增 2编辑 3删除 4批量编辑 5下载 6上传"`
	ModifyItem  string          `json:"modifyItem"  dc:"操作项目 eg：状态 踢下线 优质 等等"`
	ValueBefore string          `json:"valueBefore" dc:"存操作前的值 可以是单值 或者业务结构体json字符串"`
	ValueAfter  string          `json:"valueAfter"  dc:"操作后的值 可以是单值 或者业务结构体json字符串"`
	DiffOld     string          `json:"diffOld"     description:"修改前变动的值"`
	DiffNew     string          `json:"diffNew"     description:"修改后变动的值"`
	UserIDs     []uint          `json:"userIDs"  dc:"修改影响的用户"`
	ExtendInfo  string          `json:"extendInfo"  dc:"扩展信息 eg：批量编辑影响的会员等"`
}

// AdminAdjustType 操作类型：1新增 2编辑 3删除 4批量编辑 5下载 6上传 7覆盖
type AdminAdjustType int

const (
	AdminAdjustTypeAdd       AdminAdjustType = 1
	AdminAdjustTypeEdit      AdminAdjustType = 2
	AdminAdjustTypeDel       AdminAdjustType = 3
	AdminAdjustTypeBatchEdit AdminAdjustType = 4
	AdminAdjustTypeDownload  AdminAdjustType = 5
	AdminAdjustTypeUpload    AdminAdjustType = 6
	AdminAdjustTypeCover     AdminAdjustType = 7
	AdminAdjustTypeBatchDel  AdminAdjustType = 8
)

var MapIdModifyPath = map[uint]string{
	1: "会员管理/玩家详情",
	2: "222",
}

// todo 后续web后台配置  写数据库
var MapIdNodeInfo = map[uint]*NodeInfo{
	1: &NodeInfo{
		Name:     "会员管理",
		Id:       1,
		ParentId: 0,
		Level:    1,
	},
	2: &NodeInfo{
		Name:     "会员列表",
		Id:       2,
		ParentId: 1,
		Level:    2,
	},
	3: &NodeInfo{
		Name:     "会员详情",
		Id:       3,
		ParentId: 1,
		Level:    2,
	},
	4: &NodeInfo{
		Name:     "所有玩家",
		Id:       4,
		ParentId: 2,
		Level:    3,
	},
	5: &NodeInfo{
		Name:     "玩家标签",
		Id:       5,
		ParentId: 2,
		Level:    3,
	},
}
