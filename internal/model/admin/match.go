package admin

type MatchMakeUrl struct {
	Id          string `json:"Id" cd:"id"`
	ColumnsId   uint   `json:"columnsId" cd:"栏目id"`
	CreateTime  int64  `json:"createTime" cd:"创建时间"`
	CompName    string `json:"compName" cd:"联赛名称"`
	UrlMode     int    `json:"urlMode" cd:"url模式"`
	UrlContent  string `json:"urlContent" cd:"url内容"`
	ModuleId    int    `json:"moduleId" cd:"模块id"`
	UrlCategory int
	SportId     int
}

type TeamMakeUrl struct {
	Id         int    `json:"id" dc:"id"`
	NameEn     string `json:"nameEn"         dc:"英文名称"`
	UrlMode    int    `json:"urlMode" cd:"url模式"`
	UrlContent string `json:"urlContent" cd:"url内容"`
}
