package admin

import (
	"github.com/shopspring/decimal"
)

type UserListSearchExt struct {
	Accounts    *[]string `json:"accounts" dc:"账号数组"`
	AccountLike *string   `json:"accountLike" dc:"账号"`
	// LastSigninIp       *string   `json:"lastSigninIp" dc:"最后登录ip"`
	// LastSigninDeviceId *string   `json:"lastSigninDeviceId" dc:"最后登录设备号"`
	// AlipayAccountNum   *string   `json:"alipayAccountNum" dc:"支付宝账号"`
	BankcardAccountNum *string `json:"bankcardAccountNum" dc:"银行卡账号"`
	CoinAddress        *string `json:"coinAddress" dc:"虚拟币地址"`
	// HasBindPhoneNum    *int    `json:"hasBindPhoneNum" dc:"是否绑定手机号:0未绑定，1已绑定"`
	LabelTitle      *string `json:"labelTitle" dc:"会员标签内容"`
	CreateTimeStart *int64  `json:"createTimeStart" dc:"注册时间开始"`
	CreateTimeEnd   *int64  `json:"createTimeEnd" dc:"注册时间结束"`
	// BindRealNameTimeStart *int64    `json:"bindRealNameTimeStart" dc:"姓名绑定时间开始"`
	// BindRealNameTimeEnd   *int64    `json:"bindRealNameTimeEnd" dc:"姓名绑定时间结束"`
	// BindPhoneNumTimeStart *int64    `json:"bindPhoneNumTimeStart" dc:"手机号绑定时间开始"`
	// BindPhoneNumTimeEnd   *int64    `json:"bindPhoneNumTimeEnd" dc:"手机号绑定时间结束"`
}

type UserAccount struct {
	Id        uint   `json:"id"`
	Account   string `json:"account"`
	ProductId uint   `json:"productId"`
}

type AccountProductInput struct {
	Accounts []string `json:"accounts" dc:"账号数组"`
}

type UserOneInput struct {
	Id      uint   `json:"id"`
	Account string `json:"account"`
}

type UserBriefVo struct {
	Id uint `json:"id"                    description:""`
	// ProductId     uint   `json:"productId"             description:"产品id"`
	PhoneNum              string          `json:"phoneNum"              description:"手机号"`
	Email                 string          `json:"email"                 description:"邮箱地址"`
	VipLevel              int             `json:"vipLevel"              description:"vip等级"`
	LevelId               uint            `json:"levelId"               description:"会员层级id"`
	BindPhoneTime         int64           `json:"bindPhoneTime"         description:"手机号绑定时间"`
	BindEmailTime         int64           `json:"bindEmailTime"         description:"邮箱绑定时间"`
	Status                int             `json:"status"                description:"会员状态：1普通，2优质，3关注 "`
	IsOnline              int             `json:"isOnline"              description:"是否在线：0否，1是"`
	IsProhibit            int             `json:"isProhibit"            description:"提取状态：1 正常 2 禁提"`
	IsBanned              int             `json:"isBanned"              description:"账号状态：1 正常 2 封号"`
	TotalWithdraw         decimal.Decimal `json:"totalWithdraw"         description:"总提现数量"`
	TotalRecharge         decimal.Decimal `json:"totalRecharge"         description:"总充值数量"`
	RechargeWithdrawRatio decimal.Decimal `json:"rechargeWithdrawRatio" description:"充提比"`

	Address  string `json:"address"               description:"住址"`
	Birthday string `json:"birthday"              description:"生日"`

	SignupIpRegion       string          `json:"signupIpRegion"        description:"注册来源 IP地理区域"`
	LastSignInIp         string          `json:"lastSignInIp"          description:"最后登录ip"`
	LastSigninRegion     string          `json:"lastSigninRegion"      description:"登录地点"`
	SigninCount          int             `json:"signinCount"           description:"登录次数"`
	OnlineDuration       int64           `json:"onlineDuration"        description:"在线时长（单位 毫秒）"`
	OnlineDurationInHour decimal.Decimal `json:"onlineDurationInHour"   description:"在线时长（单位 小时）"`

	Account string `json:"account"               description:"账号"`
	// Password string  `json:"password"              description:"密码"`
	Nickname string  `json:"nickname"              description:"昵称"`
	Avatar   string  `json:"avatar"                description:"头像url"`
	AreaCode string  `json:"areaCode"              description:"手机国际区号，如：86"`
	LabelIDs []int64 `json:"labels"        description:"用户标签ID"`

	Currency            string `json:"currency"              description:"币种"`
	OddsType            string `json:"oddsType"              description:"盘口类型（Euro欧洲盘，HongKong香港盘,Malay马来盘，Indo印尼盘）"`
	OddsChange          int    `json:"oddsChange"            description:"1:不接受赔率变动,2:接收最优赔率变动,3:接收任意赔率变动""`
	HandicapDisplayType int    `json:"handicapDisplayType"   description:"盘口显示方式：1纵向显示，2横向显示"`
	// WPassword           string `json:"wPassword"             description:"提现密码"`
	// SecurityPassword    string `json:"securityPassword"      description:"安全密码，修改个人绑定信息时要验证"`
	LastSigninTime int64 `json:"lastSigninTime"        description:"最后一次登录时间"`
	// LastSignInIp          string `json:"lastSignInIp"          description:"最后登录ip"`
	LastSignInDeviceId    string `json:"lastSignInDeviceId"    description:"最后登录设备号"`
	CreateAccount         string `json:"createAccount"         description:"创建者账号"`
	CreateType            int    `json:"createType"            description:"创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）"`
	CountryId             int    `json:"countryId"  description:"国家id"`
	SignupIp              string `json:"signupIp"              description:"注册ip"`
	SignupDeviceId        string `json:"signupDeviceId"        description:"注册设备号（设备指纹）"`
	SignupDeviceOs        string `json:"signupDeviceOs"        description:"注册设备系统（android,ios,windows,mac,...）"`
	SignupDeviceOsVersion string `json:"signupDeviceOsVersion" description:"注册设备系统版本号"`
	SignupDeviceType      int    `json:"signupDeviceType"      description:"注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）"`
	SignupAppType         int    `json:"signupAppType"         description:"注册应用类型 （1:android  2: ios，3:h5，4:web，5:其他）"`
	SignupAppVersion      string `json:"signupAppVersion"      description:"注册应用类型版本号"`
	SignupHost            string `json:"signupHost"            description:"注册域名"`
	UpdateAccount         string `json:"updateAccount"         description:"更新者账号"`
	UpdateType            int    `json:"updateType"            description:"更新者来源"`
	InviteCode            string `json:"inviteCode"            description:"邀请码（选填）"`
	CreateTime            int64  `json:"createTime"            description:"创建时间（注册时间）"`
	UpdateTime            int64  `json:"updateTime"            description:"更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）"`
	State                 string `json:"state"                 description:"用户所在的州 州包含多个Region"`
	Region                string `json:"region"                description:"用户设置的地理区域"`
	Gender                string `json:"gender"                description:"性别：0未知 1男 2女"`
	DeviceTokenIos        string `json:"deviceTokenIos"        description:"IOS推送token"`
	DeviceTokenAndroid    string `json:"deviceTokenAndroid"    description:"android推送token(FCM)"`
	Version               int    `json:"version"               description:"该记录的版本号"`

	RealName         string `json:"realName"              description:"真实姓名"`
	BindRealNameTime int64  `json:"bindRealNameTime"      description:"真实姓名绑定时间"`
	IdentityCard     string `json:"identityCard"          description:"身份证号码"`
	IdentityCardImgs string `json:"identityCardImgs"      description:"身份证图片[\"url\",\"url\"]"`
	Wechat           string `json:"wechat"                description:"微信号"`
	Language         string `json:"language"              description:"语言"`
	YearOfBirth      int    `json:"yearOfBirth"           description:"出生年"`
	MonthOfBirth     int    `json:"monthOfBirth"          description:"出生月"`
	DayOfBirth       int    `json:"dayOfBirth"            description:"出生日"`
	InviterId        uint   `json:"inviterId"             description:"邀请人id"`
}

type UserBankcardVO struct {
	BankName      string `json:"bankName"       description:"银行卡名称"`
	BankId        uint   `json:"bankId"        description:"银行id（所属银行）"`
	AccountNum    string `json:"accountNum"    description:"银行卡卡号（账号）"`
	SubBank       string `json:"subBank"       description:"分行名称（默认为空字符串）"`
	Title         string `json:"title"         description:"别名"`
	CreateTime    int64  `json:"createTime"    description:"添加时间"`
	CreateAccount string `json:"createAccount" description:"添加者账号"`
}

type UserCoinAddressVO struct {
	Currency      string `json:"currency"      description:"币种"`
	Protocol      string `json:"protocol"      description:"协议"`
	Title         string `json:"title"         description:"别名"`
	Address       string `json:"address"       description:"钱包地址"`
	CreateTime    int64  `json:"createTime"    description:"添加时间"`
	CreateAccount string `json:"createAccount" description:"添加者账号"`
}

type UserRemarkVO struct {
	CreateTime    int64  `json:"createTime"    description:"日期 创建时间"`
	CreateAccount string `json:"createAccount" description:"操作员"`
	Content       string `json:"content"       description:"备注内容"`
	UpdateTime    int64  `json:"updateTime"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" description:"更新者"`
}

type UserNewSignUpStat struct {
	AppType      int             `json:"appType"         description:"渠道类型"`
	NewDevice    int             `json:"newDevice"             description:"新增访问"`
	NewUser      int             `json:"newUser"             description:"新增人数"`
	NewPayUser   uint            `json:"newPayUser"          description:"新注册首存人数"`
	NewPayAmount decimal.Decimal `json:"newPayAmount"        description:"新注册充值金额"`
}

type UserPayStat struct {
	AppType             int             `json:"appType"         description:"渠道类型"`
	TotalRechargeUser   int             `json:"totalRechargeUser"   description:"总充值人数"`
	TotalRechargeAmount decimal.Decimal `json:"totalRechargeAmount" description:"总充值"`
	TotalWithdrawUser   int             `json:"totalWithdrawUser"   description:"总提现人数"`
	TotalWithdrawAmount decimal.Decimal `json:"totalWithdrawAmount" description:"总提现"`
}

type UserLoginedUserStat struct {
	AppType  int `json:"appType"         description:"渠道类型"`
	LoginCnt int `json:"loginCnt"            description:"登录人数"`
}

type UserSearchInput struct {
	Id       uint   `json:"id" dc:"会员id"`
	Account  string `json:"account" dc:"账号名"`
	PhoneNum string `json:"phoneNum" dc:"手机号"`
	Email    string `json:"email" dc:"邮箱"`
}

type UserLevelVo struct {
	Id       uint   `json:"id"                    description:""`
	PhoneNum string `json:"phoneNum"              description:"手机号"`
	Email    string `json:"email"                 description:"邮箱地址"`
	Account  string `json:"account"               description:"账号"`
	Nickname string `json:"nickname"              description:"昵称"`
}

type UserBankVo struct {
	Id            uint   `json:"id"            description:"主键key"`
	Account       string `json:"account"        description:"游戏账号"`
	UserId        uint   `json:"userId"        description:"会员id"`
	BankId        uint   `json:"bankId"        description:"银行id（所属银行）"`
	AccountNum    string `json:"accountNum"    description:"银行卡卡号"`
	AccountName   string `json:"accountName"    description:"银行卡账号名称"`
	BankName      string `json:"bankName"    description:"银行卡名称"`
	CreateTime    int64  `json:"createTime"    description:"添加时间"`
	UpdateTime    int64  `json:"updateTime"    description:"更新时间"`
	CreateAccount string `json:"createAccount" description:"添加者账号"`
	UpdateAccount string `json:"updateAccount" description:"更新者账号"`
}

type UserBankLog struct {
	// Account    string `json:"account"        description:"游戏账号"`
	UserId      uint   `json:"userId"        description:"会员id"`
	BankId      uint   `json:"bankId"        description:"银行id（所属银行）"`
	AccountNum  string `json:"accountNum"    description:"银行卡卡号（账号）"`
	AccountName string `json:"accountName"    description:"银行卡账号名称"`
	BankName    string `json:"bankName"    description:"银行卡名称"`
}

type UserRelationVo struct {
	Id            uint   `json:"id"                    description:"用户信息"`
	Account       string `json:"account" dc:"会员账号"`
	PhoneNum      string `json:"phoneNum" dc:"手机号"`
	Email         string `json:"email"                 dc:"邮箱地址"`
	LoginDeviceId string `json:"loginDeviceId" dc:"设备号"`
	LoginIp       string `json:"loginIp" dc:"登录IP"`
	LoginAddr     string `json:"loginAddr" dc:"登录地址"`
	DeviceType    string `json:"deviceType" dc:"设备类型"`
	CreateTime    int64  `json:"createTime" dc:"登录时间"`
}

type UserRelationInfo struct {
	Id       uint   `json:"id"                    description:""`
	PhoneNum string `json:"phoneNum"              description:"手机号"`
	Email    string `json:"email"                 description:"邮箱地址"`
	Account  string `json:"account"               description:"账号"`

	LastSigninTime     int64  `json:"lastSigninTime"        description:"最后一次登录时间"`
	LastSignInIp       string `json:"lastSignInIp"          description:"最后登录ip"`
	LastSigninRegion   string `json:"lastSigninRegion"      description:"登录地点"`
	LastSignInDeviceId string `json:"lastSignInDeviceId"    description:"最后登录设备号"`

	SignupIp       string `json:"signupIp"              description:"注册ip"`
	SignupIpRegion string `json:"signupIpRegion"        description:"注册来源 IP地理区域"`
	SignupDeviceId string `json:"signupDeviceId"        description:"注册设备号（设备指纹）"`
	// SignupDeviceOs        string `json:"signupDeviceOs"        description:"注册设备系统（android,ios,windows,mac,...）"`
	// SignupDeviceOsVersion string `json:"signupDeviceOsVersion" description:"注册设备系统版本号"`
	SignupDeviceType int `json:"signupDeviceType"      description:"注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）"`
	SignupAppType    int `json:"signupAppType"         description:"注册应用类型 （1:android  2: ios，3:h5，4:web，5:其他）"`
	// SignupAppVersion      string `json:"signupAppVersion"      description:"注册应用类型版本号"`
	SignupHost string `json:"signupHost"            description:"注册域名"`
	InviteCode string `json:"inviteCode"            description:"邀请码（选填）"`
	CreateTime int64  `json:"createTime"            description:"创建时间（注册时间）"`
}

type PhoneEmailAccountInfo struct {
	Id       uint   `json:"id" dc:"会员id"`
	Account  string `json:"account" dc:"账号名"`
	PhoneNum string `json:"phoneNum" dc:"手机号"`
	Email    string `json:"email" dc:"邮箱"`
}

type ResolveBankAccountInput struct {
	UserBankcardId uint `json:"UserBankcardId"`
	ChannelId      uint `json:"channelId"`
	Again          bool `json:"Again"`
}

type OrderStat struct {
	UserId              uint            `json:"userId"              description:"用户id"`
	Amount              decimal.Decimal `json:"amount"              description:"订单金额（总）"`
	AmountMain          decimal.Decimal `json:"amountMain"          description:"主钱包订单金额"`
	AmountBonus         decimal.Decimal `json:"amountBonus"         description:"奖金订单金额"`
	AmountCoupon        decimal.Decimal `json:"amountCoupon"        description:"优惠券订单金额"`
	SettleAmount        decimal.Decimal `json:"settleAmount"        description:"结算派奖金额（有包括本金）"`
	CashoutAmount       decimal.Decimal `json:"cashoutAmount"       description:"提前结算总本金"`
	CashoutSettleAmount decimal.Decimal `json:"cashoutSettleAmount" description:"提前结算总派金（有包括提前结算本金）"`
	OCreateTime         int64           `json:"oCreateTime"         description:"订单创建时间"`
	AppType             int
	MapUserCnt          map[uint]struct{} //统计下注人数使用
}

type AccountRole struct {
	RoleLevel int    `json:"roleLevel" dc:"角色层级：1管理员 2站长 3站员"`
	SelfId    uint   `json:"selfId"`
	ParentId  uint   `json:"parentId"`
	ChildIds  []uint `json:"childIds"`
}
