package admin

type UrlSettingConfig struct {
	Category int                    `json:"category" dc:"页面类型(1:赛程栏目 2:录像栏目 3:新闻栏目 4:标签 5:新闻详情页 6:录像详情页 7:专题 8:单页)"`
	Body     []UrlSettingConfigBody `json:"body" dc:"内容"`
}

type UrlSettingConfigBody struct {
	Content   string `json:"content" dc:"内容"`
	IsDefault int    `json:"default" dc:"是否默认(1:是 2:否)"`
	Mode      int    `json:"mode"    dc:"模式(1:继承模式 2:根目录模式 3:自定义模式)"`
	Source    int    `json:"source" dc:"来源(0:基础库 1:分组 2:站点)"`
}

type CategoryByUrlRes struct {
	Category int
	ModuleId string
}

type UrlSetting struct {
	Category int    // 页面类型
	Tag      string // 页面标识
	ModuleId string // 页面对应的模块
	IsDetail bool   // 是否是详情页 后缀带.html
}
