package admin

import (
	"context"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/golang-jwt/jwt/v5"
)

type JWTD struct {
	Id uint   `json:"id"`
	Ty string `json:"ty"`
	jwt.RegisteredClaims
}

type FailId struct {
	Id  uint  `json:"id"`
	Err error `json:"err"`
}

type SelectOption struct {
	Label string `json:"label" dc:"显示名称"`
	Value any    `json:"value" dc:"值"`
}

type IsFieldValueExistInput struct {
	TableName  string `json:"tableName" dc:"表名"`
	FieldName  string `json:"fieldName" dc:"默认使用的字段名"`
	Value      *gvar.Var
	DeleteTime string `json:"deleteTime" dc:"是否使用删除时间"` // 空则不使用
}

type GenerateSupplierUniqueNumberInput struct {
	TableName          string
	FieldName          string
	DeleteTime         string
	GenerateNumberFunc func(ctx context.Context) string
}

type SeoRankTaskIdResponse struct {
	Errcode string        `json:"errcode"`
	Errmsg  string        `json:"errmsg"`
	Data    SeoRankTaskId `json:"data"`
}
type SeoRankTaskId struct {
	Taskid uint `json:"taskid"`
}

type SeoRankResponse struct {
	Errcode string              `json:"errcode"`
	Errmsg  string              `json:"errmsg"`
	Data    SeoRankResponseData `json:"data"`
}
type SeoRankResponseData struct {
	Taskid         uint          `json:"taskid"`
	KeywordMonitor []MonitorList `json:"keywordmonitor"`
}
type MonitorList struct {
	keyword      string     `json:"keyword"`
	SearchEngine string     `json:"search_engine"`
	Area         string     `json:"area"`
	Ranks        []RankList `json:"ranks"`
}
type RankList struct {
	SiteUrl    string `json:"site_url"`
	Rank       int    `json:"rank"`
	PageTitle  string `json:"page_title"`
	PageUrl    string `json:"page_url"`
	Top100     int    `json:"top100"`
	SiteWeight string `json:"site_weight"`
}

type SeoRankItemExport struct {
	Id           int64  `json:"id"        description:"id"`
	SearchEngine string `json:"search_engine"  description:"搜索引擎"`
	Url          string `json:"url"       description:"url"`
	Keywords     string `json:"keywords"  description:"关键词"`
	Area         string `json:"area"  description:"区域"`
	Rank         string `json:"rank"      description:" 排名"`
}

type DomainResolutionAddResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data uint   `json:"data"`
}
type DomainResolutionDeleteResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data string `json:"data"`
}
