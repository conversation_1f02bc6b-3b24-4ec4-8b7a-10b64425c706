package admin

type CategoryOneInput struct {
	Id  uint `json:"id" dc:"分类ID"`
	PId uint `json:"pId" dc:"父分类ID"`
}

type AttachmentCategoryCountInput struct {
	PIds []uint `json:"pIds" dc:"父分类ID"`
}

type AttachmentCategoryListInput struct {
	Id   uint   `json:"id"`
	Name string `json:"name"`
	Type int    `json:"type"`
	ListInput
}

type AttachmentCategoryListOutput struct {
	List []AttachmentCategory
	ListOutput
}

type AttachmentCategoryAddInput struct {
	Pid           uint   `json:"pid" dc:"父分类ID"`
	Type          int    `json:"type"`
	Name          string `json:"name" dc:"名称"`
	OrderBy       int    `json:"orderBy" dc:"排序值[值大排前]"`
	CreateAccount string `json:"createAccount" dc:"创建账户"`
}

type AttachmentCategoryEditInput struct {
	Id            uint   `json:"id" dc:"id"`
	Name          string `json:"name" dc:"名称"`
	OrderBy       *int   `json:"orderBy" dc:"排序值[值大排前]"`
	UpdateAccount string `json:"updateAccount" dc:"更新账户"`
}

type AttachmentCategoryDeleteInput struct {
	Ids           []uint `json:"ids" dc:"ids"`
	UpdateAccount string `json:"updateAccount" dc:"更新账户"`
}

type AttachmentCountInput struct {
	CategoryIds []uint `json:"categoryIds" dc:"分类ID"`
}

type AttachmentListInput struct {
	Id   uint   `json:"id"`
	Name string `json:"name"`
	Type int    `json:"type"`
	ListInput
}

type AttachmentListOutput struct {
	List []Attachment
	ListOutput
}

type AttachmentAddInput struct {
	CategoryId    uint   `json:"categoryId" dc:"分类ID"`
	Name          string `json:"name" dc:"名称"`
	Key           string `json:"key" dc:"对象Key"`
	Size          int64  `json:"size" dc:"文件大小"`
	Type          int    `json:"type" dc:"类型[1 图片, 2视频]"`
	CreateAccount string `json:"createAccount" dc:"创建账户"`
}

type AttachmentEditInput struct {
	Ids           []uint `json:"ids" dc:"id数组"`
	CategoryId    uint   `json:"categoryId" dc:"分类ID"`
	Name          string `json:"name" dc:"名称"`
	UpdateAccount string `json:"updateAccount" dc:"更新账户"`
}

type AttachmentDeleteInput struct {
	Ids           []uint `json:"ids" dc:"ids"`
	UpdateAccount string `json:"updateAccount" dc:"更新账户"`
}
