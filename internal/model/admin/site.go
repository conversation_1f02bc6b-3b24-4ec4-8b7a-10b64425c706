package admin

import entity "gtcms/internal/model/entity/admin"

type SiteItem struct {
	//*entity.Site
	TmplName          string                          `json:"tmplName"          dc:"模版名称"`
	DomainName        string                          `json:"domainName" dc:"域名"`
	GroupName         string                          `json:"groupName"         dc:"组名"`
	TemplatePath      string                          `json:"templatePath"`
	SitemapSuffix     string                          `json:"sitemapSuffix"`
	OldViews          int                             `json:"oldViews"`
	RefererList       []*entity.SelectorConfigContent `json:"refererList"`
	PublicWelfareList []*entity.SelectorConfigContent `json:"publicWelfareList"`
}

type SiteItemExport struct {
	Id                uint   `json:"id"                  orm:"id"                     description:""`
	Name              string `json:"name"                  orm:"name"                      description:"名称"`
	Language          string `json:"language"              orm:"language"                  description:"语言"`
	GroupId           uint   `json:"groupId"               orm:"group_id"                  description:"组id"`
	DomainName        string `json:"domainName"        dc:"域名"`
	Status            int    `json:"status"                orm:"status"                    description:"状态(1:启用 2:关闭)"`
	TmplId            uint   `json:"tmplId"                orm:"tmpl_id"                   description:"模版id"`
	TdkId             uint   `json:"tdkId"                 orm:"tdk_id"                    description:"对应tdk_tmpl表的id"`
	UrlId             uint   `json:"urlId"                 orm:"url_id"                    description:"对应url_settting表的id"`
	SeoTitle          string `json:"seoTitle"              orm:"seo_title"                 description:"首页title"`
	SeoKeyword        string `json:"seoKeyword"            orm:"seo_keyword"               description:"首页关键词"`
	SeoDesc           string `json:"seoDesc"               orm:"seo_desc"                  description:"首页描述"`
	Remark            string `json:"remark"                orm:"remark"                    description:"备注"`
	Icp               string `json:"icp"                   orm:"icp"                       description:"备案号"`
	Picp              string `json:"picp"                  orm:"picp"                      description:"公安备案号"`
	StatisticsCode    string `json:"statisticsCode"        orm:"statistics_code"           description:"统计代码"`
	BaiduPush         string `json:"baiduPush"             orm:"baidu_push"                description:"百度推送"`
	ShenmaPushAccount string `json:"shenmaPushAccount"     orm:"shenma_push_account"       description:"神马推送-站长平台账号"`
	ShenmaPushAuthkey string `json:"shenmaPushAuthkey"     orm:"shenma_push_authkey"       description:"神马推送-域名authkey"`
	ToutiaoPush       string `json:"toutiaoPush"           orm:"toutiao_push"              description:"今日头条推送"`
	Logo              string `json:"logo"                  orm:"logo"                      description:"站点logo"`
	Favicon           string `json:"favicon"               orm:"favicon"                   description:"网站图标"`
	BaiduVerifyCode   string `json:"baiduVerifyCode" dc:"百度站长平台验证码"`
	GoogleVerifyCode  string `json:"googleVerifyCode" dc:"谷歌站长平台验证码"`
	MotherId          uint   `json:"motherId" orm:"mother_id" description:"母站id"`
}

type IncludeResponse struct {
	Baidu  uint `json:"baidu"`
	Sougou uint `json:"sougou"`
}
