package admin

type GroupInfo struct {
	Id   uint   `json:"id"                description:"组id"`
	Name string `json:"name"         description:"分组名称"`
}

type GroupSiteLink struct {
	Id        uint   `json:"id"                description:""`
	GroupId   uint   `json:"groupId"           description:"组id"`
	GroupName string `json:"groupName"         description:"分组名称"`
	SiteId    uint   `json:"siteId"           description:"站点id"`
	SiteName  string `json:"siteName"         description:"站点名称"`
	Creater   uint   `json:"creater"           description:"创建者id"`
}
