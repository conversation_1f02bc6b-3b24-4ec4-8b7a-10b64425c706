// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserSignupLog is the golang structure of table user_signup_log for DAO operations like Where/Data.
type UserSignupLog struct {
	g.Meta          `orm:"table:user_signup_log, do:true"`
	Id              interface{} //
	UserId          interface{} // 关联 user 表主键
	Ip              interface{} // 注册ip
	IpRegion        interface{} // 注册IP地理区域
	DeviceId        interface{} // 注册设备号（设备指纹）
	DeviceOs        interface{} // 注册设备系统（android,ios,windows,mac,...）
	DeviceOsVersion interface{} // 注册设备系统版本号
	DeviceType      interface{} // 注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）
	AppType         interface{} // 应用类型（1:android  2: ios，3:h5，4:web，5:其他）
	AppVersion      interface{} // 注册应用类型版本号
	CreateTime      interface{} // 创建时间（注册时间）
	UpdateTime      interface{} // 更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）
}
