// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserSession is the golang structure of table user_session for DAO operations like Where/Data.
type UserSession struct {
	g.Meta         `orm:"table:user_session, do:true"`
	Id             interface{} // 会话记录ID
	SessionId      interface{} // 会话唯一ID（绑定 JWT 的 sid 字段）
	SecretKey      interface{} // refresh token的密钥
	UserId         interface{} // 会员ID
	LoginTime      interface{} // 登录时间（时间戳）
	ExpireTime     interface{} // 会话过期时间（时间戳）
	LogoutTime     interface{} // 登出时间（0表示未登出）
	Ip             interface{} // 登录IP
	DeviceId       interface{} // 设备ID
	DeviceOs       interface{} // 设备系统
	DeviceType     interface{} // 设备类型（mobile/desktop/pad等）
	AppType        interface{} // 应用类型（android/ios/h5/web等）
	AppVersion     interface{} // 应用版本
	PushToken      interface{} // 推送token，对应 Android（如 FCM）和 iOS（如 APNs）系统
	IsOnline       interface{} // 是否在线（1=在线，0=离线）
	IsKicked       interface{} // 是否被踢下线（1=是，0=否）
	LastActiveTime interface{} // 最近活跃时间（如访问接口时间）
}
