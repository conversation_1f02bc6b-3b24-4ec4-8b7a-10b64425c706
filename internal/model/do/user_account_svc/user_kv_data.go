// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserKvData is the golang structure of table user_kv_data for DAO operations like Where/Data.
type UserKvData struct {
	g.Meta     `orm:"table:user_kv_data, do:true"`
	Id         interface{} // 唯一标识，自增主键
	UserId     interface{} // 所属用户的唯一 ID
	KeyPath    interface{} // 以 dot 分隔的键路径，例如 "profile.theme.color"
	ValueData  interface{} // 对应键的 JSON 数据值，支持任意结构
	ValueSize  interface{} // value_data 的 UTF-8 字节长度，用于配额限制
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 最后更新时间
}
