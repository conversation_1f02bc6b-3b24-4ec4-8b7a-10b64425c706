// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// VideoCategoryLanguages is the golang structure of table video_category_languages for DAO operations like Where/Data.
type VideoCategoryLanguages struct {
	g.Meta      `orm:"table:video_category_languages, do:true"`
	Id          interface{} // 主键ID
	CategoryId  interface{} // 分类ID
	LanguageId  interface{} // 语言ID：0-中文，1-英文，2-印尼语
	Name        interface{} // 分类名称
	Description interface{} // 分类描述
	CreateTime  interface{} // 创建时间(毫秒时间戳)
	UpdateTime  interface{} // 更新时间(毫秒时间戳)
	DeleteTime  interface{} // 删除时间
}
