// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// VideoPlaylistLanguages is the golang structure of table video_playlist_languages for DAO operations like Where/Data.
type VideoPlaylistLanguages struct {
	g.Meta      `orm:"table:video_playlist_languages, do:true"`
	Id          interface{} // 主键ID
	PlaylistId  interface{} // 播放列表ID
	LanguageId  interface{} // 语言ID：0-中文，1-英文，2-印尼语
	Name        interface{} // 播放列表名称
	ShortTitle  interface{} // 播放列表短标题
	Description interface{} // 播放列表描述
	CreateTime  interface{} // 创建时间(毫秒时间戳)
	UpdateTime  interface{} // 更新时间(毫秒时间戳)
	DeleteTime  interface{} // 删除时间
}
