// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// VideoCollects is the golang structure of table video_collects for DAO operations like Where/Data.
type VideoCollects struct {
	g.Meta     `orm:"table:video_collects, do:true"`
	Id         interface{} // 主键ID
	UserId     interface{} // 用户ID
	VideoId    interface{} // 视频ID
	CreateTime interface{} // 收藏时间(毫秒时间戳)
}
