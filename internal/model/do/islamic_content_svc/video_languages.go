// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// VideoLanguages is the golang structure of table video_languages for DAO operations like Where/Data.
type VideoLanguages struct {
	g.Meta      `orm:"table:video_languages, do:true"`
	Id          interface{} // 主键ID
	VideoId     interface{} // 视频ID
	LanguageId  interface{} // 语言ID：0-中文，1-英文，2-印尼语
	Title       interface{} // 视频标题
	Description interface{} // 视频描述(富文本)
	CreateTime  interface{} // 创建时间(毫秒时间戳)
	UpdateTime  interface{} // 更新时间(毫秒时间戳)
	DeleteTime  interface{} // 删除时间(毫秒时间戳)
}
