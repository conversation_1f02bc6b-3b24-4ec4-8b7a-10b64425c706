// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiLandmarkType is the golang structure of table haji_landmark_type for DAO operations like Where/Data.
type HajiLandmarkType struct {
	g.Meta     `orm:"table:haji_landmark_type, do:true"`
	Id         interface{} // 主键ID
	IconUrl    interface{} // 图标路径
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
