// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsArticleCollect is the golang structure of table news_article_collect for DAO operations like Where/Data.
type NewsArticleCollect struct {
	g.Meta      `orm:"table:news_article_collect, do:true"`
	Id          interface{} //
	UserId      interface{} // 用户id
	ArticleId   interface{} // article_id
	ArticleName interface{} // 名称
	CreateTime  interface{} // 创建时间（注册时间）
	UpdateTime  interface{} // 更新时间，0代表创建后未更新
}
