// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsArticle is the golang structure of table news_article for DAO operations like Where/Data.
type NewsArticle struct {
	g.Meta           `orm:"table:news_article, do:true"`
	Id               interface{} //
	IsZh             interface{} // 是否中文，0-否，1-是
	IsEn             interface{} // 是否英文，0-否，1-是
	IsId             interface{} // 是否印尼文，0-否，1-是
	CategoryId       interface{} // 分类id
	AdminId          interface{} // 分类负责人id
	CoverImgs        interface{} // 专题图片
	Creater          interface{} // 创建者id
	CreateName       interface{} // 后台创建者
	Author           interface{} // 创建人
	IsTop            interface{} // 是否加入头条，1启用，0关闭
	IsRecommend      interface{} // 是否推荐，1启用，0关闭
	IsPublish        interface{} // 是否发布，1启用，0关闭
	IsDraft          interface{} // 是否草稿状态，1是，0否
	CreateTime       interface{} // 创建时间
	PublishTime      interface{} // 发布时间
	UpdateTime       interface{} // 修改时间
	DeleteTime       interface{} // 删除时间
	AuthorLogo       interface{} // 创建人头像
	AuthorAuthStatus interface{} // 作者认证状态 0未认证 1已认证
	RecommendTime    interface{} // 加入推荐时间
	TopTime          interface{} // 加入头条时间
}
