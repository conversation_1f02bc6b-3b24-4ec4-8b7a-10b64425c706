// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsTopic is the golang structure of table news_topic for DAO operations like Where/Data.
type NewsTopic struct {
	g.Meta     `orm:"table:news_topic, do:true"`
	Id         interface{} //
	Counts     interface{} // 文章数量
	IsZh       interface{} // 是否中文，0-否，1-是
	IsEn       interface{} // 是否英文，0-否，1-是
	IsId       interface{} // 是否印尼文，0-否，1-是
	Status     interface{} // 是否显示，1启用，0关闭
	Sort       interface{} // 排序，数字越小，排序越靠前
	AdminId    interface{} // 分类负责人id
	TopicImgs  interface{} // 专题图片
	IsAppShow  interface{} // 是否app展示，1：是 0：否
	Creater    interface{} // 创建者id
	CreateName interface{} // 创建者
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 修改时间
	DeleteTime interface{} // 删除时间
}
