// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiLandmarkLanguages is the golang structure of table haji_landmark_languages for DAO operations like Where/Data.
type HajiLandmarkLanguages struct {
	g.Meta           `orm:"table:haji_landmark_languages, do:true"`
	Id               interface{} // 主键ID
	LandmarkId       interface{} // 地标ID，关联haji_landmark.id
	LanguageId       interface{} // 语言ID：0-中文，1-英文，2-印尼语
	LandmarkName     interface{} // 地标名称
	Country          interface{} // 国家/地区(后续使用google map api，应该是能获取到的，先使用手动输入的值吧)
	Address          interface{} // 详细地址
	ShortDescription interface{} // 简介
	InformationText  interface{} // 详细介绍
	CreateTime       interface{} // 创建时间（毫秒时间戳）
	UpdateTime       interface{} // 更新时间（毫秒时间戳）
}
