// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SuratDaftar is the golang structure of table surat_daftar for DAO operations like Where/Data.
type SuratDaftar struct {
	g.Meta      `orm:"table:surat_daftar, do:true"`
	Id          interface{} //
	Nomor       interface{} // 章节编号 (1-114)
	Nama        interface{} // 阿拉伯语章节名
	NamaLatin   interface{} // 拉丁化章节名
	JumlahAyat  interface{} // 经文数量
	TempatTurun interface{} // 降示地点
	Arti        interface{} // 章节含义
	Deskripsi   interface{} // 章节描述
	Audio       interface{} // 音频文件URL
	Status      interface{} // 状态标识
	IsPopular   interface{} // 是否热门章节 0否 1是
	CreatedTime interface{} // 创建时间戳(毫秒)
	UpdatedTime interface{} // 修改时间戳(毫秒)
}
