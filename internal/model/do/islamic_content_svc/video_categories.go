// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// VideoCategories is the golang structure of table video_categories for DAO operations like Where/Data.
type VideoCategories struct {
	g.Meta     `orm:"table:video_categories, do:true"`
	Id         interface{} // 主键ID
	VideoCount interface{} // 分类下视频数量
	Remark     interface{} // 备注
	CreateTime interface{} // 创建时间(毫秒时间戳)
	UpdateTime interface{} // 更新时间(毫秒时间戳)
	DeleteTime interface{} // 删除时间
}
