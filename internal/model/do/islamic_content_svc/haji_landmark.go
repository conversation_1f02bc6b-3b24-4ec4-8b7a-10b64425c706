// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiLandmark is the golang structure of table haji_landmark for DAO operations like Where/Data.
type HajiLandmark struct {
	g.Meta     `orm:"table:haji_landmark, do:true"`
	Id         interface{} // 主键ID
	TypeId     interface{} // 地标类型ID，关联haji_landmark_type.id
	InnerType  interface{} // 内部类型: (destinasi, tokoh)
	Latitude   interface{} // 纬度
	Longitude  interface{} // 经度
	ImageUrl   interface{} // 图片URL
	SortOrder  interface{} // 排序值，数字越小排序越靠前
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
