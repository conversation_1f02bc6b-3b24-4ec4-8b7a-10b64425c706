// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// BannerStats is the golang structure of table banner_stats for DAO operations like Where/Data.
type BannerStats struct {
	g.Meta     `orm:"table:banner_stats, do:true"`
	Id         interface{} // 主键ID
	BannerId   interface{} // 广告ID
	UserId     interface{} // 用户ID，0表示未登录用户
	DeviceId   interface{} // 设备唯一标识
	IpAddress  interface{} // IP地址
	UserAgent  interface{} // 用户代理信息
	CreateTime interface{} // 操作时间(毫秒时间戳)
}
