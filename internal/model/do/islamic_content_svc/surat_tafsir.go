// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SuratTafsir is the golang structure of table surat_tafsir for DAO operations like Where/Data.
type SuratTafsir struct {
	g.Meta      `orm:"table:surat_tafsir, do:true"`
	Id          interface{} //
	TafsirId    interface{} // 注释全局ID
	SurahId     interface{} // 所属章节ID
	AyatNomor   interface{} // 对应经文编号
	Tafsir      interface{} // 注释内容
	CreatedTime interface{} // 创建时间戳(毫秒)
	UpdatedTime interface{} // 修改时间戳(毫秒)
}
