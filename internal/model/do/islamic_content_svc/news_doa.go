// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsDoa is the golang structure of table news_doa for DAO operations like Where/Data.
type NewsDoa struct {
	g.Meta     `orm:"table:news_doa, do:true"`
	Id         interface{} //
	Name       interface{} // 名称
	Bacaans    interface{} // 数量
	CreateTime interface{} // 创建时间（注册时间）
	UpdateTime interface{} // 更新时间，0代表创建后未更新
}
