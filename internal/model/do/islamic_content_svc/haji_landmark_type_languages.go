// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiLandmarkTypeLanguages is the golang structure of table haji_landmark_type_languages for DAO operations like Where/Data.
type HajiLandmarkTypeLanguages struct {
	g.Meta     `orm:"table:haji_landmark_type_languages, do:true"`
	Id         interface{} // 主键ID
	TypeId     interface{} // 地标类型ID，关联haji_landmark_type.id
	LanguageId interface{} // 语言ID：0-中文，1-英文，2-印尼语
	TypeName   interface{} // 类型名称
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
