// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Keyword is the golang structure of table keyword for DAO operations like Where/Data.
type Keyword struct {
	g.Meta       `orm:"table:keyword, do:true"`
	Id           interface{} //
	Belong       interface{} // 所属(0:自身 1:分组 2:站点)
	BelongId     interface{} // 分组/站点id
	SelfId       interface{} // 自身id(如果belong非0,那这个指向的是id)
	Name         interface{} // 名称
	Url          interface{} // 链接
	Status       interface{} // 状态(1:启用 2:禁用)
	LinkProperty interface{} // 链接属性(1:新窗口打开 2:nofollow)
	Remark       interface{} // 备注
	CreateTime   interface{} // 创建时间
	UpdateTime   interface{} // 修改时间
	DeleteTime   interface{} // 删除时间
	Creater      interface{} // 创建者
}
