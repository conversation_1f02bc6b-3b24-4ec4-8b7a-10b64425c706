// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// LabelOtherRelation is the golang structure of table label_other_relation for DAO operations like Where/Data.
type LabelOtherRelation struct {
	g.Meta   `orm:"table:label_other_relation, do:true"`
	LabelId  interface{} // 标签id
	RelId    interface{} // 关联者id
	RelType  interface{} // 关联者类型(1:新闻资讯 2:足球比赛 3:篮球比赛 4:xxx......)
	Creater  interface{} // 创建者
	BelongId interface{} // 分组/站点id
	Belong   interface{} // 所属(0:自身 1:分组 2:站点)
}
