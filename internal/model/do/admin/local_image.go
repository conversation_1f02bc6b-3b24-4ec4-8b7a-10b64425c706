// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// LocalImage is the golang structure of table local_image for DAO operations like Where/Data.
type LocalImage struct {
	g.Meta     `orm:"table:local_image, do:true"`
	Table      interface{} // 表名
	Id         interface{} // 表中的id
	Column     interface{} // 表中字段名
	CreateTime interface{} //
	Url        interface{} //
	OldUrl     interface{} //
}
