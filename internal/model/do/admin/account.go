// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Account is the golang structure of table account for DAO operations like Where/Data.
type Account struct {
	g.Meta              `orm:"table:account, do:true"`
	Id                  interface{} //
	Account             interface{} // 帐号
	Password            interface{} // 密码
	NickName            interface{} // 昵称
	Contact             interface{} // 联系方式
	Remark              interface{} // 备注
	RoleId              interface{} // 角色id
	AuditPassword       interface{} // 私人密码
	IsOnline            interface{} // 1:在线 2:离线
	IsAffect            interface{} // 1:启用 2:停用
	LastSigninTime      interface{} // 上次登录时间
	CreateTime          interface{} // 创建时间
	CreateAccount       interface{} // 创建者
	UpdateTime          interface{} // 更新时间
	UpdateAccount       interface{} // 更新者
	DeleteTime          interface{} // 删除时间
	Creater             interface{} // 创建者
	IsRequireGoogleAuth interface{} // 是否谷歌验证码登录  1:需要 2:不用
	GoogleAuthSecret    interface{} // 谷歌验证秘钥
	TemplateIds         interface{} // 可访问的模板id集合
}
