// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Label is the golang structure of table label for DAO operations like Where/Data.
type Label struct {
	g.Meta       `orm:"table:label, do:true"`
	Id           interface{} //
	BelongId     interface{} // 分组/站点id
	SelfId       interface{} // 自身id(如果belong非0,那这个指向的是id)
	Belong       interface{} // 所属(0:自身 1:分组 2:站点)
	Name         interface{} // 名称
	Clicks       interface{} // 点击数
	RelNewsCount interface{} // 文章数
	Sort         interface{} // 排序
	IsUsed       interface{} // 是否常用(1:是 2:否)
	IsRecommend  interface{} // 是否推荐(1:是 2:否)
	Status       interface{} // 状态(1:显示 2:隐藏)
	SeoTitle     interface{} // seo标题
	SeoKeyword   interface{} // seo关键词
	SeoDesc      interface{} // seo描述
	Url          interface{} // 路由(唯一)
	Thumb        interface{} // 缩略图
	CreateTime   interface{} // 创建时间
	UpdateTime   interface{} // 更新时间
	DeleteTime   interface{} // 删除时间
	Creater      interface{} // 创建者
}
