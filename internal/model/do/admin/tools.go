// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Tools is the golang structure of table tools for DAO operations like Where/Data.
type Tools struct {
	g.Meta      `orm:"table:tools, do:true"`
	Id          interface{} //
	Name        interface{} // 工具名称
	Type        interface{} // 工具类别(关联数据配置)
	Label       interface{} // 工具标签
	Icon        interface{} // 图标
	SeoTitle    interface{} // seo标题
	SeoKeywords interface{} // seo关键字
	SeoDesc     interface{} // seo描述
	Url         interface{} // 自定义url
	CreateTime  interface{} // 创建时间
	UpdateTime  interface{} // 修改时间
	Creator     interface{} // 创建者
	Status      interface{} // 状态(1启用，2禁用)
	SeoList     interface{} // seo集合
	Ui          interface{} // 界面代码
}
