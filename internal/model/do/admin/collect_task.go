// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CollectTask is the golang structure of table collect_task for DAO operations like Where/Data.
type CollectTask struct {
	g.Meta        `orm:"table:collect_task, do:true"`
	Id            interface{} //
	Name          interface{} // 任务名，纯用来显示
	Symbol        interface{} // 唯一标识，用来在代码中区分唯一的任务
	RunDuration   interface{} // 任务执行间隔，单位秒， 0表示不可用
	Status        interface{} // 任务状态. 1开启， 2禁用，3因为子任务一直失败而卡住
	MaxRetry      interface{} // 失败后的最多重试次数 (0表示不进行重试
	UpdatedAt     interface{} // 更新时间，时间戳毫秒
	RunStatus     interface{} // 这个任务的当前上下文状态，在代码中自定义，是一个json
	Param         interface{} // 这个任务的一些参数，是一个json
	CurrentInstId interface{} // 当前正在执行的任务实例的id, 每次产生新任务前都要先检查一下当前任务是否已经完成
	RandDelay     interface{} // 任务执行随机延后rand(0,rand_delay) 秒，把任务执行打散，单位秒。
}
