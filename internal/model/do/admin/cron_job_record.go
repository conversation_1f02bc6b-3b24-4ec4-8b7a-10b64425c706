// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CronJobRecord is the golang structure of table cron_job_record for DAO operations like Where/Data.
type CronJobRecord struct {
	g.Meta   `orm:"table:cron_job_record, do:true"`
	Id       interface{} //
	Symbol   interface{} // cron_job表里的标识
	RunAt    interface{} // 执行时间
	Status   interface{} // 成功还是失败。 1成功
	Progress interface{} // 当前执行的状态
	Retry    interface{} // 这是第几次重试
}
