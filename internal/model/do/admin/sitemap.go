// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Sitemap is the golang structure of table sitemap for DAO operations like Where/Data.
type Sitemap struct {
	g.Meta             `orm:"table:sitemap, do:true"`
	Id                 interface{} //
	Belong             interface{} // 所属(1:分组 2:站点)
	BelongId           interface{} // 所属分组/站点id
	Format             interface{} // 格式(1:xml地图 2:txt地图 3:html地图)
	CreateType         interface{} // 生成方式(1:手动 2:自动)
	MainRefreshRate    interface{} // 首页更新频率(1:每天 2:每星期 3:每月)
	ListRefreshRate    interface{} // 列表页更新频率(1:每天 2:每星期 3:每月)
	ContentRefreshRate interface{} // 内容页更新频率(1:每天 2:每星期 3:每月)
	MainLevel          interface{} // 首页优先级别
	ListLevel          interface{} // 列表页优先级别
	ContentLevel       interface{} // 内容页优先级别
	Status             interface{} // 状态(1:启用 2:禁用)
	Remark             interface{} // 说明
	CreateTime         interface{} // 创建时间
	UpdateTime         interface{} // 修改时间
	DeleteTime         interface{} // 删除时间
	Creater            interface{} // 创建者
	LinkNum            interface{} // 每个地图文件内链最大数
}
