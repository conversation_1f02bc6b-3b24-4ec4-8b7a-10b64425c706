// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// RiskControlTab is the golang structure of table risk_control_tab for DAO operations like Where/Data.
type RiskControlTab struct {
	g.Meta        `orm:"table:risk_control_tab, do:true"`
	Id            interface{} //
	TabType       interface{} // 栏目分类[1 IP白名单, 2 IP黑名单, 3 UA白名单, 4 UA黑名单,  5 地区屏蔽,  6 CC防御]
	TabName       interface{} // 栏目名称
	IsOpen        interface{} // 状态 [ 1 开 2 关]
	IsPass        interface{} // 类型[ 1 白名单命中通过 2 黑名单命中不通过]
	IsList        interface{} // 栏目内容 [ 1 列表  2 功能配置 ]
	Metas         interface{} // 功能属性：tab_type = 5时 对应 地区屏蔽配置 格式：    {"BlockUserSwitch": 1, "BlockSpiderSwitch": 1}             屏蔽用户：1开 2关     屏蔽蜘蛛：1开 2关 ；  tab_type = 6时 对应 CC防御配置  格式要求如下： {"DefenseSwitch": 1, "TriggerFrequency": 1}    防御开关 ：1开 2关     触发频率：90-150左右
	Priority      interface{} // 访问优先级
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
	Creater       interface{} // 创建者
}
