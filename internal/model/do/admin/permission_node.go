// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// PermissionNode is the golang structure of table permission_node for DAO operations like Where/Data.
type PermissionNode struct {
	g.Meta  `orm:"table:permission_node, do:true"`
	Id      interface{} //
	PId     interface{} // 父权限id（顶级为0）
	Name    interface{} // 菜单编号（权限编号）
	Label   interface{} // 显示名称（中），逗号分隔
	OrderBy interface{} // 排序
}
