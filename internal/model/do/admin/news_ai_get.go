// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsAiGet is the golang structure of table news_ai_get for DAO operations like Where/Data.
type NewsAiGet struct {
	g.Meta     `orm:"table:news_ai_get, do:true"`
	Id         interface{} //
	Title      interface{} // 标题
	Content    interface{} // 文章正文
	CreateTime interface{} //
	UpdateTime interface{} //
	Type       interface{} // 类型。 1足球，2篮球
	IsPass     interface{} // 审核状态(1通过 2不通过)
}
