// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// RiskControlContent is the golang structure of table risk_control_content for DAO operations like Where/Data.
type RiskControlContent struct {
	g.Meta        `orm:"table:risk_control_content, do:true"`
	Id            interface{} //
	TabType       interface{} // 分类 [1 IP白名单, 2 IP黑名单, 3 UA白名单, 4 UA黑名单 ； 不包含 5 地区屏蔽,  6 CC防御]
	Content       interface{} // （1） tab_type = 1时 对应 IP白名单地址；  （2） tab_type = 2时 对应 IP黑名单地址；  （3） tab_type = 3时 对应 UA白名单配置；  （4） tab_type = 4时 对应 UA黑名单配置
	IsOpen        interface{} // 状态 [ 1 开 2 关]
	Remark        interface{} // 备注
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
	Creater       interface{} // 创建者
}
