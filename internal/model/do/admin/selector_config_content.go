// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SelectorConfigContent is the golang structure of table selector_config_content for DAO operations like Where/Data.
type SelectorConfigContent struct {
	g.Meta        `orm:"table:selector_config_content, do:true"`
	Id            interface{} //
	SelectType    interface{} // 选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]
	Title         interface{} // 选择器类型对应的配置名称
	IsOpen        interface{} // 状态 [ 1 启用  2 禁用]
	Sort          interface{} // 排序
	Remark        interface{} // 备注
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
	Creater       interface{} // 创建者
	Extra         interface{} // 额外数据：如注册商底下的平台账号[{"account":"xx"}]
}
