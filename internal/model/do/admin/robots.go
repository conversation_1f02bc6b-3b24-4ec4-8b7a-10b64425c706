// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Robots is the golang structure of table robots for DAO operations like Where/Data.
type Robots struct {
	g.Meta     `orm:"table:robots, do:true"`
	Id         interface{} //
	Name       interface{} // name
	Content    interface{} // content
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 修改时间
	DeleteTime interface{} // 删除时间
	Creater    interface{} // 创建者
}
