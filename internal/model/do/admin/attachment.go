// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Attachment is the golang structure of table attachment for DAO operations like Where/Data.
type Attachment struct {
	g.Meta        `orm:"table:attachment, do:true"`
	Id            interface{} //
	CategoryId    interface{} // 分类id
	Name          interface{} // 名称
	Key           interface{} // 对象key
	Size          interface{} // 大小(单位字节)
	Type          interface{} // 类型[1  图片 2视频]
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
}
