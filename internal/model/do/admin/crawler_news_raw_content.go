// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CrawlerNewsRawContent is the golang structure of table crawler_news_raw_content for DAO operations like Where/Data.
type CrawlerNewsRawContent struct {
	g.Meta        `orm:"table:crawler_news_raw_content, do:true"`
	Id            interface{} //
	Content       interface{} // 文章正文
	ContentFilter interface{} // 过滤标签后的内容
	IsFilter      interface{} // 是否过滤 0-否1-是
}
