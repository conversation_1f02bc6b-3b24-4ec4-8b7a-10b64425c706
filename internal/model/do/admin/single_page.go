// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SinglePage is the golang structure of table single_page for DAO operations like Where/Data.
type SinglePage struct {
	g.Meta        `orm:"table:single_page, do:true"`
	Id            interface{} //
	BelongGroupId interface{} // 所属分组id
	BelongSiteId  interface{} // 所属站点id
	Name          interface{} // 页面名称
	Url           interface{} // url
	Desc          interface{} // 简介
	Status        interface{} // 状态(1:启用 2:禁用)
	Sort          interface{} // 排序
	SeoTitle      interface{} // seo标题
	SeoKeyword    interface{} // seo关键词
	SeoDesc       interface{} // seo描述
	Thumb         interface{} // 缩略图
	Banner        interface{} // banner图
	Type          interface{} // 类型(1:关于我们 2:联系我们)
	CreateTime    interface{} // 创建时间
	UpdateTime    interface{} // 修改时间
	DeleteTime    interface{} // 删除时间
	Creater       interface{} // 创建者
}
