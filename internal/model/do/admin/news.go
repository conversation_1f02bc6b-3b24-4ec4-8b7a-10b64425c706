// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// News is the golang structure of table news for DAO operations like Where/Data.
type News struct {
	g.Meta        `orm:"table:news, do:true"`
	Id            interface{} //
	BelongColId   interface{} // 所属栏目id
	BelongColName interface{} // 所属栏目名称
	BelongGroupId interface{} // 所属分组
	BelongSiteId  interface{} // 所属站点
	Title         interface{} // 标题
	Desc          interface{} // 简介
	Label         interface{} // 标签
	SeoTitle      interface{} // seo标题
	SeoKeyword    interface{} // seo关键词
	SeoDesc       interface{} // seo描述
	FileName      interface{} // 自定义文件名
	Author        interface{} // 作者
	Resource      interface{} // 来源
	Views         interface{} // 浏览量
	Sort          interface{} // 排序
	Status        interface{} // 状态(1:显示 2:隐藏)
	Attr          interface{} // 属性(1:视频)
	Thumb         interface{} // 缩略图
	CreateTime    interface{} // 发布时间
	UpdateTime    interface{} // 修改时间
	DeleteTime    interface{} // 删除时间
	Creater       interface{} // 创建者
	IsHot         interface{} // 是否热门（1是 2否）
	IsAuto        interface{} // 是否自动（1是0否）
	Pdf           interface{} // pdf查看链接
}
