// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// TdkTmpl is the golang structure of table tdk_tmpl for DAO operations like Where/Data.
type TdkTmpl struct {
	g.Meta           `orm:"table:tdk_tmpl, do:true"`
	Id               interface{} //
	Name             interface{} // 规则名称
	Belong           interface{} // 所属(1:分组 2:站点)
	BelongId         interface{} // 所属分组/站点id
	SelfId           interface{} // 自身id(如果belong非0,那这个指向的是id)
	TRemark          interface{} // 标题模版说明
	TConfig          interface{} // 标题模版内容
	KwRemark         interface{} // 关键词模版说明
	KwConfig         interface{} // 关键词模版内容
	DescRemark       interface{} // 描述模版说明
	DescConfig       interface{} // 描述模版内容
	Status           interface{} // 状态(1:启用 2:禁用)
	CreateTime       interface{} // 创建时间
	UpdateTime       interface{} // 修改时间
	DeleteTime       interface{} // 删除时间
	Creater          interface{} // 创建者
	IsDefault        interface{} // 是否默认(1:是 2:否)
	Demo             interface{} // 演示例子
	Language         interface{} // 语言
	OtherLanguageIds interface{} // 其它语言的tdk id
}
