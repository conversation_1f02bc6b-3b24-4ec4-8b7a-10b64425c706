// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CollectTaskInst is the golang structure of table collect_task_inst for DAO operations like Where/Data.
type CollectTaskInst struct {
	g.Meta     `orm:"table:collect_task_inst, do:true"`
	Id         interface{} //
	TaskId     interface{} // 关联 collect_task.id
	Symbol     interface{} // 关联 collect_task.symbol
	InstSymbol interface{} // 这个任务自己的标识
	RetryTimes interface{} // 已经重试几次了
	Status     interface{} // 1: 未完成， 2:已完成且成功， 3:执行失败，
	CreateTime interface{} // 创建时间,时间戳毫秒
	UpdateTime interface{} // 更新时间,时间戳毫秒
	RunStatus  interface{} // 本次任务执行的上下文信息，生成自collect_task.run_status
}
