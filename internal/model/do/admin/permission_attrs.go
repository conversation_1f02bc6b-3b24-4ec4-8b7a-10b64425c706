// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// PermissionAttrs is the golang structure of table permission_attrs for DAO operations like Where/Data.
type PermissionAttrs struct {
	g.Meta       `orm:"table:permission_attrs, do:true"`
	Id           interface{} //
	PermissionId interface{} // 权限id
	UrlPath      interface{} // api接口路径
	MaskedFields interface{} // 脱敏字段配置
	OrderBy      interface{} // 排序
}
