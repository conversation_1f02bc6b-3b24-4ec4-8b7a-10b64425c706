// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// StoreBaseConfig is the golang structure of table store_base_config for DAO operations like Where/Data.
type StoreBaseConfig struct {
	g.Meta                `orm:"table:store_base_config, do:true"`
	Id                    interface{} //
	Type                  interface{} // 存储类型[local 本地存储, aws AWS亚马逊云存储]
	ThumbnailLargeWidth   interface{} // 缩略大图宽
	ThumbnailLargeHeight  interface{} // 缩略大图高
	ThumbnailMediumWidth  interface{} // 缩略中图宽
	ThumbnailMediumHeight interface{} // 缩略中图高
	ThumbnailSmallWidth   interface{} // 缩略小图宽
	ThumbnailSmallHeight  interface{} // 缩略小图高
	IsOpenWatermark       interface{} // 是否开启水印： 1:开 2:关
	WatermarkType         interface{} // 水印类型： 1:图片 2:文字
	WatermarkContent      interface{} // 水印图片或水印文字
	WatermarkLocation     interface{} // 水印位置
	WatermarkOpacity      interface{} // 水印透明度
	WatermarkRotation     interface{} // 水印倾斜度
	WatermarkHorizontal   interface{} // 水印横坐标偏移量
	WatermarkVertical     interface{} // 水印纵坐标偏移量
	CreateTime            interface{} // 创建时间
	CreateAccount         interface{} // 创建者
	UpdateTime            interface{} // 更新时间
	UpdateAccount         interface{} // 更新者
	DeleteTime            interface{} // 删除时间
}
