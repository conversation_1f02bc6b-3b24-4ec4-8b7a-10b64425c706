// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SelectorConfigTab is the golang structure of table selector_config_tab for DAO operations like Where/Data.
type SelectorConfigTab struct {
	g.Meta        `orm:"table:selector_config_tab, do:true"`
	Id            interface{} //
	SelectType    interface{} // 选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]
	TabName       interface{} // 栏目名称
	IsOpen        interface{} // 状态 [ 1 开 2 关]
	IsList        interface{} // 栏目内容 [ 1 列表  2 功能配置 ]
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
	Creater       interface{} // 创建者
}
