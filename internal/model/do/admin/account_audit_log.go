// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AccountAuditLog is the golang structure of table account_audit_log for DAO operations like Where/Data.
type AccountAuditLog struct {
	g.Meta      `orm:"table:account_audit_log, do:true"`
	Id          interface{} //
	AdminId     interface{} // 管理员id
	Account     interface{} // 管理员账号
	LoginIp     interface{} // 登录IP
	NodeId      interface{} // 节点ID
	Path        interface{} // 操作路径  eg: 会员管理/会员列表/所有玩家
	Object      interface{} // 操作对象 eg：会员属性 | 黑名单 | 产品名称 |
	ModifyType  interface{} // 修改类型：1新增 2编辑 3删除 4批量编辑 5下载 6上传 7覆盖
	ModifyItem  interface{} // 操作属性  ；eg： 标签 | 状态 | 备注 | 踢下线 | 优质
	ValueBefore interface{} // 修改前记录
	ValueAfter  interface{} // 修改后记录
	DiffOld     interface{} // 修改前变动的值
	DiffNew     interface{} // 修改后变动的值
	ExtendInfo  interface{} // 扩展信息 eg：存放批量编辑影响的会员等
	CreateTime  interface{} // 创建时间
	Creater     interface{} // 创建者
}
