// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CmsLoginConfig is the golang structure of table cms_login_config for DAO operations like Where/Data.
type CmsLoginConfig struct {
	g.Meta        `orm:"table:cms_login_config, do:true"`
	Id            interface{} //
	IsOpen        interface{} // 后台开关： 1:开 2:关
	Title         interface{} // 后台名称
	Url           interface{} // 后台网址
	IsIpBind      interface{} // IP绑定： 1:开 2:关
	IsMacBind     interface{} // 机器码绑定： 1:开 2:关
	IsGoogleBind  interface{} // 谷歌验证码绑定： 1:开 2:关
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
	Creater       interface{} // 创建者
}
