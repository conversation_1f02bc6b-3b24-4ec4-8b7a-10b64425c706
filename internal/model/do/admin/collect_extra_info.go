// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CollectExtraInfo is the golang structure of table collect_extra_info for DAO operations like Where/Data.
type CollectExtraInfo struct {
	g.Meta       `orm:"table:collect_extra_info, do:true"`
	CategoryId   interface{} // 本seo属于哪个分类 1足球赛事 2足球球队 3足球球员 4篮球赛事 5篮球球队 6篮球球员 ...
	BelongId     interface{} // 属于对应的category中的哪个id的数据。比如当category=1时，它表示足球赛事id
	ViewCount    interface{} // 浏览量
	ReleaseTime  interface{} // 更新时间
	OrderWeight  interface{} // 排序权重
	Introduction interface{} // 简介(联赛、球队、球员)
	IsHot        interface{} // 是否热门(1是 2否)
	IsTop        interface{} // 是否置顶(1是 2否)
}
