// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CrawlerNewsRaw is the golang structure of table crawler_news_raw for DAO operations like Where/Data.
type CrawlerNewsRaw struct {
	g.Meta        `orm:"table:crawler_news_raw, do:true"`
	Id            interface{} //
	Source        interface{} // 来源 （比如新浪体育
	Docid         interface{} // 在原始来源处的唯一id
	Title         interface{} // 标题
	Url           interface{} // 原始链接
	Intro         interface{} // 摘要
	CompetitionId interface{} // 关联的赛事id,  为0表示还未关联
	SourceCate    interface{} // 在原始来源里的分类标识 （比如新浪就是media_name)
	CreateTime    interface{} //
	UpdateTime    interface{} //
	Content       interface{} // 文章正文
	Type          interface{} // 类型。 1足球，2篮球
	IsVideo       interface{} // 是否视频(1是 2否)
	Language      interface{} // 语言-cn中文 en英文
}
