// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AccountSiteLink is the golang structure of table account_site_link for DAO operations like Where/Data.
type AccountSiteLink struct {
	g.Meta        `orm:"table:account_site_link, do:true"`
	Id            interface{} //
	AccountId     interface{} // 账号id
	GroupId       interface{} // 组ID
	SiteId        interface{} // 站点id
	IsAffect      interface{} // 1:启用 2:停用
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	Creater       interface{} // 创建者ID
}
