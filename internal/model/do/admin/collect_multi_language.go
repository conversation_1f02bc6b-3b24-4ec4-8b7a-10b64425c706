// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CollectMultiLanguage is the golang structure of table collect_multi_language for DAO operations like Where/Data.
type CollectMultiLanguage struct {
	g.Meta      `orm:"table:collect_multi_language, do:true"`
	Table       interface{} // 表名
	Id          interface{} // 表中的id
	Column      interface{} // 表中字段名
	UpdatedAt   interface{} // 更新时间
	NameId      interface{} // 印尼语
	ShortNameId interface{} // 印尼语，去掉()"'"
	NameVi      interface{} // 越南语
	ShortNameVi interface{} // 越南语，去掉()"'"
}
