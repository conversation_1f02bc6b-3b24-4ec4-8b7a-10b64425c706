// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// RolePermissionConfig is the golang structure of table role_permission_config for DAO operations like Where/Data.
type RolePermissionConfig struct {
	g.Meta        `orm:"table:role_permission_config, do:true"`
	Id            interface{} //
	Name          interface{} // 角色名称
	RoleLevel     interface{} // 角色层级：1管理员 2站长 3站员
	Label         interface{} // 角色名称（中）
	PermissionSet interface{} // 权限集合：id数组
	MaskedFields  interface{} // 掩码字段 ：map结构{id: {k1:v1, k2:v2}}
	Remark        interface{} // 备注
	OrderBy       interface{} // 排序
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
}
