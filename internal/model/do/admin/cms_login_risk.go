// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CmsLoginRisk is the golang structure of table cms_login_risk for DAO operations like Where/Data.
type CmsLoginRisk struct {
	g.Meta        `orm:"table:cms_login_risk, do:true"`
	Id            interface{} //
	TabType       interface{} // 分类 [1 IP绑定管理, 2 机器码管理]
	Content       interface{} // （1） tab_type = 1时 对应 IP地址（2） tab_type = 2时 对应机器码
	IsOpen        interface{} // 状态 [ 1 开 2 关]
	Remark        interface{} // 备注
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
	Creater       interface{} // 创建者
}
