// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Sensitive is the golang structure of table sensitive for DAO operations like Where/Data.
type Sensitive struct {
	g.Meta        `orm:"table:sensitive, do:true"`
	Id            interface{} //
	ClassId       interface{} // 分类ID
	Word          interface{} // 敏感词
	IsOpen        interface{} // 是否启用：1开 2关
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
}
