// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Module is the golang structure of table module for DAO operations like Where/Data.
type Module struct {
	g.Meta `orm:"table:module, do:true"`
	Id     interface{} // 对应collect_xxx_competition id
	Name   interface{} // 名称
}
