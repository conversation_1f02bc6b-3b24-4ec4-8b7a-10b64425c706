// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsBody is the golang structure of table news_body for DAO operations like Where/Data.
type NewsBody struct {
	g.Meta     `orm:"table:news_body, do:true"`
	Id         interface{} //
	NewsId     interface{} // 新闻id
	Body       interface{} // 内容
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 修改时间
	DeleteTime interface{} // 删除时间
	Creater    interface{} // 创建者
}
