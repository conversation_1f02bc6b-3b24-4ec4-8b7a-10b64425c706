// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AccountNodeConfig is the golang structure of table account_node_config for DAO operations like Where/Data.
type AccountNodeConfig struct {
	g.Meta        `orm:"table:account_node_config, do:true"`
	Id            interface{} //
	Name          interface{} // 节点名称
	Level         interface{} // 层级
	ParentId      interface{} // 父id 第一层级为0
	ApiNode       interface{} // 对应的api接口名称
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
	Creater       interface{} // 创建者
}
