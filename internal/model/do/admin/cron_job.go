// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CronJob is the golang structure of table cron_job for DAO operations like Where/Data.
type CronJob struct {
	g.Meta    `orm:"table:cron_job, do:true"`
	Id        interface{} //
	Name      interface{} // 任务名，纯用来显示
	Symbol    interface{} // 唯一标识，用来在代码中区分唯一的任务
	Status    interface{} // 任务状态. 1开启， 2禁用，3因为子任务一直失败而卡住
	MaxRetry  interface{} // 失败后的最多重试次数 (0表示不进行重试
	UpdatedAt interface{} // 更新时间，时间戳毫秒
	Param     interface{} // 这个任务的一些参数，是一个json
	RandDelay interface{} // 任务执行随机延后rand(0,rand_delay) 秒，把任务执行打散，单位秒。
	Cron      interface{} // crontab string
	Progress  interface{} // 当前进度，自定义的json
}
