// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AccountLoginLog is the golang structure of table account_login_log for DAO operations like Where/Data.
type AccountLoginLog struct {
	g.Meta      `orm:"table:account_login_log, do:true"`
	Id          interface{} //
	AccountId   interface{} // 账户id
	AccountName interface{} // 账户名
	SigninTime  interface{} // 登录时间
	Ip          interface{} // 登录ip（ip6长度为39字符）
	IpRegion    interface{} // ip地址位置
	OperType    interface{} // 操作类型 1 登入 2 登出 3 修改密码
	Status      interface{} // 状态 1 成功 2 失败
	DeviceId    interface{} // 设备编号
	DeviceType  interface{} // chrome ie
	Creater     interface{} // 创建者
}
