// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SignalClickRecord is the golang structure of table signal_click_record for DAO operations like Where/Data.
type SignalClickRecord struct {
	g.Meta    `orm:"table:signal_click_record, do:true"`
	Id        interface{} //
	ProductId interface{} // 直播信号源id
	Clicks    interface{} // 当天点击量
	RecordDay *gtime.Time // 当天日期
}
