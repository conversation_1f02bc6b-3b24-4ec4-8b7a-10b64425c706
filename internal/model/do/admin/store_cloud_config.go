// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// StoreCloudConfig is the golang structure of table store_cloud_config for DAO operations like Where/Data.
type StoreCloudConfig struct {
	g.Meta        `orm:"table:store_cloud_config, do:true"`
	Id            interface{} //
	Type          interface{} // 存储类型[local 本地存储, aws AWS亚马逊云存储]
	Config        interface{} // 配置
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
}
