// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AttachmentCategory is the golang structure of table attachment_category for DAO operations like Where/Data.
type AttachmentCategory struct {
	g.Meta        `orm:"table:attachment_category, do:true"`
	Id            interface{} //
	Pid           interface{} // 父分类
	Type          interface{} // 类型[1  图片 2视频]
	Name          interface{} // 名称
	OrderBy       interface{} // 排序值(值大排前)
	CreateTime    interface{} // 创建时间
	CreateAccount interface{} // 创建者
	UpdateTime    interface{} // 更新时间
	UpdateAccount interface{} // 更新者
	DeleteTime    interface{} // 删除时间
}
