// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CrawlerNewsComment is the golang structure of table crawler_news_comment for DAO operations like Where/Data.
type CrawlerNewsComment struct {
	g.Meta        `orm:"table:crawler_news_comment, do:true"`
	Id            interface{} //
	Source        interface{} // 来源 （比如新浪体育
	Mid           interface{} // 消息id
	Docid         interface{} // 在原始来源处的唯一id
	Content       interface{} // 评论内容
	CompetitionId interface{} // 关联的赛事id,  为0表示还未关联
	Lid           interface{} // 关联的赛事id,  为0表示还未关联
	CreateTime    interface{} //
	UpdateTime    interface{} //
	Type          interface{} // 类型。 1足球，2篮球
}
