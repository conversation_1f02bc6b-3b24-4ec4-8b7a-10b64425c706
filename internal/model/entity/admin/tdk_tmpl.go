// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// TdkTmpl is the golang structure for table tdk_tmpl.
type TdkTmpl struct {
	Id               uint   `json:"id"               orm:"id"                 description:""`
	Name             string `json:"name"             orm:"name"               description:"规则名称"`
	Belong           int    `json:"belong"           orm:"belong"             description:"所属(1:分组 2:站点)"`
	BelongId         uint   `json:"belongId"         orm:"belong_id"          description:"所属分组/站点id"`
	SelfId           uint   `json:"selfId"           orm:"self_id"            description:"自身id(如果belong非0,那这个指向的是id)"`
	TRemark          string `json:"tRemark"          orm:"t_remark"           description:"标题模版说明"`
	TConfig          string `json:"tConfig"          orm:"t_config"           description:"标题模版内容"`
	KwRemark         string `json:"kwRemark"         orm:"kw_remark"          description:"关键词模版说明"`
	KwConfig         string `json:"kwConfig"         orm:"kw_config"          description:"关键词模版内容"`
	DescRemark       string `json:"descRemark"       orm:"desc_remark"        description:"描述模版说明"`
	DescConfig       string `json:"descConfig"       orm:"desc_config"        description:"描述模版内容"`
	Status           int    `json:"status"           orm:"status"             description:"状态(1:启用 2:禁用)"`
	CreateTime       int64  `json:"createTime"       orm:"create_time"        description:"创建时间"`
	UpdateTime       int64  `json:"updateTime"       orm:"update_time"        description:"修改时间"`
	DeleteTime       int64  `json:"deleteTime"       orm:"delete_time"        description:"删除时间"`
	Creater          uint   `json:"creater"          orm:"creater"            description:"创建者"`
	IsDefault        int    `json:"isDefault"        orm:"is_default"         description:"是否默认(1:是 2:否)"`
	Demo             string `json:"demo"             orm:"demo"               description:"演示例子"`
	Language         string `json:"language"         orm:"language"           description:"语言"`
	OtherLanguageIds string `json:"otherLanguageIds" orm:"other_language_ids" description:"其它语言的tdk id"`
}
