// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// Tools is the golang structure for table tools.
type Tools struct {
	Id          uint   `json:"id"          orm:"id"           description:""`
	Name        string `json:"name"        orm:"name"         description:"工具名称"`
	Type        int    `json:"type"        orm:"type"         description:"工具类别(关联数据配置)"`
	Label       string `json:"label"       orm:"label"        description:"工具标签"`
	Icon        string `json:"icon"        orm:"icon"         description:"图标"`
	SeoTitle    string `json:"seoTitle"    orm:"seo_title"    description:"seo标题"`
	SeoKeywords string `json:"seoKeywords" orm:"seo_keywords" description:"seo关键字"`
	SeoDesc     string `json:"seoDesc"     orm:"seo_desc"     description:"seo描述"`
	Url         string `json:"url"         orm:"url"          description:"自定义url"`
	CreateTime  int64  `json:"createTime"  orm:"create_time"  description:"创建时间"`
	UpdateTime  int64  `json:"updateTime"  orm:"update_time"  description:"修改时间"`
	Creator     uint   `json:"creator"     orm:"creator"      description:"创建者"`
	Status      int    `json:"status"      orm:"status"       description:"状态(1启用，2禁用)"`
	SeoList     string `json:"seoList"     orm:"seo_list"     description:"seo集合"`
	Ui          string `json:"ui"          orm:"ui"           description:"界面代码"`
}
