package videoCategory

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

// 添加
func (c *Controller) Add(ctx context.Context, req *v1.VideoCategoryAddReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.VideoCategory().Add(ctx, req)
	return
}

// 编辑
func (c *Controller) Edit(ctx context.Context, req *v1.VideoCategoryEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.VideoCategory().Edit(ctx, req)
	return
}

// 列表
func (c *Controller) List(ctx context.Context, req *v1.VideoCategoryListReq) (res *v1.VideoCategoryListRes, err error) {
	res = new(v1.VideoCategoryListRes)
	ret, err := service.VideoCategory().List(ctx, req)
	if err != nil {
		return
	}
	res.List = ret.List
	res.Current = req.Current
	res.Total = ret.Total
	res.Offset = req.Offset
	return
}

// 删除
func (c *Controller) Delete(ctx context.Context, req *v1.VideoCategoryDeleteReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.VideoCategory().Delete(ctx, req)
	return
}

// 详情
func (c *Controller) Info(ctx context.Context, req *v1.VideoCategoryInfoReq) (res *v1.VideoCategoryInfoRes, err error) {
	res = new(v1.VideoCategoryInfoRes)
	res, err = service.VideoCategory().Info(ctx, req)
	return
}
