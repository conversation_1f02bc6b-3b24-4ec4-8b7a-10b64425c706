package keyword

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

//
//func (c *Controller) List(ctx context.Context, req *v1.KeywordListReq) (res *v1.KeywordListRes, err error) {
//	res, err = service.Keyword().List(ctx, req)
//	return
//}
//
//func (c *Controller) Add(ctx context.Context, req *v1.KeywordAddReq) (res *v1.KeywordAddRes, err error) {
//	res, err = service.Keyword().Add(ctx, req)
//	return
//}
//
//func (c *Controller) Edit(ctx context.Context, req *v1.KeywordEditReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Keyword().Edit(ctx, req)
//	return
//}
//
//func (c *Controller) Delete(ctx context.Context, req *v1.KeywordDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Keyword().Delete(ctx, req)
//	return
//}
//
//func (c *Controller) One(ctx context.Context, req *v1.KeywordOneReq) (res *v1.KeywordOneRes, err error) {
//	res, err = service.Keyword().One(ctx, req)
//	return
//}
//
//func (c *Controller) GroupSiteAllList(ctx context.Context, req *v1.KeywordGroupSiteAllListReq) (res *v1.KeywordGroupSiteAllListRes, err error) {
//	res, err = service.Keyword().GroupSiteAllList(ctx, req)
//	return
//}
//
//func (c *Controller) GroupSiteAdd(ctx context.Context, req *v1.KeywordGroupSiteAddReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Keyword().GroupSiteAdd(ctx, req)
//	return
//}
//
//func (c *Controller) Options(ctx context.Context, req *v1.KeywordOptionsReq) (res *v1.KeywordOptionsRes, err error) {
//	res = new(v1.KeywordOptionsRes)
//	res.Options, err = service.Keyword().Options(ctx, req)
//	return
//}
