package tdkTmpl

type Controller struct {
}

//
//func New() *Controller {
//	return &Controller{}
//}
//func (c *Controller) TypeList(ctx context.Context, req *v1.TdkTmplTypeListReq) (res *v1.DomainOptionsRes, err error) {
//	res = new(v1.DomainOptionsRes)
//	res.Options, err = service.TdkTmpl().TypeList(ctx)
//	return
//}
//func (c *Controller) List(ctx context.Context, req *v1.TdkTmplListReq) (res *v1.TdkTmplListRes, err error) {
//	res, err = service.TdkTmpl().List(ctx, req)
//	return
//}
//
//func (c *Controller) Add(ctx context.Context, req *v1.TdkTmplAddReq) (res *v1.TdkTmplAddRes, err error) {
//	res, err = service.TdkTmpl().Add(ctx, req)
//	return
//}
//
//func (c *Controller) Edit(ctx context.Context, req *v1.TdkTmplEditReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.TdkTmpl().Edit(ctx, req)
//	return
//}
//
//func (c *Controller) Delete(ctx context.Context, req *v1.TdkTmplDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.TdkTmpl().Delete(ctx, req)
//	return
//}
//
//func (c *Controller) One(ctx context.Context, req *v1.TdkTmplOneReq) (res *v1.TdkTmplOneRes, err error) {
//	res, err = service.TdkTmpl().One(ctx, req)
//	return
//}
//
//func (c *Controller) FastFill(ctx context.Context, req *v1.TdkTmplFastFillReq) (res *v1.TdkTmplFastFillRes, err error) {
//
//	title, desc, keywords, err := service.TdkTmpl().FastFill(ctx, req)
//	if err != nil {
//		return nil, err
//	}
//	return &v1.TdkTmplFastFillRes{
//		Title:    title,
//		Desc:     desc,
//		Keywords: keywords,
//	}, nil
//}
//
//func (c *Controller) GroupSiteAllList(ctx context.Context, req *v1.TdkTmplGroupSiteAllListReq) (res *v1.TdkTmplGroupSiteAllListRes, err error) {
//	res, err = service.TdkTmpl().GroupSiteAllList(ctx, req)
//	return
//}
//
//func (c *Controller) GroupSiteAdd(ctx context.Context, req *v1.TdkTmplGroupSiteAddReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.TdkTmpl().GroupSiteAdd(ctx, req)
//	return
//}
//
//func (c *Controller) Import(ctx context.Context, req *v1.TdkTmplImportReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.TdkTmpl().Import(ctx, req)
//	return
//}
//
//func (c *Controller) Export(ctx context.Context, req *v1.TdkTmplExportReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.TdkTmpl().Export(ctx, req)
//	return
//}
