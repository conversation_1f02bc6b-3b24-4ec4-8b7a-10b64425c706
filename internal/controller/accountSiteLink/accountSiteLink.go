package accountSiteLink

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

//
//func (c *Controller) AccountSiteLinkAdd(ctx context.Context, req *v1.AccountSiteLinkAddReq) (res *v1.EmptyDataRes, err error) {
//	var admin *entity.Account
//	admin, err = service.Utility().GetSelf(ctx)
//	if err != nil || admin == nil {
//		err = gerror.Wrap(err, "Get Account failed!")
//		return
//	}
//
//	//站长只能编辑 自己创建的站员
//	info, err := service.Account().Detail(ctx, req.AccountId)
//	if err != nil {
//		err = gerror.Wrap(err, "Get Account failed!")
//		return
//	}
//
//	adminRole, _ := service.RoleV2().Detail(ctx, admin.RoleId)
//	if adminRole.RoleLevel == consts.RoleLevelSiteMember {
//		err = gerror.New("站员无分配站点权限!")
//		return
//	}
//
//	if adminRole.RoleLevel == consts.RoleLevelSiteLeader && info.Creater != admin.Id {
//		err = gerror.New("站长只能编辑 自己创建的站员!")
//		return
//	}
//
//	leaderID := admin.Id
//	if info.Creater != admin.Id {
//		//管理员编辑 且不是站员的站长时
//		leaderID = info.Creater
//	}
//
//	isPass, _ := service.Site().CheckSiteGroupExist(ctx, req.SiteId, req.GroupId)
//	if !isPass {
//		err = gerror.New("分组ID对应的站点ID角色不存在!")
//		return
//	}
//
//	in := &entity.AccountSiteLink{
//		Id:        0,
//		AccountId: req.AccountId,
//		GroupId:   int(req.GroupId),
//		SiteId:    req.SiteId,
//		IsAffect:  1,
//		Creater:   leaderID,
//	}
//	err = service.AccountSiteLink().Create(ctx, in)
//	return
//}
//
//func (c *Controller) AccountSiteLinkDelete(ctx context.Context, req *v1.AccountSiteLinkDeleteReq) (res *v1.EmptyDataRes, err error) {
//	var admin *entity.Account
//	admin, err = service.Utility().GetSelf(ctx)
//	if err != nil || admin == nil {
//		err = gerror.Wrap(err, "Get Account failed!")
//		return
//	}
//
//	//站长只能删除 自己创建的站员
//	info, err := service.Account().Detail(ctx, req.AccountId)
//	if err != nil {
//		err = gerror.Wrap(err, "Get Account failed!")
//		return
//	}
//
//	adminRole, _ := service.RoleV2().Detail(ctx, admin.RoleId)
//	if adminRole.RoleLevel == consts.RoleLevelSiteMember {
//		err = gerror.New("站员无删除站点权限!")
//		return
//	}
//
//	if adminRole.RoleLevel == consts.RoleLevelSiteLeader && info.Creater != admin.Id {
//		err = gerror.New("站长只能删除 自己创建的站员!")
//		return
//	}
//
//	err = service.AccountSiteLink().Delete(ctx, req.Id, req.AccountId)
//	return
//}
//
////func (c *Controller) AccountSiteLinkEdit(ctx context.Context, req *v1.AccountSiteLinkEditReq) (res *v1.EmptyDataRes, err error) {
////	in := &entity.AccountSiteLink{
////		Id:            req.Id,
////		AccountId:     0,
////		SiteId:        0,
////		IsAffect:      0,
////		CreateTime:    0,
////		CreateAccount: "",
////		UpdateTime:    0,
////		UpdateAccount: "",
////		Creater:       0,
////	}
////	err = service.AccountSiteLink().Edit(ctx, req.Id, in)
////	return
////}
//
//func (c *Controller) AccountSiteLinkDetail(ctx context.Context, req *v1.AccountSiteLinkDetailReq) (res *v1.AccountSiteLinkDetailRes, err error) {
//	res, err = service.AccountSiteLink().Detail(ctx, req.Id)
//	return
//}
//
//func (c *Controller) AccountSiteLinkList(ctx context.Context, req *v1.AccountSiteLinkListReq) (res *v1.AccountSiteLinkListRes, err error) {
//	res = new(v1.AccountSiteLinkListRes)
//	var admin *entity.Account
//	admin, err = service.Utility().GetSelf(ctx)
//	if err != nil || admin == nil {
//		err = gerror.Wrap(err, "Get Account failed!")
//		return
//	}
//
//	//站长只能查看 自己创建的站员
//	info, err := service.Account().Detail(ctx, req.AccountId)
//	if err != nil {
//		err = gerror.Wrap(err, "Get Account failed!")
//		return
//	}
//
//	yuanRole, _ := service.RoleV2().Detail(ctx, info.RoleId)
//	if yuanRole.RoleLevel != consts.RoleLevelSiteMember {
//		return
//	}
//
//	adminRole, _ := service.RoleV2().Detail(ctx, admin.RoleId)
//	if adminRole.RoleLevel != consts.RoleLevelAdmin {
//		if info.Creater != admin.Id {
//			err = gerror.New("站长仅能查看自己创建的站员!")
//			return
//		}
//	}
//
//	creatorID := admin.Id //默认站长查看站员
//	if adminRole.RoleLevel == consts.RoleLevelAdmin {
//		creatorID = info.Creater
//	}
//
//	var groups []model.GroupInfo
//	groups, err = service.SiteGroup().GetGroupIDsByCreator(ctx, creatorID)
//	if err != nil {
//		err = gerror.Wrap(err, "GetGroupIDsByCreator failed!")
//		return
//	}
//	if g.IsEmpty(groups) {
//		g.Log().Line().Infof(ctx, "Account : %s not config group!", admin.Account)
//		return
//	}
//
//	var cfgs []*model.GroupSiteLink
//	cfgs, err = service.Site().GetSiteByGroupIDs(ctx, groups)
//	if err != nil {
//		err = gerror.Wrap(err, "GetSiteByGroupIDs failed!")
//		return
//	}
//	if g.IsEmpty(cfgs) {
//		g.Log().Line().Infof(ctx, "Account: %s not config sites, groups=%+v !", admin.Account, groups)
//		return
//	}
//
//	var mapSiteID map[uint]*v1.AccountSiteLinkVo
//	mapSiteID, err = service.AccountSiteLink().List(ctx, req)
//	if err != nil {
//		err = gerror.Wrap(err, "GetGroupIDsByCreator failed!")
//		return
//	}
//	if g.IsEmpty(mapSiteID) {
//		g.Log().Line().Infof(ctx, "Account : %s not config group!", admin.Account)
//	}
//
//	for _, cf := range cfgs {
//		v, ok := mapSiteID[cf.SiteId]
//		if ok {
//			v.GroupName = cf.GroupName
//			v.SiteName = cf.SiteName
//			v.IsAffect = 1
//			res.List = append(res.List, v)
//			continue
//		}
//
//		item := &v1.AccountSiteLinkVo{
//			Id: 0,
//			//AccountId: cf.Creater,
//			GroupId:   cf.GroupId,
//			GroupName: cf.GroupName,
//			SiteId:    cf.SiteId,
//			SiteName:  cf.SiteName,
//			IsAffect:  2,
//		}
//		res.List = append(res.List, item)
//	}
//
//	return
//}
