package attachment

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

//
//// List 素材列表
//func (c *Controller) List(ctx context.Context, req *v1.AttachmentListReq) (res *v1.AttachmentListRes, err error) {
//	res = new(v1.AttachmentListRes)
//	out, err := service.Attachment().List(ctx, model.AttachmentListInput{
//		Name:      req.Name,
//		Type:      req.Type,
//		ListInput: req.GetListInput(),
//	})
//	if err != nil {
//		return
//	}
//	res.ListRes.Set(out.ListOutput)
//	res.Current = req.Current
//	// https://github.com/dustin/go-humanize
//	getSize := func(s int64) int64 {
//		var base float64 = 1000
//		var e float64 = 1
//		val := math.Floor(float64(s)/math.Pow(base, e)*10+0.5) / 10
//		return int64(val)
//	}
//	for _, row := range out.List {
//		res.List = append(res.List, v1.AttachmentItem{
//			Attachment: row,
//			Url:        service.File().GetFrontendUrl(ctx, row.Key, nil),
//			Size:       getSize(row.Size),
//		})
//	}
//	return
//}
//
//// Add 素材新增
//func (c *Controller) Add(ctx context.Context, req *v1.AttachmentAddReq) (res *v1.AddRes, err error) {
//	res = new(v1.AddRes)
//
//	if req.CategoryId > 0 {
//		// 查找当前分类的信息
//		categoryOne, oneErr := service.Attachment().CategoryOne(ctx, model.CategoryOneInput{
//			Id: req.CategoryId,
//		})
//		if oneErr != nil {
//			err = oneErr
//			return
//		}
//		if categoryOne.Type != req.Type {
//			err = gerror.NewCode(gcode.CodeInvalidParameter)
//			return
//		}
//		// 当前分类不能有子分类
//		pCategory, oneErr := service.Attachment().CategoryOne(ctx, model.CategoryOneInput{
//			PId: req.CategoryId,
//		})
//		if oneErr != nil && !gerror.Is(oneErr, errno.CodeNotFound) {
//			err = oneErr
//			return
//		}
//		// 有子分类则不能新增素材
//		if pCategory != nil {
//			err = gerror.NewCode(gcode.CodeInvalidParameter)
//			return
//		}
//
//	}
//
//	res.Id, err = service.Attachment().Add(ctx, model.AttachmentAddInput{
//		CategoryId:    req.CategoryId,
//		Name:          req.Name,
//		Key:           req.Key,
//		Type:          req.Type,
//		CreateAccount: service.Utility().GetSelfAccount(ctx),
//	})
//	return
//}
//
//// Rename 素材重命名
//func (c *Controller) Rename(ctx context.Context, req *v1.AttachmentRenameReq) (res *v1.EmptyRes, err error) {
//	res = new(v1.EmptyRes)
//	err = service.Attachment().Edit(ctx, model.AttachmentEditInput{
//		Ids:           []uint{req.Id},
//		Name:          req.NewName,
//		UpdateAccount: service.Utility().GetSelfAccount(ctx),
//	})
//	return
//}
//
//// Move 素材移动
//func (c *Controller) Move(ctx context.Context, req *v1.AttachmentMoveReq) (res *v1.EmptyRes, err error) {
//	res = new(v1.EmptyRes)
//
//	// 当前分类不能有子分类
//	category, err := service.Attachment().CategoryOne(ctx, model.CategoryOneInput{
//		PId: req.CategoryId,
//	})
//	if !gerror.Is(err, errno.CodeNotFound) {
//		err = gerror.NewCode(gcode.CodeInvalidParameter)
//		return
//	}
//	// 有子分类则不能新增素材
//	if category != nil {
//		err = gerror.NewCode(gcode.CodeInvalidParameter)
//		return
//	}
//	err = service.Attachment().Edit(ctx, model.AttachmentEditInput{
//		Ids:           req.Ids,
//		CategoryId:    req.CategoryId,
//		UpdateAccount: service.Utility().GetSelfAccount(ctx),
//	})
//	return
//}
//
//// Delete 素材删除
//func (c *Controller) Delete(ctx context.Context, req *v1.AttachmentDeleteReq) (res *v1.EmptyRes, err error) {
//	res = new(v1.EmptyRes)
//	err = service.Attachment().Delete(ctx, model.AttachmentDeleteInput{
//		Ids:           req.Ids,
//		UpdateAccount: service.Utility().GetSelfAccount(ctx),
//	})
//	return
//}
