package attachment

import (
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	v1 "gtcms/api/v1"
	model "gtcms/internal/model/admin"
	"gtcms/internal/service"
	"sort"
)

// CategoryList 附件分类列表
func (c *Controller) CategoryList(ctx context.Context, req *v1.AttachmentCategoryListReq) (res *v1.AttachmentCategoryListRes, err error) {
	res = new(v1.AttachmentCategoryListRes)
	// 已经按orderBy排序
	out, err := service.Attachment().CategoryList(ctx, model.AttachmentCategoryListInput{
		Type: req.Type,
	})
	if err != nil {
		return
	}
	if out.Total < 1 {
		return
	}
	mData := make(map[uint]*v1.AttachmentCategory)
	for _, tmp := range out.List {
		mData[tmp.Id] = &v1.AttachmentCategory{
			AttachmentCategory: tmp,
			Children:           make([]*v1.AttachmentCategory, 0),
		}
	}
	for _, mRow := range mData {
		if mRow.Pid == 0 {
			res.List = append(res.List, mRow)
		} else {
			if _, ok := mData[mRow.Pid]; ok {
				mData[mRow.Pid].Children = append(mData[mRow.Pid].Children, mRow)
			}
		}
	}
	if len(res.List) > 0 {
		// 一级分类重新orderBy排序
		sort.SliceStable(res.List, func(i, j int) bool {
			if res.List[i].OrderBy > res.List[j].OrderBy {
				return true
			}
			if res.List[i].OrderBy == res.List[j].OrderBy && res.List[i].Id > res.List[j].Id {
				return true
			}
			return false
		})
	}

	return
}

// CategoryAdd 附件分类新增
func (c *Controller) CategoryAdd(ctx context.Context, req *v1.AttachmentCategoryAddReq) (res *v1.AddRes, err error) {
	res = new(v1.AddRes)
	if req.CategoryId > 0 {
		pCat, oneErr := service.Attachment().CategoryOne(ctx, model.CategoryOneInput{
			Id: req.CategoryId,
		})
		if oneErr != nil {
			err = oneErr
			return
		}
		if pCat.Type != req.Type {
			err = gerror.NewCode(gcode.CodeInvalidParameter)
			return
		}
	}

	res.Id, err = service.Attachment().CategoryAdd(ctx, model.AttachmentCategoryAddInput{
		Pid:           req.CategoryId,
		Type:          req.Type,
		Name:          req.Name,
		OrderBy:       req.OrderBy,
		CreateAccount: service.Utility().GetSelfAccount(ctx),
	})
	return
}

// CategoryEdit 附件分类新增
func (c *Controller) CategoryEdit(ctx context.Context, req *v1.AttachmentCategoryEditReq) (res *v1.EmptyRes, err error) {
	res = new(v1.EmptyRes)
	in := model.AttachmentCategoryEditInput{
		Id:            req.Id,
		OrderBy:       req.OrderBy,
		UpdateAccount: service.Utility().GetSelfAccount(ctx),
	}
	if req.Name != nil && len(*req.Name) > 0 {
		in.Name = *req.Name
	}
	err = service.Attachment().CategoryEdit(ctx, in)
	return
}

// CategoryDelete 附件分类删除
func (c *Controller) CategoryDelete(ctx context.Context, req *v1.AttachmentCategoryDeleteReq) (res *v1.EmptyRes, err error) {
	res = new(v1.EmptyRes)
	// 查询当前分类下有没有子分类,有则不允许删除
	checkCount, err := service.Attachment().CategoryCount(ctx, model.AttachmentCategoryCountInput{
		PIds: req.Ids,
	})
	if err != nil {
		g.Log().Error(ctx, err)
		err = gerror.NewCode(gcode.CodeInternalError)
		return
	}
	if checkCount > 0 {
		err = gerror.NewCode(gcode.CodeInvalidParameter, g.I18n().T(ctx, "category.sub.delete.id.count"))
		return
	}
	// 查询当前分类下有没有图片,有则不允许删除
	checkCount, err = service.Attachment().AttachmentCount(ctx, model.AttachmentCountInput{
		CategoryIds: req.Ids,
	})
	if err != nil {
		g.Log().Error(ctx, err)
		err = gerror.NewCode(gcode.CodeInternalError)
		return
	}
	if checkCount > 0 {
		err = gerror.NewCode(gcode.CodeInvalidParameter, g.I18n().T(ctx, "attachment.category.delete.id.count"))
		return
	}
	err = service.Attachment().CategoryDelete(ctx, model.AttachmentCategoryDeleteInput{
		Ids:           req.Ids,
		UpdateAccount: service.Utility().GetSelfAccount(ctx),
	})
	return
}
