package selectorConfig

import (
	"context"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "gtcms/api/v1"
	entity "gtcms/internal/model/entity/admin"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) SelectorConfigAdd(ctx context.Context, req *v1.SelectorConfigAddReq) (res *v1.EmptyDataRes, err error) {
	in := &entity.SelectorConfigContent{
		SelectType: req.SelectType,
		Title:      req.Title,
		IsOpen:     req.IsOpen,
		Sort:       req.Sort,
		Remark:     req.Remark,
		Extra:      gconv.String(req.Extra),
	}
	err = service.SelectorConfig().Create(ctx, in)
	return
}
func (c *Controller) SelectorConfigDelete(ctx context.Context, req *v1.SelectorConfigDeleteReq) (res *v1.EmptyDataRes, err error) {
	err = service.SelectorConfig().Delete(ctx, req.Id)
	return
}

func (c *Controller) SelectorConfigEdit(ctx context.Context, req *v1.SelectorConfigEditReq) (res *v1.EmptyDataRes, err error) {
	in := &entity.SelectorConfigContent{
		Id:         req.Id,
		SelectType: req.SelectType,
		Title:      req.Title,
		IsOpen:     req.IsOpen,
		Sort:       req.Sort,
		Remark:     req.Remark,
		Extra:      req.Extra,
	}
	err = service.SelectorConfig().Edit(ctx, req.Id, in)
	return
}
func (c *Controller) SelectorConfigDetail(ctx context.Context, req *v1.SelectorConfigDetailReq) (res *v1.SelectorConfigDetailRes, err error) {
	res, err = service.SelectorConfig().Detail(ctx, req.Id)
	return
}

func (c *Controller) SelectorConfigList(ctx context.Context, req *v1.SelectorConfigListReq) (res *v1.SelectorConfigListRes, err error) {
	res, err = service.SelectorConfig().List(ctx, req)
	return
}
