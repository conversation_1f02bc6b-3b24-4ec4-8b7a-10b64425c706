package noAuth

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "gtcms/api/v1"
	model "gtcms/internal/model/admin"
	"gtcms/internal/service"
	"io/ioutil"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func getToken(params map[string]string) string {
	appKey := "P8dBhEsY7pGn1h7pY7LT"
	gntoken := generateGntoken(params, appKey)
	return gntoken
}

func generateGntoken(params map[string]string, appKey string) string {
	// 1. 按照 ASCII 码顺序对参数名排序
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 2. 生成 key=value 形式的字符串，并进行 URL 编码
	var stringA []string
	for _, k := range keys {
		encodedValue := url.QueryEscape(params[k])
		stringA = append(stringA, fmt.Sprintf("%s=%s", k, encodedValue))
	}
	// 3. 拼接 APPKEY
	stringSignTemp := strings.Join(stringA, "&") + appKey

	// 4. 计算 MD5 并转大写
	h := md5.New()
	h.Write([]byte(stringSignTemp))
	sign := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
	return sign
}

func urlEncode(params map[string]string) string {
	var encodedParams []string
	for key, value := range params {
		encodedParams = append(encodedParams, fmt.Sprintf("%s=%s", key, url.QueryEscape(value)))
	}
	return strings.Join(encodedParams, "&")
}
func sendAddApi(url string, params map[string]string) (resolution model.DomainResolutionAddResponse, err error) {

	var response model.DomainResolutionAddResponse
	// 创建一个 POST 请求
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(urlEncode(params)))
	if err != nil {
		return response, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return response, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return response, err
	}
	jsonString := strings.TrimPrefix(string(body), "Response: ")
	// 将 JSON 解码到 map 中
	err = json.Unmarshal([]byte(jsonString), &response)
	if err != nil {
		return response, err
	}
	return response, nil
}
func sendDeleteApi(url string, params map[string]string) (resolution model.DomainResolutionDeleteResponse, err error) {

	var response model.DomainResolutionDeleteResponse
	// 创建一个 POST 请求
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(urlEncode(params)))
	if err != nil {
		return response, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return response, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return response, err
	}
	jsonString := strings.TrimPrefix(string(body), "Response: ")
	// 将 JSON 解码到 map 中
	err = json.Unmarshal([]byte(jsonString), &response)
	if err != nil {
		return response, err
	}
	return response, nil
}

// 新增用户域名解析记录
func (c *Controller) AddResolution(ctx context.Context, req *v1.AddResolutionReq) (res *v1.AddResolutionRes, err error) {
	params := map[string]string{
		"appid":  "15831367ac715bb6cf9",
		"gntime": gconv.String(time.Now().Unix()),
		"ym":     req.Ym,
		"lx":     "TXT",
		"zj":     "_acme-challenge",
		"jlz":    req.Jlz,
	}

	gntoken := getToken(params)
	params["gntoken"] = gntoken

	url := "https://api.gname.com/api/resolution/add"
	resolution, err := sendAddApi(url, params)
	if err != nil {
		err = fmt.Errorf("新增解析失败: %s", resolution.Msg)
		return
	}
	if resolution.Code != 1 {
		err = fmt.Errorf("新增解析失败: %s", resolution.Msg)
		return
	}
	res = &v1.AddResolutionRes{
		Jxid: resolution.Data,
	}
	return
}

func (c *Controller) DeleteResolution(ctx context.Context, req *v1.DeleteResolutionReq) (res *v1.AddResolutionRes, err error) {
	params := map[string]string{
		"appid":  "15831367ac715bb6cf9",
		"gntime": gconv.String(time.Now().Unix()),
		"ym":     req.Ym,
		"lx":     "TXT",
	}
	gntoken := getToken(params)
	params["gntoken"] = gntoken

	url := "https://api.gname.com/api/resolution/delete/type"
	resolution, err := sendDeleteApi(url, params)
	if err != nil {
		err = fmt.Errorf("删除解析失败: %s", resolution.Msg)
		return
	}
	if resolution.Code != 1 {
		err = fmt.Errorf("删除解析失败: %s", resolution.Msg)
		return
	}
	return
}

func (c *Controller) GetCaptcha(ctx context.Context, req *v1.CaptchaReq) (res *v1.CaptchaRes, err error) {
	service.Event().OnGetCaptcha(ctx)
	id, b64s, err := service.Utility().GenerateCaptcha()
	if err != nil {
		return
	}
	res = &v1.CaptchaRes{
		Key:  id,
		Data: b64s,
	}
	return
}

// 获取域名解析记录列表
func (c *Controller) GetDomainList(ctx context.Context, req *v1.GetDomainsReq) (res *v1.SelectorConfigListRes, err error) {
	res, err = service.SelectorConfig().List(ctx, &v1.SelectorConfigListReq{
		SelectType: "signal-domain",
	})
	if err != nil {
		return
	}
	return
}
