package noAuth

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

func (c *Controller) CmsConfigDetail(ctx context.Context, req *v1.CmsConfigDetailReq) (res *v1.CmsConfigDetailRes, err error) {
	res = new(v1.CmsConfigDetailRes)
	// 后台设置
	detailRes, err := service.CmsLoginConfig().DetailCache(ctx)
	if err != nil {
		return
	}
	res.LoginConfig.Title = detailRes.Title

	return
}

func (c *Controller) SynQuranJuz(ctx context.Context, req *v1.SynQuranJuzReq) (res *v1.CmsConfigDetailRes, err error) {
	res = new(v1.CmsConfigDetailRes)
	service.CmsLoginConfig().SynQuranJuz(ctx, req.Id)
	return
}
func (c *Controller) SynTahlil(ctx context.Context, req *v1.SynTahlilReq) (res *v1.CmsConfigDetailRes, err error) {
	res = new(v1.CmsConfigDetailRes)
	service.CmsLoginConfig().SynTahlil(ctx)
	return
}

func (c *Controller) SynDoa(ctx context.Context, req *v1.SynDoaReq) (res *v1.CmsConfigDetailRes, err error) {
	res = new(v1.CmsConfigDetailRes)
	service.CmsLoginConfig().SynDoa(ctx, req.Id)
	return
}
func (c *Controller) SynWirid(ctx context.Context, req *v1.SynWiridReq) (res *v1.CmsConfigDetailRes, err error) {
	res = new(v1.CmsConfigDetailRes)
	service.CmsLoginConfig().SynWirid(ctx, req.Id)
	return
}
