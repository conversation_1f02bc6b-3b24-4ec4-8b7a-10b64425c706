package newsCategory

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

// 新增
func (c *Controller) Add(ctx context.Context, req *v1.NewsCategoryAddReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsCategory().Add(ctx, &v1.NewsCategoryAddReq{
		NewsCategory: v1.NewsCategory{
			CoverImgs: req.CoverImgs,
			IsAppShow: req.IsAppShow,
			NameArr:   req.NameArr,
			ParentId:  req.ParentId,
			Remark:    req.Remark,
			Sort:      req.Sort,
			Status:    req.Status,
		},
	})
	return
}

// 修改
func (c *Controller) Edit(ctx context.Context, req *v1.NewsCategoryEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsCategory().Edit(ctx, &v1.NewsCategoryEditReq{
		Id: req.Id,
		NewsCategory: v1.NewsCategory{
			CoverImgs: req.CoverImgs,
			IsAppShow: req.IsAppShow,
			NameArr:   req.NameArr,
			ParentId:  req.ParentId,
			Remark:    req.Remark,
			Sort:      req.Sort,
			Status:    req.Status,
		},
	})
	return
}

// 详情
func (c *Controller) Info(ctx context.Context, req *v1.NewsCategoryInfoReq) (res *v1.NewsCategoryInfoRes, err error) {
	res = &v1.NewsCategoryInfoRes{}
	ret, err := service.NewsCategory().Info(ctx, &v1.NewsCategoryInfoReq{
		Id: req.Id,
	})
	if err != nil {
		return
	}
	res.Sort = ret.Sort
	res.NameArr = ret.NameArr
	res.ParentId = ret.ParentId
	res.Remark = ret.Remark
	res.Status = ret.Status
	res.CoverImgs = ret.CoverImgs
	res.IsAppShow = ret.IsAppShow
	return
}

// 删除
func (c *Controller) Delete(ctx context.Context, req *v1.NewsCategoryDeleteReq) (res *v1.EmptyDataRes, err error) {
	res = &v1.EmptyDataRes{}
	err = service.NewsCategory().Delete(ctx, &v1.NewsCategoryDeleteReq{
		Ids: req.Ids,
	})
	return
}

// 列表
func (c *Controller) List(ctx context.Context, req *v1.NewsCategoryListReq) (res *v1.NewsCategoryListRes, err error) {
	res = new(v1.NewsCategoryListRes)
	ret, err := service.NewsCategory().List(ctx, &v1.NewsCategoryListReq{
		CategoryName: req.CategoryName,
		Status:       req.Status,
		ListReq: v1.ListReq{
			Current:  req.Current,
			PageSize: req.PageSize,
			OrderBy:  req.OrderBy,
			Offset:   req.Offset,
		},
	})
	if err != nil {
		return
	}
	res.List = ret.List
	res.Current = req.Current
	res.Offset = req.Offset
	res.Total = ret.Total
	return
}
