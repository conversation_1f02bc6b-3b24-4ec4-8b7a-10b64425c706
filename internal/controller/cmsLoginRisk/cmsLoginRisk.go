package cmsLoginRisk

import (
	"context"
	v1 "gtcms/api/v1"
	entity "gtcms/internal/model/entity/admin"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) CmsLoginRiskAdd(ctx context.Context, req *v1.CmsLoginRiskAddReq) (res *v1.EmptyDataRes, err error) {
	in := &entity.CmsLoginRisk{
		TabType: req.TabType,
		Content: req.Content,
		IsOpen:  req.IsOpen,
		Remark:  req.Remark,
	}
	err = service.CmsLoginRisk().Create(ctx, in)
	return
}
func (c *Controller) CmsLoginRiskDelete(ctx context.Context, req *v1.CmsLoginRiskDeleteReq) (res *v1.EmptyDataRes, err error) {
	err = service.CmsLoginRisk().Delete(ctx, req.Id)
	return
}

func (c *Controller) CmsLoginRiskEdit(ctx context.Context, req *v1.CmsLoginRiskEditReq) (res *v1.EmptyDataRes, err error) {
	in := &entity.CmsLoginRisk{
		Id:      req.Id,
		TabType: req.TabType,
		Content: req.Content,
		IsOpen:  req.IsOpen,
		Remark:  req.Remark,
	}
	err = service.CmsLoginRisk().Edit(ctx, req.Id, in)
	return
}
func (c *Controller) CmsLoginRiskDetail(ctx context.Context, req *v1.CmsLoginRiskDetailReq) (res *v1.CmsLoginRiskDetailRes, err error) {
	res, err = service.CmsLoginRisk().Detail(ctx, req.Id)
	return
}

func (c *Controller) CmsLoginRiskList(ctx context.Context, req *v1.CmsLoginRiskListReq) (res *v1.CmsLoginRiskListRes, err error) {
	res, err = service.CmsLoginRisk().List(ctx, req)
	return
}
