package video

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

// 新增
func (c *Controller) Add(ctx context.Context, req *v1.VideoAddReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.Video().Add(ctx, req)
	return
}

// 编辑
func (c *Controller) Edit(ctx context.Context, req *v1.VideoEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.Video().Edit(ctx, req)
	return
}

// 详情
func (c *Controller) Info(ctx context.Context, req *v1.VideoInfoReq) (res *v1.VideoInfoRes, err error) {
	res = new(v1.VideoInfoRes)
	res, err = service.Video().Info(ctx, req)
	return
}

// 列表
func (c *Controller) List(ctx context.Context, req *v1.VideoListReq) (res *v1.VideoListRes, err error) {
	res = new(v1.VideoListRes)
	res, err = service.Video().List(ctx, req)
	return
}

// 删除
func (c *Controller) Delete(ctx context.Context, req *v1.VideoDeleteReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.Video().Delete(ctx, req)
	return
}

// 上线
func (c *Controller) SetOnline(ctx context.Context, req *v1.VideoSetOnlineReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.Video().SetOnline(ctx, req)
	return
}

// 下线
func (c *Controller) SetOffline(ctx context.Context, req *v1.VideoSetOfflineReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.Video().SetOffline(ctx, req)
	return
}
