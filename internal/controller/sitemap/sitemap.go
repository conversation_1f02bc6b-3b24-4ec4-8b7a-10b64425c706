package sitemap

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

//
//func (c *Controller) List(ctx context.Context, req *v1.SitemapListReq) (res *v1.SitemapListRes, err error) {
//	res, err = service.Sitemap().List(ctx, req)
//	return
//}
//
//func (c *Controller) Add(ctx context.Context, req *v1.SitemapAddReq) (res *v1.SitemapAddRes, err error) {
//	res, err = service.Sitemap().Add(ctx, req)
//	return
//}
//
//func (c *Controller) Edit(ctx context.Context, req *v1.SitemapEditReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Sitemap().Edit(ctx, req)
//	return
//}
//
//func (c *Controller) Delete(ctx context.Context, req *v1.SitemapDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Sitemap().Delete(ctx, req)
//	return
//}
//
//func (c *Controller) One(ctx context.Context, req *v1.SitemapOneReq) (res *v1.SitemapOneRes, err error) {
//	res, err = service.Sitemap().One(ctx, req)
//	return
//}
//
//func (c *Controller) RobotsList(ctx context.Context, req *v1.RobotsListReq) (res *v1.RobotsListRes, err error) {
//	res, err = service.Sitemap().RobotsList(ctx, req)
//	return
//}
//
//func (c *Controller) RobotsAdd(ctx context.Context, req *v1.RobotsAddReq) (res *v1.RobotsAddRes, err error) {
//	res, err = service.Sitemap().RobotsAdd(ctx, req)
//	return
//}
//
//func (c *Controller) RobotsEdit(ctx context.Context, req *v1.RobotsEditReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Sitemap().RobotsEdit(ctx, req)
//	return
//}
//
//func (c *Controller) RobotsDelete(ctx context.Context, req *v1.RobotsDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Sitemap().RobotsDelete(ctx, req)
//	return
//}
//
//func (c *Controller) RobotsOne(ctx context.Context, req *v1.RobotsOneReq) (res *v1.RobotsOneRes, err error) {
//	res, err = service.Sitemap().RobotsOne(ctx, req)
//	return
//}
