package label

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

//
//func (c *Controller) List(ctx context.Context, req *v1.LabelListReq) (res *v1.LabelListRes, err error) {
//	res, err = service.Label().List(ctx, req)
//	return
//}
//
//func (c *Controller) Add(ctx context.Context, req *v1.LabelAddReq) (res *v1.LabelAddRes, err error) {
//	res, err = service.Label().Add(ctx, req)
//	return
//}
//
//func (c *Controller) Edit(ctx context.Context, req *v1.LabelEditReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Label().Edit(ctx, req)
//	return
//}
//
//func (c *Controller) Delete(ctx context.Context, req *v1.LabelDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Label().Delete(ctx, req)
//	return
//}
//
//func (c *Controller) One(ctx context.Context, req *v1.LabelOneReq) (res *v1.LabelOneRes, err error) {
//	res, err = service.Label().One(ctx, req)
//	return
//}
//
//func (c *Controller) Related(ctx context.Context, req *v1.LabelRelatedReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Label().Related(ctx, req)
//	return
//}
//
//func (c *Controller) NewsList(ctx context.Context, req *v1.LabelNewsListReq) (res *v1.LabelNewsListRes, err error) {
//	res, err = service.Label().NewsList(ctx, req)
//	return
//}
//
//func (c *Controller) GroupSiteAllList(ctx context.Context, req *v1.LabelGroupSiteAllListReq) (res *v1.LabelGroupSiteAllListRes, err error) {
//	res, err = service.Label().GroupSiteAllList(ctx, req)
//	return
//}
//
//func (c *Controller) GroupSiteAdd(ctx context.Context, req *v1.LabelGroupSiteAddReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Label().GroupSiteAdd(ctx, req)
//	return
//}
