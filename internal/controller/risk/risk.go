package risk

//
//import (
//	"context"
//	"github.com/gogf/gf/v2/encoding/gjson"
//	"github.com/gogf/gf/v2/errors/gerror"
//	"github.com/gogf/gf/v2/util/gconv"
//	v1 "gtcms/api/v1"
//	"gtcms/internal/consts"
//	do "gtcms/internal/model/do/admin"
//	"gtcms/internal/service"
//)
//
//type Controller struct{}
//
//func New() *Controller {
//	return &Controller{}
//}
//
//func (c *Controller) RiskControlTabList(ctx context.Context, req *v1.RiskControlTabListReq) (res *v1.RiskControlTabListRes, err error) {
//	res = &v1.RiskControlTabListRes{}
//	rets, err := service.Risk().RiskTabList(ctx)
//	if err != nil {
//		return
//	}
//
//	_ = gconv.SliceStruct(rets, &res.List)
//	return
//}
//
//func (c *Controller) RiskControlTabEdit(ctx context.Context, req *v1.RiskControlTabEditSwitchReq) (res *v1.EmptyRes, err error) {
//	res = new(v1.EmptyRes)
//	err = service.Risk().RiskTabEditSwitch(ctx, req.TabType, req.IsOpen)
//	if err == nil {
//		_ = service.Event().PublishConfigUpdate(ctx, consts.SubTopicRiskControlTab)
//	}
//
//	return
//}
//
//func (c *Controller) RiskControlTabEditMetas(ctx context.Context, req *v1.RiskControlTabEditMetasReq) (res *v1.EmptyRes, err error) {
//	res = new(v1.EmptyRes)
//	if !gjson.Valid(req.Metas) {
//		err = gerror.New("Metas is not valid Json Format!")
//		return
//	}
//
//	err = service.Risk().RiskTabEditMetas(ctx, req.TabType, req.Metas)
//	if err == nil {
//		_ = service.Event().PublishConfigUpdate(ctx, consts.SubTopicRiskControlTab)
//	}
//
//	return
//}
//
//func (c *Controller) RiskContentList(ctx context.Context, req *v1.RiskControlContentListReq) (res *v1.RiskControlContentListRes, err error) {
//	rets, listRes, err := service.Risk().RiskContentList(ctx, req.TabType, req.Key, req.IsOpen,
//		req.StartTime, req.EndTime, &req.ListReq)
//	if err != nil {
//		return
//	}
//
//	var items []*v1.RiskContentItem
//	_ = gconv.SliceStruct(rets, &items)
//
//	res = &v1.RiskControlContentListRes{
//		ListRes: listRes,
//		List:    items,
//	}
//	return
//}
//
//func (c *Controller) RiskContentListAdd(ctx context.Context, req *v1.RiskControlContentAddReq) (res *v1.EmptyRes, err error) {
//	res = new(v1.EmptyRes)
//	item := &do.RiskControlContent{
//		TabType: req.TabType,
//		Content: req.Content,
//		IsOpen:  req.IsOpen,
//		Remark:  req.Remark,
//	}
//
//	err = service.Risk().RiskContentAdd(ctx, item)
//	if err == nil {
//		_ = service.Event().PublishConfigUpdate(ctx, consts.SubTopicRiskControlContent)
//	}
//	return
//}
//
//func (c *Controller) RiskContentListEdit(ctx context.Context, req *v1.RiskControlContentEditReq) (res *v1.EmptyRes, err error) {
//	res = new(v1.EmptyRes)
//	item := &do.RiskControlContent{
//		Id:      req.Id,
//		TabType: req.TabType,
//		Content: req.Content,
//		IsOpen:  req.IsOpen,
//		Remark:  req.Remark,
//	}
//
//	err = service.Risk().RiskContentEdit(ctx, req.Id, item)
//	if err == nil {
//		_ = service.Event().PublishConfigUpdate(ctx, consts.SubTopicRiskControlContent)
//	}
//
//	return
//}
//
//func (c *Controller) RiskContentListDelete(ctx context.Context, req *v1.RiskControlContentDeleteReq) (res *v1.EmptyRes, err error) {
//	res = new(v1.EmptyRes)
//	err = service.Risk().RiskContentDelete(ctx, req.Id)
//	if err == nil {
//		_ = service.Event().PublishConfigUpdate(ctx, consts.SubTopicRiskControlContent)
//	}
//
//	return
//}
