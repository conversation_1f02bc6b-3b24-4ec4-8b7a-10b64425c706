package feedback

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

// 列表
func (c *Controller) FeedbackList(ctx context.Context, req *v1.FeedbackListReq) (res *v1.Feedback<PERSON>ist<PERSON><PERSON>, err error) {
	return service.Feedback().FeedbackList(ctx, req)

}

// 详情
func (c *Controller) FeedbackOne(ctx context.Context, req *v1.FeedbackOneReq) (res *v1.FeedbackOneRes, err error) {
	return service.Feedback().FeedbackOne(ctx, req)
}

// 删除
func (c *Controller) FeedbackDelete(ctx context.Context, req *v1.FeedbackDeleteReq) (res *v1.FeedbackDeleteRes, err error) {
	return service.Feedback().FeedbackDelete(ctx, req)
}

// 处理反馈
func (c *Controller) FeedbackComplete(ctx context.Context, req *v1.FeedbackCompleteReq) (res *v1.FeedbackCompleteRes, err error) {
	return service.Feedback().FeedbackComplete(ctx, req)
}
