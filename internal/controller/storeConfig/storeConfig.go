package storeConfig

import (
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	model "gtcms/internal/model/admin"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) GetTypes(ctx context.Context, req *v1.StoreConfigTypesReq) (res *v1.StoreConfigTypesRes, err error) {
	res = new(v1.StoreConfigTypesRes)
	res.Options = service.StoreConfig().GetTypesOption(ctx)
	return
}

func (c *Controller) StoreBaseConfigDetail(ctx context.Context, req *v1.StoreBaseConfigDetailReq) (res *v1.StoreBaseConfigDetailRes, err error) {
	res = new(v1.StoreBaseConfigDetailRes)
	data, err := service.StoreConfig().BaseOne(ctx)
	if err != nil {
		return
	}
	res.Type = data.Type
	return
}

func (c *Controller) StoreBaseConfigEdit(ctx context.Context, req *v1.StoreBaseConfigEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	in := model.StoreBaseConfigEdit{
		Type: req.Type,
	}
	err = service.StoreConfig().BaseEdit(ctx, in)
	return
}

func (c *Controller) StoreCloudConfigDetail(ctx context.Context, req *v1.StoreCloudConfigDetailReq) (res *v1.StoreCloudConfigDetailRes, err error) {
	res = new(v1.StoreCloudConfigDetailRes)
	data, err := service.StoreConfig().CloudOne(ctx, req.Type)
	if gerror.HasCode(err, gcode.CodeNotFound) { // 这里忽略找不到的错误
		err = nil
	}
	if err != nil {
		return
	}
	if data == nil {
		return
	}
	switch req.Type {
	case consts.FileTypeS3Aws:
		err = gconv.Struct(data.Config, res.Aws)
	}
	return
}

func (c *Controller) StoreCloudConfigEdit(ctx context.Context, req *v1.StoreCloudConfigEditReq) (res *v1.EmptyDataRes, err error) {
	config := make(map[string]any)
	switch req.Type {
	case consts.FileTypeS3Aws:
		config = gconv.Map(req.Aws)
	}
	cloudConfig, oneErr := service.StoreConfig().CloudOne(ctx, req.Type)
	if oneErr != nil && !gerror.HasCode(oneErr, gcode.CodeNotFound) {
		return
	}
	if cloudConfig == nil {
		_, err = service.StoreConfig().CloudOneAdd(ctx, model.StoreCloudConfigAddInput{
			Type:   req.Type,
			Config: config,
		})
	} else {
		err = service.StoreConfig().CloudOneEdit(ctx, model.StoreCloudConfigEditInput{
			Type:   req.Type,
			Config: config,
		})
	}
	return
}
