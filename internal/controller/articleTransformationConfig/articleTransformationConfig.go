package articleTransformationConfig

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

//
//func (c *Controller) ArticleTransformationConfigAdd(ctx context.Context, req *v1.ArticleTransformationConfigAddReq) (res *v1.EmptyDataRes, err error) {
//	in := &entity.ArticleTransformationConfig{
//		TabType:        req.TabType,
//		Choice:         req.TabType,
//		IsOpen:         req.IsOpen,
//		AppId:          req.AppId,
//		Token:          req.Token,
//		IsAutoDetect:   req.IsAutoDetect,
//		SourceLanguage: req.SourceLanguage,
//		DestLanguage:   req.DestLanguage,
//		Domain:         req.Domain,
//	}
//	err = service.ArticleTransformationConfig().Create(ctx, in)
//	return
//}
//func (c *Controller) ArticleTransformationConfigDelete(ctx context.Context, req *v1.ArticleTransformationConfigDeleteReq) (res *v1.EmptyDataRes, err error) {
//	err = service.ArticleTransformationConfig().Delete(ctx, req.Id)
//	return
//}
//
//func (c *Controller) ArticleTransformationConfigEdit(ctx context.Context, req *v1.ArticleTransformationConfigEditReq) (res *v1.EmptyDataRes, err error) {
//	in := &entity.ArticleTransformationConfig{
//		Id:             req.Id,
//		TabType:        req.TabType,
//		Choice:         req.TabType,
//		IsOpen:         req.IsOpen,
//		AppId:          req.AppId,
//		Token:          req.Token,
//		IsAutoDetect:   req.IsAutoDetect,
//		SourceLanguage: req.SourceLanguage,
//		DestLanguage:   req.DestLanguage,
//		Domain:         req.Domain,
//	}
//	err = service.ArticleTransformationConfig().Edit(ctx, req.Id, in)
//	return
//}
//func (c *Controller) ArticleTransformationConfigDetail(ctx context.Context, req *v1.ArticleTransformationConfigDetailReq) (res *v1.ArticleTransformationConfigDetailRes, err error) {
//	res, err = service.ArticleTransformationConfig().Detail(ctx, req.Id)
//	return
//}
//
//func (c *Controller) ArticleTransformationConfigList(ctx context.Context, req *v1.ArticleTransformationConfigListReq) (res *v1.ArticleTransformationConfigListRes, err error) {
//	res, err = service.ArticleTransformationConfig().List(ctx, req)
//	return
//}
