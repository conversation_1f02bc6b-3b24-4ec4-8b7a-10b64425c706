package homePage

import (
	"context"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/os/gtimer"
	"github.com/gogf/gf/v2/util/grand"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	"gtcms/internal/service"
	"sync"
	"time"
)

var ctx = gctx.New()

type Controller struct {
	data    sync.Map
	dataCtx sync.Map
}

func New() *Controller {
	s := &Controller{
		data:    sync.Map{},
		dataCtx: sync.Map{},
	}
	//s.timer()
	return s
}

// 每60分钟，自动统计一次蜘蛛日志
func (c *Controller) timer() {
	gtimer.DelayAdd(ctx, 1*time.Hour, 1*time.Hour, func(ctx context.Context) {
		c.dataCtx.Range(func(key, value any) bool {
			ctx = value.(context.Context)
			c.run(ctx)
			return true
		})
	})
}

func (c *Controller) run(ctx context.Context) {
	res := new(v1.HomePageInfoRes)
	var rankSitePvs []*v1.RankSitePv
	engs := []*v1.SpiderEngine{{EngineId: 1, Icon: "", Title: "百度", NumVisit: 0}, {EngineId: 2, Icon: "", Title: "谷歌", NumVisit: 0},
		{EngineId: 3, Icon: "", Title: "360蜘蛛", NumVisit: 0}, {EngineId: 4, Icon: "", Title: "搜狗", NumVisit: 0},
		{EngineId: 5, Icon: "", Title: "神马", NumVisit: 0}, {EngineId: 6, Icon: "", Title: "今日头条", NumVisit: 0},
		{EngineId: 7, Icon: "", Title: "微软Bing", NumVisit: 0}, {EngineId: 8, Icon: "", Title: "其他", NumVisit: 0},
	}

	//// 今日蜘蛛统计数据
	//startOfDay, endOfDay := service.Utility().GetTodayTimestamps()
	//res.SpiderEngines, _ = service.Spider().SpiderAccessStatisticsToday(ctx, model.HomePageInfo{
	//	StartOfDay: startOfDay,
	//	EndOfDay:   endOfDay,
	//})

	// 使用映射来存储 stat1 中的数据，以便根据 EngineId 进行快速查找和更新
	statMap := make(map[int]*v1.SpiderEngine)
	for _, se := range res.SpiderEngines {
		statMap[se.EngineId] = se
	}

	// 更新 engs 中的数据
	for _, eng := range engs {
		if stat, ok := statMap[eng.EngineId]; ok {
			eng.NumVisit = stat.NumVisit
		}
	}

	rankSitePvs, _ = service.Site().GetAccessRanking(ctx)
	res.SpiderEngines = engs
	res.RankSitePvs = rankSitePvs

	self, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return
	}
	ac, err := service.RoleV2().Detail(ctx, self.RoleId)
	if err != nil {
		return
	}
	var key uint
	if ac.RoleLevel == consts.RoleLevelAdmin {
		key = uint(ac.RoleLevel)
	} else {
		key = self.Id
	}
	c.data.Store(key, res)
	c.dataCtx.Store(key, ctx)
}

func (c *Controller) HomePageInfo(ctx context.Context, req *v1.HomePageInfoReq) (res *v1.HomePageInfoRes, err error) {
	self, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return
	}
	ac, err := service.RoleV2().Detail(ctx, self.RoleId)
	if err != nil {
		return
	}
	var key uint
	if ac.RoleLevel == consts.RoleLevelAdmin {
		key = uint(ac.RoleLevel)
	} else {
		key = self.Id
	}

	_, ok := c.data.Load(key)
	if !ok {
		c.run(ctx)
	}
	data, _ := c.data.Load(key)
	dt := data.(*v1.HomePageInfoRes)

	var spiderToday []*v1.SpiderPvHour
	var spiderYesterday []*v1.SpiderPvHour
	var rankInclusions []*v1.RankInclusion
	var rankTags []*v1.RankTag

	webSt, _ := service.Site().ValidCount(ctx)

	res = &v1.HomePageInfoRes{
		Website:           webSt,
		SpiderEngines:     dt.SpiderEngines,
		SpiderPvYesterday: spiderYesterday,
		SpiderPvToday:     spiderToday,
		RankSitePvs:       dt.RankSitePvs,
		RankInclusions:    rankInclusions,
		RankTags:          rankTags,
	}

	return
}

func (c *Controller) HomePageSpiderSearch(ctx context.Context, req *v1.HomePageSpiderSearchReq) (res *v1.HomePageSpiderSearchRes, err error) {
	spiderYesterday := []*v1.SpiderPvHour{}
	spiderToday := []*v1.SpiderPvHour{}
	for i := 1; i <= 24; i++ {
		spiderYesterday = append(spiderYesterday, &v1.SpiderPvHour{
			Date:     gtime.Now().AddDate(0, 0, -1),
			Hour:     i,
			EngineId: int(req.Id),
			NumVisit: uint(grand.N(125, 2000)),
		})
		spiderToday = append(spiderToday, &v1.SpiderPvHour{
			Date:     gtime.Now(),
			Hour:     i,
			EngineId: int(req.Id),
			NumVisit: uint(grand.N(358, 5335)),
		})
	}

	res = &v1.HomePageSpiderSearchRes{
		SpiderPvYesterday: spiderYesterday,
		SpiderPvToday:     spiderToday,
	}

	return
}
