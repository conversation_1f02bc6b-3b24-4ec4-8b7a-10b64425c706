package tool

import (
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/os/gfile"
	v1 "gtcms/api/v1"
	"gtcms/internal/errno"
	model "gtcms/internal/model/admin"
	"gtcms/internal/service"
)

// Upload 上传文件
func (a *Controller) Upload(ctx context.Context, req *v1.FileUploadReq) (res *v1.FileUploadRes, err error) {
	if req.File == nil {
		return nil, gerror.NewCode(gcode.CodeMissingParameter, gi18n.T(ctx, "upload.file.missingParameter"))
	}
	f, err := req.File.Open()
	if err != nil {
		g.Log().Error(ctx, err)
		err = errno.T(ctx, errno.CodeFileUploadError)
		return
	}
	defer func() {
		_ = f.Close()
	}()

	result, err := service.File().SingleUpload(ctx, model.SingleUploadInput{
		File:    f,
		NameExt: gfile.Ext(req.File.Filename),
	})
	if err != nil {
		return nil, err
	}
	res = &v1.FileUploadRes{
		Name:       req.File.Filename,
		Key:        result.ObjectName,
		BackendUrl: service.File().GetBackUpUrl(ctx, result.ObjectName),
	}
	return
}

func (a *Controller) MultiUpload(ctx context.Context, req *v1.MultiUploadReq) (res *v1.MultiUploadRes, err error) {
	list := make([]*v1.FileUploadRes, len(req.Files))
	for key, item := range req.Files {
		upload, err := a.Upload(ctx, &v1.FileUploadReq{File: item})
		if err != nil {
			return nil, err
		}
		list[key] = upload
	}
	return &v1.MultiUploadRes{
		List: list,
	}, nil
}
