package cmsLoginConfig

import (
	"context"
	v1 "gtcms/api/v1"
	entity "gtcms/internal/model/entity/admin"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) CmsLoginConfigDetail(ctx context.Context, req *v1.CmsLoginConfigDetailReq) (res *v1.CmsLoginConfigDetailRes, err error) {
	res, err = service.CmsLoginConfig().Detail(ctx)
	return
}

func (c *Controller) CmsLoginConfigEdit(ctx context.Context, req *v1.CmsLoginConfigEditReq) (res *v1.EmptyDataRes, err error) {
	in := &entity.CmsLoginConfig{
		Id:           req.Id,
		IsOpen:       req.IsOpen,
		Title:        req.Title,
		Url:          req.Url,
		IsIpBind:     req.IsIpBind,
		IsMacBind:    req.IsMac<PERSON>ind,
		IsGoogleBind: req.IsGoogleBind,
	}
	err = service.CmsLoginConfig().Edit(ctx, req.Id, in)
	return
}
