package newsTopic

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

// 新增
func (c *Controller) Add(ctx context.Context, req *v1.NewsTopicAddReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsTopic().Add(ctx, &v1.NewsTopicAddReq{
		NewsTopic: v1.NewsTopic{
			TopicImgs:  req.TopicImgs,
			IsAppShow:  req.IsAppShow,
			NameArr:    req.NameArr,
			Sort:       req.Sort,
			ArticleArr: req.ArticleArr,
		},
	})
	return
}

// 编辑
func (c *Controller) Edit(ctx context.Context, req *v1.NewsTopicEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsTopic().Edit(ctx, &v1.NewsTopicEditReq{
		Id: req.Id,
		NewsTopic: v1.NewsTopic{
			TopicImgs:  req.TopicImgs,
			IsAppShow:  req.IsAppShow,
			NameArr:    req.NameArr,
			Sort:       req.Sort,
			ArticleArr: req.ArticleArr,
		},
	})
	return
}

// 详情
func (c *Controller) Info(ctx context.Context, req *v1.NewsTopicInfoReq) (res *v1.NewsTopicInfoRes, err error) {
	res = &v1.NewsTopicInfoRes{}
	ret, err := service.NewsTopic().Info(ctx, &v1.NewsTopicInfoReq{
		Id: req.Id,
	})
	if err != nil {
		return
	}
	res.Id = ret.Id
	res.NewsTopic = v1.NewsTopic{
		IsAppShow:  ret.IsAppShow,
		NameArr:    ret.NameArr,
		Sort:       ret.Sort,
		TopicImgs:  ret.TopicImgs,
		ArticleArr: ret.ArticleArr,
	}
	return
}

// 删除
func (c *Controller) Delete(ctx context.Context, req *v1.NewsTopicDeleteReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsTopic().Delete(ctx, &v1.NewsTopicDeleteReq{
		Ids: req.Ids,
	})
	return
}

// 列表
func (c *Controller) List(ctx context.Context, req *v1.NewsTopicListReq) (res *v1.NewsTopicListRes, err error) {
	res = &v1.NewsTopicListRes{}
	ret, err := service.NewsTopic().List(ctx, &v1.NewsTopicListReq{
		TopicName: req.TopicName,
		ListReq: v1.ListReq{
			Current:  req.Current,
			PageSize: req.PageSize,
			OrderBy:  req.OrderBy,
			Offset:   req.Offset,
		},
	})
	if err != nil {
		return
	}
	res.List = ret.List
	res.Current = req.Current
	res.Offset = req.Offset
	res.Total = ret.Total
	return
}
