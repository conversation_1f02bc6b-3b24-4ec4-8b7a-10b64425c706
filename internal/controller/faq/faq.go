package faq

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) CateAdd(ctx context.Context, req *v1.CateCreateReq) (res *v1.CateCreateRes, err error) {
	res, err = service.Faq().CateAdd(ctx, req)
	return
}

func (c *Controller) CateEdit(ctx context.Context, req *v1.CateEditeReq) (res *v1.CateEditRes, err error) {
	res, err = service.Faq().CateEdit(ctx, req)
	return
}

func (c *Controller) CateDelete(ctx context.Context, req *v1.CateDeleteReq) (res *v1.CateDeleteRes, err error) {
	res, err = service.Faq().CateDelete(ctx, req)
	return
}

func (c *Controller) CateOne(ctx context.Context, req *v1.CateOneReq) (res *v1.CateOneRes, err error) {
	res, err = service.Faq().CateOne(ctx, req)
	return
}

func (c *Controller) CateList(ctx context.Context, req *v1.CateListReq) (res *v1.CateListRes, err error) {
	res, err = service.Faq().CateList(ctx, req)
	return

}

func (c *Controller) QuestionAdd(ctx context.Context, req *v1.QuestionCreateReq) (res *v1.QuestionCreateRes, err error) {
	res, err = service.Faq().QuestionAdd(ctx, req)
	return
}

func (c *Controller) QuestionEdit(ctx context.Context, req *v1.QuestionEditReq) (res *v1.QuestionEditRes, err error) {
	res, err = service.Faq().QuestionEdit(ctx, req)
	return
}

func (c *Controller) QuestionDelete(ctx context.Context, req *v1.QuestionDeleteReq) (res *v1.QuestionDeleteRes, err error) {
	res, err = service.Faq().QuestionDelete(ctx, req)
	return
}

func (c *Controller) QuestionOne(ctx context.Context, req *v1.QuestionOneReq) (res *v1.QuestionOneRes, err error) {
	res, err = service.Faq().QuestionOne(ctx, req)
	return
}

func (c *Controller) QuestionList(ctx context.Context, req *v1.QuestionListReq) (res *v1.QuestionListRes, err error) {
	res, err = service.Faq().QuestionList(ctx, req)
	return
}
