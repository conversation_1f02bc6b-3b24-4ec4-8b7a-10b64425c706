package sensitive

import (
	"context"
	v1 "gtcms/api/v1"
	entity "gtcms/internal/model/entity/admin"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) SensitiveAdd(ctx context.Context, req *v1.SensitiveAddReq) (res *v1.EmptyDataRes, err error) {
	in := &entity.Sensitive{
		ClassId: req.ClassId,
		Word:    req.Word,
		IsOpen:  req.<PERSON>pen,
	}
	err = service.Sensitive().Create(ctx, in)
	return
}
func (c *Controller) SensitiveDelete(ctx context.Context, req *v1.SensitiveDeleteReq) (res *v1.EmptyDataRes, err error) {
	err = service.Sensitive().Delete(ctx, req.Id)
	return
}

func (c *Controller) SensitiveEdit(ctx context.Context, req *v1.SensitiveEditReq) (res *v1.EmptyDataRes, err error) {
	in := &entity.Sensitive{
		Id:      req.Id,
		ClassId: req.ClassId,
		Word:    req.Word,
		IsOpen:  req.IsOpen,
	}
	err = service.Sensitive().Edit(ctx, req.Id, in)
	return
}
func (c *Controller) SensitiveDetail(ctx context.Context, req *v1.SensitiveDetailReq) (res *v1.SensitiveDetailRes, err error) {
	res, err = service.Sensitive().Detail(ctx, req.Id)
	return
}

func (c *Controller) SensitiveList(ctx context.Context, req *v1.SensitiveListReq) (res *v1.SensitiveListRes, err error) {
	res, err = service.Sensitive().List(ctx, req)
	return
}

func (c *Controller) Options(ctx context.Context, req *v1.SensitiveOptionsReq) (res *v1.SensitiveOptionsRes, err error) {
	res = &v1.SensitiveOptionsRes{}
	res.Options, err = service.Sensitive().Options(ctx)
	return
}
