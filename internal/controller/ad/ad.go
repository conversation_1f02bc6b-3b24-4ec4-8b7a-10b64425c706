package ad

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

//
//func (c *Controller) NginxList(ctx context.Context, req *v1.NginxReq) (res *v1.NginxR<PERSON>, err error) {
//	res, err = service.Ad().NginxList(ctx, req)
//	return
//}
//func (c *Controller) NginxWebsList(ctx context.Context, req *v1.NginxWebsReq) (res *v1.NginxWebsRes, err error) {
//	res, err = service.Ad().NginxWebsList(ctx, req)
//	return
//}
//func (c *Controller) NginxOverallList(ctx context.Context, req *v1.NginxOverallReq) (res *v1.NginxOverallRes, err error) {
//	res, err = service.Ad().NginxOverallList(ctx, req)
//	return
//}
//func (c *Controller) NginxSeriesList(ctx context.Context, req *v1.NginxSeriesReq) (res *v1.NginxSeriesRes, err error) {
//	res, err = service.Ad().NginxSeriesList(ctx, req)
//	return
//}
//func (c *Controller) ClicksList(ctx context.Context, req *v1.AdClicksReq) (res *v1.AdClicksRes, err error) {
//	res, err = service.Ad().ClicksList(ctx, req)
//	return
//}
//
//func (c *Controller) ClicksExport(ctx context.Context, req *v1.AdClicksExportReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Ad().ClicksExport(ctx, req)
//	return
//}
//
//func (c *Controller) List(ctx context.Context, req *v1.AdListReq) (res *v1.AdListRes, err error) {
//	res, err = service.Ad().List(ctx, req)
//	return
//}
//
//func (c *Controller) Add(ctx context.Context, req *v1.AdAddReq) (res *v1.AdAddRes, err error) {
//	res, err = service.Ad().Add(ctx, req)
//	return
//}
//
//func (c *Controller) Edit(ctx context.Context, req *v1.AdEditReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Ad().Edit(ctx, req)
//	return
//}
//
//func (c *Controller) Delete(ctx context.Context, req *v1.AdDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Ad().Delete(ctx, req)
//	return
//}
//
//func (c *Controller) One(ctx context.Context, req *v1.AdOneReq) (res *v1.AdOneRes, err error) {
//	res, err = service.Ad().One(ctx, req)
//	return
//}
//
//func (c *Controller) GroupSiteAllList(ctx context.Context, req *v1.AdGroupSiteAllListReq) (res *v1.AdGroupSiteAllListRes, err error) {
//	res, err = service.Ad().GroupSiteAllList(ctx, req)
//	return
//}
//
//func (c *Controller) GroupSiteAdd(ctx context.Context, req *v1.AdGroupSiteAddReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Ad().GroupSiteAdd(ctx, req)
//	return
//}
//
//func (c *Controller) AddBatch(ctx context.Context, req *v1.AdAddBatchReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Ad().AddBatch(ctx, req)
//	return
//}
