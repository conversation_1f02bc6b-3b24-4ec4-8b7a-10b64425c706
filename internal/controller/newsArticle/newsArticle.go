package newsArticle

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

// 添加文章
func (c *Controller) Add(ctx context.Context, req *v1.NewsArticleAddReq) (res *v1.NewsArticleAddRes, err error) {
	res = new(v1.NewsArticleAddRes)
	err = service.NewsArticle().Add(ctx, &v1.NewsArticleAddReq{
		NewArticle: v1.NewArticle{
			Author:      req.Author,
			CategoryId:  req.CategoryId,
			ContentArr:  req.ContentArr,
			CoverImgs:   req.CoverImgs,
			IsDraft:     req.IsDraft,
			IsPublish:   req.IsPublish,
			IsRecommend: req.IsRecommend,
			IsTop:       req.IsTop,
			PublishTime: req.PublishTime,
		},
	})
	return
}

// 编辑文章
func (c *Controller) Edit(ctx context.Context, req *v1.NewsArticleEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsArticle().Edit(ctx, &v1.NewsArticleEditReq{
		NewArticle: v1.NewArticle{
			Author:      req.Author,
			CategoryId:  req.CategoryId,
			ContentArr:  req.ContentArr,
			CoverImgs:   req.CoverImgs,
			IsDraft:     req.IsDraft,
			IsPublish:   req.IsPublish,
			IsRecommend: req.IsRecommend,
			IsTop:       req.IsTop,
		},
		Id: req.Id,
	})
	return
}

// 获取文章列表
func (c *Controller) List(ctx context.Context, req *v1.NewsArticleListReq) (res *v1.NewsArticleListRes, err error) {
	res = new(v1.NewsArticleListRes)
	ret, err := service.NewsArticle().List(ctx, &v1.NewsArticleListReq{
		CategoryId:      req.CategoryId,
		IsPublish:       req.IsPublish,
		IsTop:           req.IsTop,
		IsRecommend:     req.IsRecommend,
		ArticleName:     req.ArticleName,
		CreateTimeBegin: req.CreateTimeBegin,
		CreateTimeEnd:   req.CreateTimeEnd,
		ListReq: v1.ListReq{
			Current:  req.Current,
			PageSize: req.PageSize,
			OrderBy:  req.OrderBy,
			Offset:   req.Offset,
		},
	})
	if err != nil {
		return
	}
	res.List = ret.List
	res.Current = ret.Current
	res.Total = ret.Total
	res.Offset = ret.Offset
	return
}

// 获取文章详情
func (c *Controller) Info(ctx context.Context, req *v1.NewsArticleInfoReq) (res *v1.NewsArticleInfoRes, err error) {
	res = &v1.NewsArticleInfoRes{}
	ret, err := service.NewsArticle().Info(ctx, &v1.NewsArticleInfoReq{
		Id: req.Id,
	})
	if err != nil {
		return
	}
	if ret == nil {
		return
	}
	res.NewArticle = v1.NewArticle{
		Author:      ret.Author,
		CategoryId:  ret.CategoryId,
		ContentArr:  ret.ContentArr,
		CoverImgs:   ret.CoverImgs,
		IsDraft:     ret.IsDraft,
		IsPublish:   ret.IsPublish,
		IsRecommend: ret.IsRecommend,
		IsTop:       ret.IsTop,
		PublishTime: ret.PublishTime,
	}
	return
}

// 删除文章
func (c *Controller) Delete(ctx context.Context, req *v1.NewsArticleDeleteReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsArticle().Delete(ctx, &v1.NewsArticleDeleteReq{
		Ids: req.Ids,
	})
	return
}

// 上线文章
func (c *Controller) SetOffline(ctx context.Context, req *v1.NewsArticleSetOfflineReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsArticle().SetOffline(ctx, &v1.NewsArticleSetOfflineReq{
		Ids: req.Ids,
	})
	return
}

// 下线文章
func (c *Controller) SetOnline(ctx context.Context, req *v1.NewsArticleSetOnlineReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsArticle().SetOnline(ctx, &v1.NewsArticleSetOnlineReq{
		Ids: req.Ids,
	})
	return
}

// 加入头条
func (c *Controller) SetTop(ctx context.Context, req *v1.NewsArticleSetTopReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsArticle().SetTop(ctx, &v1.NewsArticleSetTopReq{
		Ids: req.Ids,
	})
	return
}

// 取消加入头条
func (c *Controller) SetNotTop(ctx context.Context, req *v1.NewsArticleSetNotTopReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsArticle().SetNotTop(ctx, &v1.NewsArticleSetNotTopReq{
		Ids: req.Ids,
	})
	return
}

// 设置推荐
func (c *Controller) SetRecommend(ctx context.Context, req *v1.NewsArticleSetRecommendReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsArticle().SetRecommend(ctx, &v1.NewsArticleSetRecommendReq{
		Ids: req.Ids,
	})
	return
}

// 设置不推荐
func (c *Controller) SetNotRecommend(ctx context.Context, req *v1.NewsArticleSetNotRecommendReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.NewsArticle().SetNotRecommend(ctx, &v1.NewsArticleSetNotRecommendReq{
		Ids: req.Ids,
	})
	return
}

// 草稿箱
func (c *Controller) Draft(ctx context.Context, req *v1.NewsArticleDraftReq) (res *v1.NewsArticleDraftRes, err error) {
	res = new(v1.NewsArticleDraftRes)
	ret, err := service.NewsArticle().Draft(ctx, &v1.NewsArticleDraftReq{
		ArticleName:     req.ArticleName,
		CreateTimeBegin: req.CreateTimeBegin,
		CreateTimeEnd:   req.CreateTimeEnd,
		ListReq: v1.ListReq{
			Current:  req.Current,
			PageSize: req.PageSize,
			OrderBy:  req.OrderBy,
			Offset:   req.Offset,
		},
		UpdateTimeBegin: req.UpdateTimeBegin,
		UpdateTimeEnd:   req.UpdateTimeEnd,
	})
	if err != nil {
		return
	}
	res.List = ret.List
	res.Current = ret.Current
	res.Offset = ret.Offset
	res.Total = ret.Total
	return
}
