package spider

import ()

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

//
//func (c *Controller) SpiderList(ctx context.Context, req *v1.SpiderListReq) (res *v1.SpiderListRes, err error) {
//	res, err = service.Spider().List(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderAdd(ctx context.Context, req *v1.SpiderAddReq) (res *v1.SpiderAddRes, err error) {
//	res, err = service.Spider().Add(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderEdit(ctx context.Context, req *v1.SpiderEditReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Spider().Edit(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderDelete(ctx context.Context, req *v1.SpiderDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Spider().Delete(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderOne(ctx context.Context, req *v1.SpiderOneReq) (res *v1.SpiderOneRes, err error) {
//	res, err = service.Spider().One(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderFirewallList(ctx context.Context, req *v1.SpiderFirewallListReq) (res *v1.SpiderFirewallListRes, err error) {
//	res, err = service.Spider().FirewallList(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderFirewallAdd(ctx context.Context, req *v1.SpiderFirewallAddReq) (res *v1.SpiderFirewallAddRes, err error) {
//	res, err = service.Spider().FirewallAdd(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderFirewallEdit(ctx context.Context, req *v1.SpiderFirewallEditReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Spider().FirewallEdit(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderFirewallDelete(ctx context.Context, req *v1.SpiderFirewallDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Spider().FirewallDelete(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderLogList(ctx context.Context, req *v1.SpiderLogListReq) (res *v1.SpiderLogListRes, err error) {
//	res, err = service.Spider().LogList(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderLogPullBlack(ctx context.Context, req *v1.SpiderLogPullBlackReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Spider().LogPullBlack(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderLogCheck(ctx context.Context, req *v1.SpiderLogCheckReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Spider().SpiderLogCheck(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderLogBlackList(ctx context.Context, req *v1.SpiderLogBlackListReq) (res *v1.SpiderLogBlackListRes, err error) {
//	res, err = service.Spider().LogBlackList(ctx, req)
//	return
//}
//
//func (c *Controller) SpiderLogRemoveBlack(ctx context.Context, req *v1.SpiderLogRemoveBlackReq) (res *v1.EmptyDataRes, err error) {
//	res, err = service.Spider().LogRemoveBlack(ctx, req)
//	return
//}
