package videoTopic

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

// 新增
func (c *Controller) Add(ctx context.Context, req *v1.VideoTopicAddReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.VideoTopic().Add(ctx, req)
	return
}

// 编辑
func (c *Controller) Edit(ctx context.Context, req *v1.VideoTopicEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.VideoTopic().Edit(ctx, req)
	return
}

// 详情
func (c *Controller) Info(ctx context.Context, req *v1.VideoTopicInfoReq) (res *v1.VideoTopicInfoRes, err error) {
	res = new(v1.VideoTopicInfoRes)
	res, err = service.VideoTopic().Info(ctx, req)
	return
}

// 列表
func (c *Controller) List(ctx context.Context, req *v1.VideoTopicListReq) (res *v1.VideoTopicListRes, err error) {
	res = new(v1.VideoTopicListRes)
	ret, err := service.VideoTopic().List(ctx, req)
	if err != nil {
		return
	}
	res.List = ret.List
	res.Current = req.Current
	res.Total = ret.Total
	res.Offset = req.Offset
	return
}

// 删除
func (c *Controller) Delete(ctx context.Context, req *v1.VideoTopicDeleteReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	err = service.VideoTopic().Delete(ctx, req)
	return
}
