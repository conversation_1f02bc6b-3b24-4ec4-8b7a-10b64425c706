package permissionV2

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/util/gutil"
	"github.com/shopspring/decimal"
	"gtcms/internal/service"
	"reflect"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	v1 "gtcms/api/v1"

	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	do "gtcms/internal/model/do/admin"
	entity "gtcms/internal/model/entity/admin"
)

type (
	sPermissionV2 struct{}
)

func init() {
	service.RegisterPermissionV2(New())
}

var nodeCl = dao.PermissionNode.Columns()
var attrsCl = dao.PermissionAttrs.Columns()

func New() service.IPermissionV2 {
	s := &sPermissionV2{}
	//	_ = s.fillData()
	return s
}

func (s *sPermissionV2) fillData() (err error) {
	ctx := context.Background()
	var recs []entity.PermissionNode
	err = dao.PermissionNode.Ctx(ctx).WhereLike(nodeCl.Name, "%.list%").Scan(&recs)
	if err != nil {
		return
	}

	for _, r := range recs {
		names := strings.Split(r.Name, ".")
		labels := strings.Split(r.Label, "-")
		addItems := []string{"create", "delete", "edit"}
		addItems2 := []string{"新建", "删除", "编辑"}
		for i, v := range addItems {
			node := do.PermissionNode{
				Id:    nil,
				PId:   r.PId,
				Name:  names[0] + "." + v,
				Label: labels[0] + "-" + addItems2[i],
			}
			_, err = dao.PermissionNode.Ctx(ctx).Data(node).InsertAndGetId()
		}
	}
	return
}

func (s *sPermissionV2) Create(ctx context.Context, in *v1.PermissionMgrAddReq) (err error) {
	var (
		exist bool
	)

	if in.PId != 0 {
		exist, err = s.checkIdExisted(ctx, in.PId)
		if err != nil {
			return err
		}
		if !exist {
			return gerror.New(g.I18n().Tf(ctx, fmt.Sprintf("Parent id=%d not exist", in.PId)))
		}
	}

	exist, err = s.checkNameExisted(ctx, in.Name, nil)
	if err != nil {
		return err
	}
	if exist {
		return gerror.New(g.I18n().Tf(ctx, fmt.Sprintf("permission name repeat! VAL=%s ", in.Name)))
	}

	// get p_ids
	//pIds, err := s.getNodePath(ctx, in.PId)

	var node do.PermissionNode
	var attrs do.PermissionAttrs
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err error
		node = do.PermissionNode{
			Id:    nil,
			PId:   in.PId,
			Name:  in.Name,
			Label: in.Label,
			//PIds:    pIds,
			OrderBy: in.OrderBy,
		}
		var lastInsertId int64
		lastInsertId, err = dao.PermissionNode.Ctx(ctx).TX(tx).Data(node).InsertAndGetId()
		if err != nil {
			return err
		}

		if !g.IsEmpty(in.UrlPath) || !g.IsEmpty(in.MapMaskedFields) {
			//var maskBytes []byte
			//if !g.IsEmpty(in.MapMaskedFields) {
			//	maskBytes, err = gjson.Marshal(in.MapMaskedFields)
			//	if err != nil {
			//		return gerror.New(g.I18n().Tf(ctx, fmt.Sprintf("MapMaskedFields Marshal err =%s", err)))
			//	}
			//}
			attrs = do.PermissionAttrs{
				PermissionId: lastInsertId,
				UrlPath:      in.UrlPath,
				//MaskedFields: string(maskBytes),
				MaskedFields: "{}",
				OrderBy:      in.OrderBy,
			}
			_, err = dao.PermissionAttrs.Ctx(ctx).TX(tx).Data(attrs).Insert()
		}
		return err
	})

	if err == nil {
		json1, _ := gjson.EncodeString(node)
		json2, _ := gjson.EncodeString(attrs)
		input := &model.AdminModifyInput{
			ModifyItem:      fmt.Sprintf("新增:%s", in.Name),
			NewRecordOrAttr: json1 + json2,
		}
		_ = service.AccountNodeConfig().PushLog(ctx, input)
	}
	return
}

func (s *sPermissionV2) Edit(ctx context.Context, in *v1.PermissionMgrEditReq) (err error) {
	var (
		exist bool
	)
	exist, err = s.checkIdExisted(ctx, in.Id)
	if err != nil {
		return err
	}
	if !exist {
		return gerror.New(g.I18n().Tf(ctx, fmt.Sprintf("Permission Id=%d not exist", in.Id)))
	}

	if in.PId != 0 {
		exist, err = s.checkIdExisted(ctx, in.PId)
		if err != nil {
			return err
		}
		if !exist {
			return gerror.New(g.I18n().Tf(ctx, fmt.Sprintf("Parent permission Id=%d not exist", in.PId)))
		}
	}

	exist, err = s.checkNameExisted(ctx, in.Name, []uint{in.Id})
	if err != nil {
		return err
	}
	if exist {
		return gerror.New(g.I18n().Tf(ctx, fmt.Sprintf("permission name repeat! VAL=%s", in.Name)))
	}

	var node, oldNode do.PermissionNode
	_ = dao.PermissionNode.Ctx(ctx).WherePri(in.Id).Scan(&oldNode)
	var attrs, oldAttrs do.PermissionAttrs
	_ = dao.PermissionAttrs.Ctx(ctx).Where(attrsCl.PermissionId, in.Id).Scan(&oldAttrs)

	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err error
		node = do.PermissionNode{
			Id:      in.Id,
			PId:     in.PId,
			Name:    in.Name,
			Label:   in.Label,
			OrderBy: in.OrderBy,
		}
		_, err = dao.PermissionNode.Ctx(ctx).TX(tx).WherePri(in.Id).Data(node).Update()
		if err != nil {
			return err
		}

		if !g.IsEmpty(in.UrlPath) || !g.IsEmpty(in.MapMaskedFields) {
			//var maskBytes []byte
			//if !g.IsEmpty(in.MapMaskedFields) {
			//	maskBytes, err = gjson.Marshal(in.MapMaskedFields)
			//	if err != nil {
			//		return gerror.New(g.I18n().Tf(ctx, fmt.Sprintf("MapMaskedFields Marshal err =%s", err)))
			//	}
			//}

			attrs = do.PermissionAttrs{
				PermissionId: in.Id,
				UrlPath:      in.UrlPath,
				//MaskedFields: string(maskBytes),
				MaskedFields: "{}",
				OrderBy:      in.OrderBy,
			}
			_, err = dao.PermissionAttrs.Ctx(ctx).TX(tx).Where(attrsCl.PermissionId, in.Id).Data(attrs).Save()
		}
		return err
	})

	if err == nil {
		mergeAttrs := make(map[string]interface{})
		gutil.MapMerge(mergeAttrs, gconv.Map(oldNode), gconv.Map(oldAttrs))
		newAttrs := make(map[string]interface{})
		gutil.MapMerge(newAttrs, gconv.Map(node), gconv.Map(attrs))
		input := &model.AdminModifyInput{
			OldRecordOrAttr: mergeAttrs,
			NewRecordOrAttr: newAttrs,
		}
		_ = service.AccountNodeConfig().PushLog(ctx, input)
	}

	return
}

func (s *sPermissionV2) Delete(ctx context.Context, in *v1.PermissionMgrDeleteReq) (err error) {
	var (
		exist bool
	)
	exist, err = s.checkIdExisted(ctx, in.Id)
	if err != nil {
		return err
	}
	if !exist {
		return gerror.New(g.I18n().Tf(ctx, fmt.Sprintf("permission Id=%d not exist", in.Id)))
	}

	// 检查 子权限
	exist, err = s.checkExistSub(ctx, in.Id)
	if err != nil {
		return err
	}
	if exist {
		return gerror.New(g.I18n().T(ctx, "Prohibit deletion when sub-permissions exist!"))
	}

	var oldNode do.PermissionNode
	dao.PermissionNode.Ctx(ctx).WherePri(in.Id).Scan(&oldNode)
	var oldAttrs do.PermissionAttrs
	dao.PermissionAttrs.Ctx(ctx).Where(attrsCl.PermissionId, in.Id).Scan(&oldAttrs)

	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err = dao.PermissionNode.Ctx(ctx).TX(tx).WherePri(in.Id).Delete()
		if err != nil {
			return err
		}
		_, err = dao.PermissionAttrs.Ctx(ctx).TX(tx).Where(attrsCl.PermissionId, in.Id).Delete()
		return err
	})

	if err == nil {
		mergeAttrs := make(map[string]interface{})
		gutil.MapMerge(mergeAttrs, gconv.Map(oldNode), gconv.Map(oldAttrs))
		input := &model.AdminModifyInput{
			ModifyItem:      fmt.Sprintf("删除权限:%s", oldNode.Name),
			OldRecordOrAttr: mergeAttrs,
		}
		_ = service.AccountNodeConfig().PushLog(ctx, input)
	}

	return
}

func (s *sPermissionV2) List(ctx context.Context, in *v1.PermissionMgrListReq) (out *v1.PermissionMgrListRes, err error) {
	out = &v1.PermissionMgrListRes{}
	out.Current = in.Current

	out.Items, err = s.GetPermTree(ctx)
	if err != nil {
		return
	}
	out.Total = len(out.Items)

	var start, end int
	if in.Current <= 0 {
		in.Current = 1
	}
	start = (in.Current - 1) * in.PageSize
	if start > out.Total {
		return
	}

	end = start + in.PageSize
	if end > out.Total {
		end = out.Total
	}
	out.Items = out.Items[start:end]
	return
}

func (s *sPermissionV2) IsUrlOpen(ctx context.Context, url string) (isOpen bool, err error) {
	var count int
	count, err = dao.PermissionAttrs.Ctx(ctx).Where(attrsCl.UrlPath, url).Count()
	if err != nil {
		return
	}

	return count == 0, nil
}

func (s *sPermissionV2) GetIDByNodeUrl(ctx context.Context, ids []uint, nodePath, url string) (permId uint, err error) {
	var recs []*entity.PermissionAttrs
	cl := dao.PermissionAttrs.Columns()
	err = dao.PermissionAttrs.Ctx(ctx).WhereIn(cl.PermissionId, ids).Scan(&recs)
	if err != nil {
		return
	}

	var nodes []*entity.PermissionNode
	err = dao.PermissionNode.Ctx(ctx).Scan(&nodes)
	if err != nil {
		return
	}
	if nodes == nil || len(nodes) == 0 {
		return
	}

	var count int
	var sameUrlRecs []*entity.PermissionAttrs
	for _, v := range recs {
		if url == v.UrlPath {
			sameUrlRecs = append(sameUrlRecs, v)
			permId = v.PermissionId
			count++
		}
	}
	if count == 1 {
		return
	}

	for _, v := range sameUrlRecs {
		path := s.getNodePath(nodes, v.PermissionId)
		if nodePath == path && url == v.UrlPath {
			permId = v.PermissionId
			return
		}
	}
	return
}

func (s *sPermissionV2) GetSubs(ctx context.Context) (err error) {
	var nodes []*entity.PermissionNode
	err = dao.PermissionNode.Ctx(ctx).Scan(&nodes)
	if err != nil {
		return
	}
	if nodes == nil || len(nodes) == 0 {
		return
	}

	var attrs []*entity.PermissionAttrs
	err = dao.PermissionAttrs.Ctx(ctx).Scan(&attrs)
	if err != nil {
		return
	}

	mapId2Attrs := make(map[uint]*entity.PermissionAttrs)
	for _, v := range attrs {
		mapId2Attrs[v.PermissionId] = v
	}

	return
}

func (s *sPermissionV2) getNodePath(nodes []*entity.PermissionNode, permId uint) (path string) {
	var parentId uint
	var paths []string
	for _, v := range nodes {
		if v.Id == permId {
			parentId = v.PId
			paths = append(paths, v.Name)
			break
		}
	}

	var i int = 0
	for parentId != 0 {
		i++
		if i == 100000 {
			break
		}
		for _, v := range nodes {
			if v.Id == parentId {
				parentId = v.PId
				paths = append(paths, v.Name)
				break
			}
		}
	}

	for i := len(paths) - 1; i >= 0; i-- {
		path += "/"
		path += paths[i]
	}
	return
}

func (s *sPermissionV2) Detail(ctx context.Context, id uint) (node v1.PermissionNode, err error) {
	var main = entity.PermissionNode{}
	err = dao.PermissionNode.Ctx(ctx).WherePri(id).Scan(&main)
	if err != nil {
		return
	}

	var attrs = entity.PermissionAttrs{}
	err = dao.PermissionNode.Ctx(ctx).Where(attrsCl.PermissionId, id).Scan(&attrs)
	if err != nil {
		return
	}

	_ = gconv.Scan(&node, main)
	node.PermissionAttrs = &v1.PermissionAttrs{
		UrlPath:         attrs.UrlPath,
		MapMaskedFields: gconv.MapStrStr(attrs.MaskedFields),
	}

	return
}

//func (s *sPermissionV2) Subs(ctx context.Context, pId uint) (subs []entity.Permission, err error) {
//	// check pId?
//	err = dao.PermissionNode.Ctx(ctx).Where(nodeCl.PId, pId).Order(nodeCl.Id).Scan(&subs)
//	if err != nil {
//		return
//	}
//	if subs == nil {
//		//subs = []entity.Permission{}
//	}
//	return
//}

func (s *sPermissionV2) checkNameExisted(ctx context.Context, name string, excludeId []uint) (bool, error) {
	d := dao.PermissionNode.Ctx(ctx).Where(nodeCl.Name, name)
	// 排除指定的ids
	if len(excludeId) > 0 {
		d = d.WhereNotIn(nodeCl.Id, excludeId)
	}
	count, err := d.Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *sPermissionV2) checkIdExisted(ctx context.Context, id uint) (bool, error) {
	count, err := dao.PermissionNode.Ctx(ctx).WherePri(id).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *sPermissionV2) checkExistSub(ctx context.Context, id uint) (bool, error) {
	count, err := dao.PermissionNode.Ctx(ctx).Where(nodeCl.PId, id).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *sPermissionV2) CheckRolePermissionCfg(ctx context.Context, cfg []*v1.RolePermissionCfg) error {
	if g.IsEmpty(cfg) {
		return nil
	}

	for _, v := range cfg {
		exist, err := s.checkIdExisted(ctx, v.PermissionId)
		if err != nil {
			return err
		}
		if !exist {
			return gerror.New(g.I18n().Tf(ctx, fmt.Sprintf("Permission Id=%d not exist", v.PermissionId)))
		}
	}

	return nil
}

func (s *sPermissionV2) GetPermNames(ctx context.Context, ids []uint) (names []string, err error) {
	var recs []*entity.PermissionNode
	err = dao.PermissionNode.Ctx(ctx).Scan(&recs)
	if err != nil {
		return
	}

	mapIDNode := make(map[uint]*entity.PermissionNode, len(recs))
	for _, v := range recs {
		mapIDNode[v.Id] = v
	}

	mapName := make(map[string]struct{})
	for _, id := range ids {
		reverseGetName(id, mapIDNode, mapName, 0)
	}

	for key, _ := range mapName {
		names = append(names, key)
	}

	return
}

func reverseGetName(id uint, mapIDNode map[uint]*entity.PermissionNode, mapName map[string]struct{}, callCnt int) {
	callCnt++
	if callCnt > 10000 {
		return
	}
	node, ok := mapIDNode[id]
	if ok {
		mapName[node.Name] = struct{}{}
		reverseGetName(node.PId, mapIDNode, mapName, callCnt)
	}

	return
}

func (s *sPermissionV2) GetPermTree(ctx context.Context) (nodeTree []*v1.PermissionNode, err error) {
	var nodes []*entity.PermissionNode
	err = dao.PermissionNode.Ctx(ctx).OrderDesc(nodeCl.OrderBy).OrderAsc(nodeCl.Id).Scan(&nodes)
	if err != nil {
		return
	}
	mapIDNode := make(map[uint]*entity.PermissionNode, len(nodes))
	for _, v := range nodes {
		mapIDNode[v.Id] = v
	}

	var attrs []*entity.PermissionAttrs
	err = dao.PermissionAttrs.Ctx(ctx).Scan(&attrs)
	if err != nil {
		return
	}
	mapIDAttrs := make(map[uint]*entity.PermissionAttrs, len(attrs))
	for _, v := range attrs {
		mapIDAttrs[v.PermissionId] = v
	}

	return reverseGetTree(nodes, 0, 0, mapIDAttrs)
}

func reverseGetTree(allNode []*entity.PermissionNode, pid uint, callCnt int, mapIDAttrs map[uint]*entity.PermissionAttrs) (recs []*v1.PermissionNode, err error) {
	callCnt++
	if callCnt > 10000 {
		return
	}

	var levelNodes []*v1.PermissionNode
	for _, v := range allNode {
		if v.PId != pid {
			continue
		}
		var temp = &v1.PermissionNode{}
		err = gconv.Scan(v, &temp)
		if err != nil {
			return
		}

		attrs, ok := mapIDAttrs[v.Id]
		if ok {
			temp.PermissionAttrs = &v1.PermissionAttrs{
				UrlPath: attrs.UrlPath,
			}
			if !g.IsEmpty(attrs.MaskedFields) {
				err = gconv.Scan(attrs.MaskedFields, &temp.PermissionAttrs.MapMaskedFields)
				if err != nil {
					return
				}
			}
		}

		temp.ChildNodes, err = reverseGetTree(allNode, v.Id, callCnt, mapIDAttrs)
		if err != nil {
			return
		}
		levelNodes = append(levelNodes, temp)
	}

	return levelNodes, nil
}

func (s *sPermissionV2) MaskingRespPacket(ctx context.Context, reflectValue reflect.Value, fieldPath []string, index int, isIntegerMask *bool) {
	length := len(fieldPath)
	if index >= length {
		return
	}
	fieldName := fieldPath[index]
	g.Log().Line().Info(ctx, "** MaskingRespPacket input kind=", reflectValue.Kind(), "type=", reflectValue.Type(), " CanSet=", reflectValue.CanSet())

	// 结构体 ： 判断 指针 循环获取原类型
	// 复合结构体： 1 数组： 获取单元素 ； 循环获取  2 结构体：直接比对字段进行处理  3 map：根据特征函数 设置值；
	// 简单结构体： 找到对应名称： IsSet， 设置值；整形值进行屏蔽；
	var (
		reflectKind = reflectValue.Kind()
	)
	for reflectKind == reflect.Ptr {
		reflectValue = reflectValue.Elem()
		reflectKind = reflectValue.Kind()
	}
	//if reflectValue.Kind() != reflect.Pointer {
	//	reflectValue = reflectValue.FieldByName(fieldName)
	//} else {
	//	reflectValue = reflect.Indirect(reflectValue).FieldByName(fieldName)
	//}
	switch reflectValue.Kind() {
	case reflect.Array, reflect.Slice:
		if reflectValue.Len() == 0 {
			return
		}
		g.Log().Line().Debug(ctx, "MaskingRespPacket Array:", reflectValue.Type(), fieldName)
		for i := 0; i < reflectValue.Len(); i++ {
			s.MaskingRespPacket(ctx, reflectValue.Index(i), fieldPath, index+1, isIntegerMask)
		}
	case reflect.Struct:
		// 类型断言 decimal.Decimal
		if "decimal.Decimal" == reflectValue.Type().String() {
			reflectValue.Set(reflect.ValueOf(decimal.Zero))
			*isIntegerMask = true
			return
		}
		reflectValue = reflectValue.FieldByName(fieldName)
		if !reflectValue.IsValid() {
			//g.Log().Warning(ctx, "MaskingRespPacket reflectValue is not valid, FieldByName=", fieldName)
			return
		}
		g.Log().Line().Debug(ctx, "MaskingRespPacket Struct:", fieldName, reflectValue.Type(), reflectValue.CanSet())
		s.MaskingRespPacket(ctx, reflectValue, fieldPath, index, isIntegerMask)
	case reflect.Map:
		g.Log().Line().Debug(ctx, "MaskingRespPacket Map:", fieldName, reflectValue.Type(), reflectValue.CanSet())
		reflectValue.MapRange()
		iter := reflectValue.MapRange()
		for iter.Next() {
			if fieldName == iter.Key().String() {
				s.MaskingRespPacket(ctx, iter.Value(), fieldPath, index, isIntegerMask)
			}
		}
	case reflect.String:
		reflectValue.SetString("******")
		g.Log().Line().Info(ctx, "maskResponseFields maskFiled success! fieldName=", fieldName, "type=", reflectValue.Type())
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Uint,
		reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		reflectValue.SetInt(0)
		*isIntegerMask = true
	case reflect.Float32, reflect.Float64:
		reflectValue.SetFloat(0)
		*isIntegerMask = true
	case reflect.Complex64, reflect.Complex128:
		reflectValue.SetComplex(0)
		*isIntegerMask = true
	case reflect.Bool:
		reflectValue.SetBool(false)
		*isIntegerMask = true
	default:
		g.Log().Line().Warning(ctx, "*** MaskingRespPacket Unsupported kind=", reflectValue.Kind())
	}
	return
}

func dealIndex(ctx context.Context, reflectValue reflect.Value, index *int) {
	var (
		reflectKind = reflectValue.Kind()
	)
	for reflectKind == reflect.Ptr {
		reflectValue = reflectValue.Elem()
		reflectKind = reflectValue.Kind()
	}

	switch reflectValue.Kind() {
	case reflect.Array, reflect.Slice:
		//g.Log().Line().Debug(ctx, "MaskingRespPacket Array:", reflectValue.Type(), fieldName)
	case reflect.Struct:
		//*index++
		//g.Log().Line().Debug(ctx, "dealIndex Struct, increase index=%d", reflectValue.Type(), index)
	case reflect.Map:
		//*index++
		//g.Log().Line().Debug(ctx, "dealIndex Map, increase index=%d", reflectValue.Type(), index)
	case reflect.String:
		*index++
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Uint,
		reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		*index++
	case reflect.Float32, reflect.Float64:
		*index++
	default:
		g.Log().Line().Warning(ctx, "*** dealIndex Unsupported kind=", reflectValue.Kind())
	}
	return
}

func (s *sPermissionV2) MaskingRespPacket2(ctx context.Context, respItf interface{}, fieldPath []string) {
	length := len(fieldPath)
	var inputVal = respItf
	var subStruct interface{}
	var found bool
	for i, fieldName := range fieldPath {
		isLast := i == length-1
		subStruct, found = gutil.ItemValue(inputVal, fieldName)
		if !found {
			g.Log().Line().Warning(ctx, "** MaskingRespPacket fieldName=", fieldName, " not find")
			return
		}
		if !isLast {
			inputVal = subStruct
			continue
		} else {
			break
		}
	}

	s.maskRespField(ctx, subStruct)
	return
}

func (s *sPermissionV2) maskRespField(ctx context.Context, item interface{}) {
	//var reflectValue reflect.Value
	//if v, ok := respValues.(reflect.Value); ok {
	//	reflectValue = v
	//} else {
	//	reflectValue = reflect.ValueOf(respValues)
	//}
	//
	//var (
	//	//reflectValue = reflect.ValueOf(respValues)
	//	reflectKind = reflectValue.Kind()
	//)
	//for reflectKind == reflect.Ptr {
	//	reflectValue = reflectValue.Elem()
	//	reflectKind = reflectValue.Kind()
	//}
	var reflectValue reflect.Value
	if v, ok := item.(reflect.Value); ok {
		reflectValue = v
	} else {
		reflectValue = reflect.ValueOf(item)
	}
	reflectKind := reflectValue.Kind()
	if reflectKind == reflect.Interface {
		reflectValue = reflectValue.Elem()
		reflectKind = reflectValue.Kind()
	}
	for reflectKind == reflect.Ptr {
		reflectValue = reflectValue.Elem()
		reflectKind = reflectValue.Kind()
	}

	switch reflectValue.Kind() {
	case reflect.Array, reflect.Slice:
		if reflectValue.Len() == 0 {
			return
		}
		for i := 0; i < reflectValue.Len(); i++ {
			s.maskRespField(ctx, reflectValue.Index(i))
		}
	case reflect.Interface:
		s.maskRespField(ctx, reflectValue.Elem())
	case reflect.String:
		reflectValue.SetString("******")
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Uint,
		reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		reflectValue.SetInt(0)
	case reflect.Float32, reflect.Float64:
		reflectValue.SetFloat(0)
	default:
		g.Log().Line().Warning(ctx, "*** MaskingRespPacket Unsupported kind=", reflectValue.Kind(), reflectValue.Type())
	}
	return
}
