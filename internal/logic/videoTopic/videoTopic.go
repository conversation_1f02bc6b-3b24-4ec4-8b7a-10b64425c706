package videoTopic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"

	dao "gtcms/internal/dao/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"

	"gtcms/internal/service"
	"time"
)

type sVideoTopic struct{}

func init() {
	service.RegisterVideoTopic(New())
}

func New() *sVideoTopic {
	return &sVideoTopic{}
}

// Add 新增
func (s *sVideoTopic) Add(ctx context.Context, req *v1.VideoTopicAddReq) error {
	// 参数校验
	if 0 >= req.Sort {
		return gerror.New("排序值不能为空")
	}
	admin, _ := service.Utility().GetSelf(ctx)
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	topicData := entity.VideoPlaylists{
		CoverUrl:   req.TopicImgs,
		Creater:    admin.Id,
		CreateName: admin.Account,
		CreateTime: currentTime,
		UpdateTime: currentTime,
		SortOrder:  req.Sort,
		IsVisible:  gconv.Uint(req.IsAppShow),
		VideoCount: gconv.Uint(len(req.VideoArr)),
	}
	var topicLanguageData = make([]entity.VideoPlaylistLanguages, 0)
	// 校验视频名称和内容
	for _, item := range req.NameArr {
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return gerror.New("语言类型错误")
		}
		one := entity.VideoPlaylistLanguages{
			Name:       item.TopicName,
			ShortTitle: item.ShortName,
			LanguageId: gconv.Uint(item.LanguageType),
			CreateTime: currentTime,
			UpdateTime: currentTime,
		}
		topicLanguageData = append(topicLanguageData, one)
	}
	// 写入表
	err := dao.VideoPlaylists.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		id, err1 := dao.VideoPlaylists.Ctx(ctx).Data(topicData).InsertAndGetId()
		if err1 != nil {
			return err1
		}
		// 组装数据
		for key := range topicLanguageData {
			topicLanguageData[key].PlaylistId = gconv.Uint(id)
		}
		fmt.Println(topicLanguageData, 11111111111)
		// 批量插入
		_, err1 = dao.VideoPlaylistLanguages.Ctx(ctx).Data(topicLanguageData).Insert()
		if err1 != nil {
			return err1
		}
		// 写入news_topic_article表
		topicVideoData := g.List{}
		for _, videoItem := range req.VideoArr {
			topicVideoData = append(topicVideoData, g.Map{
				dao.VideoPlaylistRelations.Columns().PlaylistId: gconv.Uint(id),
				dao.VideoPlaylistRelations.Columns().VideoId:    videoItem.VideoId,
				dao.VideoPlaylistRelations.Columns().VideoName:  videoItem.VideoName,
				dao.VideoPlaylistRelations.Columns().CreateTime: currentTime,
				dao.VideoPlaylistRelations.Columns().UpdateTime: currentTime,
			})
		}
		_, err1 = dao.VideoPlaylistRelations.Ctx(ctx).Data(topicVideoData).Insert()
		return err1
	})
	return err
}

// Edit 编辑
func (s *sVideoTopic) Edit(ctx context.Context, req *v1.VideoTopicEditReq) error {
	// 参数校验
	if 0 >= req.Sort {
		return gerror.New("排序值不能为空")
	}
	if 0 >= req.Id {
		return gerror.New("专题id不能为空")
	}
	// 获取专题信息
	var topic entity.VideoPlaylists
	err := dao.VideoPlaylists.Ctx(ctx).Where(dao.VideoPlaylists.Columns().Id, req.Id).Scan(&topic)
	if err != nil {
		return err
	}
	if 0 >= topic.Id {
		return gerror.New("专题不存在")
	}
	admin, _ := service.Utility().GetSelf(ctx)
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	topicData := g.Map{
		dao.VideoPlaylists.Columns().CoverUrl:   req.TopicImgs,
		dao.VideoPlaylists.Columns().Creater:    admin.Id,
		dao.VideoPlaylists.Columns().CreateName: admin.Account,
		dao.VideoPlaylists.Columns().UpdateTime: currentTime,
		dao.VideoPlaylists.Columns().SortOrder:  req.Sort,
		dao.VideoPlaylists.Columns().IsVisible:  req.IsAppShow,
		dao.VideoPlaylists.Columns().VideoCount: gconv.Uint(len(req.VideoArr)),
	}
	var topicLanguageData = make([]entity.VideoPlaylistLanguages, 0)
	var topicVideoData = make([]entity.VideoPlaylistRelations, 0)
	// 校验视频名称和内容
	for _, item := range req.NameArr {
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return gerror.New("语言类型错误")
		}
		one := entity.VideoPlaylistLanguages{
			Name:       item.TopicName,
			ShortTitle: item.ShortName,
			CreateTime: currentTime,
			UpdateTime: currentTime,
			LanguageId: gconv.Uint(item.LanguageType),
		}
		topicLanguageData = append(topicLanguageData, one)
	}
	// 写入表
	err = dao.VideoPlaylists.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		_, err1 = dao.VideoPlaylists.Ctx(ctx).Where(dao.VideoPlaylists.Columns().Id, req.Id).Data(topicData).Update()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.VideoPlaylistLanguages.Ctx(ctx).Where(dao.VideoPlaylistLanguages.Columns().PlaylistId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		// 组装数据
		for key := range topicLanguageData {
			topicLanguageData[key].PlaylistId = gconv.Uint(req.Id)
		}
		// 批量插入
		_, err1 = dao.VideoPlaylistLanguages.Ctx(ctx).Data(topicLanguageData).Insert()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.VideoPlaylistRelations.Ctx(ctx).Where(dao.VideoPlaylistRelations.Columns().PlaylistId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		// 写入news_topic_article表
		for _, videoItem := range req.VideoArr {
			topicVideoData = append(topicVideoData, entity.VideoPlaylistRelations{
				PlaylistId: gconv.Uint(req.Id),
				VideoId:    videoItem.VideoId,
				VideoName:  videoItem.VideoName,
				CreateTime: currentTime,
				UpdateTime: currentTime,
			})
		}
		_, err1 = dao.VideoPlaylistRelations.Ctx(ctx).Data(topicVideoData).Insert()
		return err1
	})
	return err
}

// Info 详情
func (s *sVideoTopic) Info(ctx context.Context, req *v1.VideoTopicInfoReq) (out *v1.VideoTopicInfoRes, err error) {
	out = new(v1.VideoTopicInfoRes)
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("参数错误")
	}
	var topic entity.VideoPlaylists
	err = dao.VideoPlaylists.Ctx(ctx).Where(dao.VideoPlaylists.Columns().Id, req.Id).Scan(&topic)
	if err != nil {
		return out, err
	}
	if 0 >= topic.Id {
		return out, gerror.New("专题不存在")
	}
	// 获取多语言列表
	var topicLanguageList []entity.VideoPlaylistLanguages
	err = dao.VideoPlaylistLanguages.Ctx(ctx).Where(dao.VideoPlaylistLanguages.Columns().PlaylistId, req.Id).Scan(&topicLanguageList)
	if err != nil {
		return out, err
	}
	// 获取关联的视频
	var videoList []entity.VideoPlaylistRelations
	err = dao.VideoPlaylistRelations.Ctx(ctx).Where(dao.VideoPlaylistRelations.Columns().PlaylistId, req.Id).Scan(&videoList)
	if err != nil {
		return out, err
	}
	var nameArr = make([]v1.VideoTopicNameArrItem, 0)
	var videoArr = make([]v1.VideoTopicArticleArrItem, 0)
	for _, topicLanguage := range topicLanguageList {
		nameArr = append(nameArr, v1.VideoTopicNameArrItem{
			LanguageType: gconv.Int(topicLanguage.LanguageId + 1), // 0特殊处理
			ShortName:    topicLanguage.ShortTitle,
			TopicName:    topicLanguage.Name,
		})
	}
	for _, topicArticle := range videoList {
		videoArr = append(videoArr, v1.VideoTopicArticleArrItem{
			VideoId:   topicArticle.VideoId,
			VideoName: topicArticle.VideoName,
		})
	}
	// 组装返回值
	out = &v1.VideoTopicInfoRes{
		Id: req.Id,
		VideoTopic: v1.VideoTopic{
			NameArr:   nameArr,
			TopicImgs: topic.CoverUrl,
			IsAppShow: gconv.Int(topic.IsVisible),
			Sort:      topic.SortOrder,
			VideoArr:  videoArr,
		},
	}
	return
}

// List 列表
func (s *sVideoTopic) List(ctx context.Context, req *v1.VideoTopicListReq) (out *v1.VideoTopicListRes, err error) {
	out = new(v1.VideoTopicListRes)
	// 获取语言
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))
	orm := dao.VideoPlaylists.Ctx(ctx).Where(dao.VideoPlaylists.Columns().DeleteTime, consts.Zero)
	if "" != req.TopicName {
		type topicLanguage struct {
			PlaylistId int `json:"playlist_id"`
		}
		var topicLanguageList []*topicLanguage
		err = dao.VideoPlaylistLanguages.Ctx(ctx).
			WhereLike(dao.VideoPlaylistLanguages.Columns().Name, "%"+req.TopicName+"%").
			Scan(&topicLanguageList)
		if err != nil {
			return
		}
		topicIds := gutil.ListItemValuesUnique(topicLanguageList, "PlaylistId")
		if 0 < len(topicIds) {
			orm = orm.WhereIn(dao.VideoPlaylists.Columns().Id, topicIds)
		}
	}
	// 获取总数
	out.Total, err = orm.Count()
	if err != nil {
		out.List = []v1.VideoTopicListItem{}
		return out, err
	}
	var topicList []*entity.VideoPlaylists
	orm = orm.OrderDesc(dao.VideoPlaylists.Columns().CreateTime)
	orm = orm.Page(req.Current, req.PageSize)
	err = orm.Scan(&topicList)
	if err != nil {
		out.List = []v1.VideoTopicListItem{}
		return out, err
	}
	// 提取专题id
	topicIds := gutil.ListItemValuesUnique(topicList, "Id")
	// 获取支持的语言类型
	type topicLanguage struct {
		PlaylistId int    `json:"playlist_id"`
		LanguageId int    `json:"language_id"`
		Name       string `json:"name"`
		ShortTitle string `json:"short_title"`
	}
	var topicLanguageList []topicLanguage
	err = dao.VideoPlaylistLanguages.Ctx(ctx).
		WhereIn(dao.VideoPlaylistLanguages.Columns().PlaylistId, topicIds).
		Scan(&topicLanguageList)
	if err != nil {
		out.List = []v1.VideoTopicListItem{}
		return out, err
	}
	topicLanguageMap := make(map[int]map[int]topicLanguage)
	for _, v := range topicLanguageList {
		if _, ok := topicLanguageMap[v.PlaylistId]; !ok {
			topicLanguageMap[v.PlaylistId] = make(map[int]topicLanguage)
		}
		topicLanguageMap[v.PlaylistId][v.LanguageId] = v
	}
	// 组装返回值
	for _, v := range topicList {
		// 将时间戳转为字符串
		createTime := time.Unix(gconv.Int64(v.CreateTime), 0).Format("2006-01-02 15:04:05")
		one := v1.VideoTopicListItem{
			Id:         v.Id,
			VideoNum:   v.VideoCount,
			ViewNum:    gconv.Int(v.ViewCount),
			CollectNum: gconv.Int(v.CollectCount),
			ShareNum:   gconv.Int(v.ShareCount),
			Sort:       v.SortOrder,
			CreateTime: createTime,
			IsAppShow:  v.IsVisible,
		}
		// 取当前语言的专题名称
		one.TopicName = topicLanguageMap[gconv.Int(v.Id)][currentLang].Name
		one.ShortName = topicLanguageMap[gconv.Int(v.Id)][currentLang].ShortTitle
		_, ok := topicLanguageMap[gconv.Int(v.Id)][consts.Zero]
		var IsZh, IsEn, IsId int
		if ok {
			IsZh = consts.One
		}
		_, ok = topicLanguageMap[gconv.Int(v.Id)][consts.One]
		if ok {
			IsEn = consts.One
		}
		_, ok = topicLanguageMap[gconv.Int(v.Id)][consts.Two]
		if ok {
			IsId = consts.One
		}
		// 支持的语言
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.One,
			LanguageTypeText: "ZH",
			IsSupport:        IsZh,
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Two,
			LanguageTypeText: "EN",
			IsSupport:        IsEn,
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Three,
			LanguageTypeText: "ID",
			IsSupport:        IsId,
		})
		out.List = append(out.List, one)
	}
	return
}

// Delete 删除
func (s *sVideoTopic) Delete(ctx context.Context, req *v1.VideoTopicDeleteReq) error {
	// 校验参数
	if 1 > len(req.Ids) {
		return gerror.New("请选择要删除的专题")
	}
	type VideoPlaylist struct {
		VideoCount uint `json:"video_count"`
	}
	// 判断专题下是否存在视频
	for _, id := range req.Ids {
		var videoPlaylist VideoPlaylist
		err := dao.VideoPlaylists.Ctx(ctx).Where(dao.VideoPlaylists.Columns().Id, id).Scan(&videoPlaylist)
		if err != nil {
			return err
		}
		if videoPlaylist.VideoCount > 0 {
			return gerror.New("该专题下有关联视频，不允许删除！")
		}
	}
	currentTime := time.Now().UnixMilli()
	err := dao.VideoPlaylists.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		_, err1 = dao.VideoPlaylists.Ctx(ctx).
			WhereIn(dao.VideoPlaylists.Columns().Id, req.Ids).
			Data(g.Map{
				dao.VideoPlaylists.Columns().DeleteTime: currentTime,
			}).
			Update()
		if err1 != nil {
			return err1
		}
		_, err1 = dao.VideoPlaylistLanguages.Ctx(ctx).
			WhereIn(dao.VideoPlaylistLanguages.Columns().PlaylistId, req.Ids).
			Data(g.Map{
				dao.VideoPlaylistLanguages.Columns().DeleteTime: currentTime,
			}).
			Update()
		if err1 != nil {
			return err1
		}
		_, err1 = dao.VideoPlaylistRelations.Ctx(ctx).
			WhereIn(dao.VideoPlaylistRelations.Columns().PlaylistId, req.Ids).
			Data(g.Map{
				dao.VideoPlaylistRelations.Columns().DeleteTime: currentTime,
			}).
			Update()
		return err1
	})
	return err
}
