package utility

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"github.com/gogf/gf/v2/crypto/gaes"
	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/encoding/gbase64"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/golang-jwt/jwt/v5"
	"github.com/mozillazg/go-pinyin"
	"gtcms/internal/consts"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"
	"gtcms/internal/service"
	"gtcms/utility"
	"gtcms/utility/iploc"
	"gtcms/utility/slices"
	"math"
	"math/rand"
	"net"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/mojocn/base64Captcha"
	"golang.org/x/crypto/bcrypt"
)

type (
	sUtility struct {
		Gid       *utility.Gid // 用于生成订单id
		pinyinMap sync.Map
	}
)

func init() {
	service.RegisterUtility(New())
}

func New() service.IUtility {
	gid, err := utility.NewGid(g.Cfg().MustGet(ctx, "server.id").Int())
	if err != nil {
		panic(err)
	}
	return &sUtility{
		Gid:       gid,
		pinyinMap: sync.Map{},
	}
}

// ---- CaptchaRedisStore
var store base64Captcha.Store = CaptchaRedisStore{}

type CaptchaRedisStore struct {
}

var ctx = context.Background()

func (r CaptchaRedisStore) Set(id, value string) error {
	err := g.Redis().SetEX(ctx, "captcha_"+id, value, consts.CaptchaEx)
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return err
}
func (r CaptchaRedisStore) Get(id string, clear bool) string {
	var value *gvar.Var
	var err error
	if clear {
		value, err = g.Redis().GetDel(ctx, "captcha_"+id)
	} else {
		value, err = g.Redis().Get(ctx, "captcha_"+id)
	}

	if err != nil {
		return ""
	}
	return value.String()
}
func (r CaptchaRedisStore) Verify(id, answer string, clear bool) bool {
	value := CaptchaRedisStore{}.Get(id, clear)
	return value == answer
}

// ----Captcha
func (s *sUtility) GenerateCaptcha() (id, b64s string, err error) {
	driver := base64Captcha.DefaultDriverDigit
	c := base64Captcha.NewCaptcha(driver, store)
	id, b64s, _, err = c.Generate()
	return
}
func (s *sUtility) VerifyCaptcha(id, answer string, clear bool) (match bool) {
	match = store.Verify(id, answer, clear)
	return
}
func (s *sUtility) VerifySmsCaptcha(areaCode string, phoneNum string, smsCaptcha string, clear bool) (match bool) {
	var value *gvar.Var
	var err error
	if clear {
		value, err = g.Redis().GetDel(ctx, "sms_"+areaCode+"_"+phoneNum)
	} else {
		value, err = g.Redis().Get(ctx, "sms_"+areaCode+"_"+phoneNum)
	}

	if err != nil {
		return false
	}
	return value.String() == smsCaptcha
}

// ---- JWT
func (s *sUtility) GenerateJWT(id uint, ty string, secretKey string) (token string, err error) {
	now := time.Now()
	claims := model.JWTD{
		Id: id,
		Ty: ty,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(consts.JWTEx)), // 过期时间
			// ExpiresAt: jwt.NewNumericDate(time.Now().Add(30 * time.Second)), //过期时间30秒
			IssuedAt:  jwt.NewNumericDate(now), // 签发时间
			NotBefore: jwt.NewNumericDate(now), // 生效时间
		},
	}
	// 使用HS256签名算法
	t := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err = t.SignedString([]byte(secretKey))
	return
}
func (s *sUtility) ParseJWT(token, secretKey string) (*model.JWTD, error) {
	t, err := jwt.ParseWithClaims(token, &model.JWTD{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretKey), nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := t.Claims.(*model.JWTD); ok && t.Valid {
		return claims, nil
	} else {
		return nil, err
	}
}

// ---- Password
func (s *sUtility) HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}
func (s *sUtility) CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// ----Client Ip
// todo 需要做 代理 ip，ip段过滤
// $remote_addr、$proxy_add_x_forwarded_for、$http_x_forwarded_for
// X-Forwarded-For: client, proxy1, proxy2
// X-Real-IP是否为指定的代理ip或ip段，是则到X-Forwarded-For里的，否则取X-Real-IP
/*
	ip := net.ParseIP("***********")
	_, ipNet, _ := net.ParseCIDR("***********/24")

	if ipNet.Contains(ip) {
		fmt.Println("IP is in subnet")
	} else {
		fmt.Println("IP is not in subnet")
	}

	start = net.ParseIP("*************")
    end = net.ParseIP("*************")
	input := net.ParseIP(ip)
	//input.To4() == nil//判断是否为IP4
	if bytes.Compare(input, start) >= 0 && bytes.Compare(input, end) <= 0 {
		fmt.Println("IP is in subnet")
    }
*/
/*
	todo：要与运维约定好ip的获取方式
*/
func (s *sUtility) GetClientIp(r *ghttp.Request) string {
	if r == nil {
		return ""
	}

	// myProxys := []string{}  //单个ip；ip段 x.x.x.x-x.x.x.x；子网掩码 x.x.x.x/x
	// cdnProxys := []string{} //单个ip；ip段 x.x.x.x-x.x.x.x；子网掩码 x.x.x.x/x

	remoteIp := r.RemoteAddr
	if ip := r.Header.Get("X-Real-IP"); ip != "" {
		remoteIp = strings.TrimSpace(ip)
	} else if ip = r.Header.Get("X-Forwarded-For"); ip != "" {
		// splitIps := strings.Split(ips, ",")
		ip = strings.TrimSpace(strings.Split(ip, ",")[0])
		remoteIp = ip
	} else {
		remoteIp, _, _ = net.SplitHostPort(strings.TrimSpace(remoteIp))
	}
	// 本地ip
	if remoteIp == "::1" {
		remoteIp = "127.0.0.1"
	}
	return remoteIp
}

// IsInIps 检查给定的 IP 是否在一组 IP 地址或 IP 范围中（如 x.x.x.x~x.x.x.x 或 x.x.x.x/x）。
// 支持使用通配符 "*" 匹配任意 IP。
func (s *sUtility) IsInIps(ips []string, ip string) bool {
	var ipss []string
	nIp := net.ParseIP(ip)

	for _, v := range ips {
		if strings.Contains(v, "~") {
			// IP 范围格式：x.x.x.x~x.x.x.x
			ipss = strings.Split(v, "~")
			start := net.ParseIP(ipss[0])
			end := net.ParseIP(ipss[1])
			if bytes.Compare(nIp, start) >= 0 && bytes.Compare(nIp, end) <= 0 {
				return true
			}
		} else if strings.Contains(v, "/") {
			// CIDR 格式：x.x.x.x/x
			_, ipNet, _ := net.ParseCIDR(v)
			if ipNet.Contains(nIp) {
				return true
			}
		} else if strings.Contains(v, "*") {
			// 通配符格式：x.x.x.x*
			// 将通配符转换为正则表达式
			pattern := strings.ReplaceAll(v, "*", ".*")
			if s.matchIP(nIp, pattern) {
				return true
			}
		} else {
			// 精确匹配
			if v == ip {
				return true
			}
		}
	}
	return false
}

// matchIP 检查 IP 地址是否匹配指定的正则表达式模式
func (s *sUtility) matchIP(ip net.IP, pattern string) bool {
	ipStr := ip.String()
	matched, _ := regexp.MatchString(pattern, ipStr)
	return matched
}

// 查找字符串是否在切片及在切片中的位置
func (s *sUtility) StringSliceFind(slice []string, val string) (int, bool) {
	for i, item := range slice {
		if item == val {
			return i, true
		}
	}
	return -1, false
}

// 查找int是否在切片及切片中的位置
func (s *sUtility) IntSliceFind(slice []int, val int) (int, bool) {
	for i, item := range slice {
		if item == val {
			return i, true
		}
	}
	return -1, false
}

// 检测服务器自身网络是否异常
func (s *sUtility) NetWorkStatus() bool {
	cmd := exec.Command("ping", "baidu.com", "-c", "1", "-W", "5")
	// fmt.Println("NetWorkStatus Start:", time.Now().UnixMilli())
	err := cmd.Run()
	// fmt.Println("NetWorkStatus End :", time.Now().UnixMilli())
	if err != nil {
		// fmt.Println(err.Error())
		return false
	} else {
		// fmt.Println("Net Status , OK")
	}
	return true
}

// ctx中获取self
func (s *sUtility) GetSelf(ctx context.Context) (selfCtx *entity.Account, err error) {
	selfCtxVar := ctx.Value(consts.KeyHttpContext)
	selfCtx, ok := selfCtxVar.(*entity.Account)
	if !ok {
		return nil, gerror.New(g.I18n().T(ctx, `ctx.getAdmin.error`))
	}
	return
}

func (s *sUtility) GetAccountRole(ctx context.Context) (roleInfo *model.AccountRole, err error) {
	ctxValue := ctx.Value(consts.KeyContextRole)
	roleInfo, ok := ctxValue.(*model.AccountRole)
	if !ok {
		return nil, gerror.New(g.I18n().T(ctx, `ctx.getAccountRole.error`))
	}
	return
}

func (s *sUtility) GetRolePermission(ctx context.Context) (ret *model.RolePermissionInfo, err error) {
	ctxVar := ctx.Value(consts.ContextKeyRolePerm)
	var ok bool
	ret, ok = ctxVar.(*model.RolePermissionInfo)
	if !ok {
		return nil, gerror.New(g.I18n().T(ctx, "ctx.GetRolePermission.error"))
	}
	return
}

// ctx中获取self
func (s *sUtility) GetApiNode(ctx context.Context) (nodes []string, apiUrl string, err error) {
	urlVar := ctx.Value(consts.ContextKeyUrl)
	path, ok := urlVar.(string)
	if !ok {
		return nil, "", gerror.New(g.I18n().T(ctx, `ctx.getApiUrl.error`))
	}

	apiUrl = path

	// 移除字符串开头的斜杠
	trimmedPath := strings.TrimPrefix(path, "/")
	// 使用 SplitN 函数以斜杠为分隔符分割字符串，最多分割成 3 部分
	nodes = strings.SplitN(trimmedPath, "/", 3)
	return
}

// GetSelfAccount 当前账号
func (s *sUtility) GetSelfAccount(ctx context.Context) (account string) {
	selfCtx, err := s.GetSelf(ctx)
	if err != nil {
		glog.Error(ctx, err)
	}
	account = selfCtx.Account
	return
}

// GetCurrentLanguageCode 获取当前会话的语言编码
func (s *sUtility) GetCurrentLanguageCode(ctx context.Context) (languageCode string) {
	languageCode = gi18n.LanguageFromCtx(ctx)
	if len(languageCode) < 1 {
		languageCode = consts.LangDefaultCode
	}
	return
}

// 比较两个浮点数是否相等（在配置的误差精度内）
func (s *sUtility) IsEqualFloat64(a float64, b float64) (isEqual bool) {
	dif := math.Abs(a - b)
	if dif < consts.Precision {
		isEqual = true
	}
	return
}

// todo:生成订单号
func (s *sUtility) GenNbbId() (id string, err error) {
	id = "123"
	return
}

func (s *sUtility) GetIPRegion(ctx context.Context, ip string) (region string) {
	_, region = iploc.IPLoc(ip)
	return
}

// 获取host
func (s *sUtility) GetHost(ctx context.Context) (host string) {
	return g.RequestFromCtx(ctx).Host
}

// IsFieldValueExist 检查一个表中是否有指定字段的值；前置条件：表名和字段名必须正确
func (s *sUtility) IsFieldValueExist(ctx context.Context, in model.IsFieldValueExistInput) (bool, error) {
	m := g.Model(in.TableName).Ctx(ctx)
	n := 1
	if in.Value.IsSlice() {
		vs := in.Value.Slice()
		m = m.WhereIn(in.FieldName, vs)
		n = len(vs)
	} else {
		m = m.Where(in.FieldName, in.Value)
	}
	if len(in.DeleteTime) > 0 {
		m = m.Where(in.DeleteTime, 0)
	}
	count, err := m.Limit(1).Count()
	if err != nil {
		return false, err
	}
	return count >= n, nil
}

// 获取两个时间秒差
func (s *sUtility) getTimeSubSec(startTime, endTime string) float64 {
	eTime, _ := gtime.StrToTime(endTime)
	sTime, _ := gtime.StrToTime(startTime)
	return eTime.Sub(sTime).Seconds()
}

// CheckTimeSecDiff 判断两个时间差是否在有效时间秒内
func (s *sUtility) CheckTimeSecDiff(startTime, endTime string, effSec float64) bool {
	if s.getTimeSubSec(startTime, endTime) > effSec {
		return false
	}
	return true
}

// GenerateSupplierUniqueNumber 生成唯一的编号
func (s *sUtility) GenerateSupplierUniqueNumber(ctx context.Context, in model.GenerateSupplierUniqueNumberInput) (res string) {

OutLoop:
	for i := 0; i < 10; i++ {
		nn := 5
		selectNumbers := make([]string, 0, nn)
		for j := 0; j < nn; j++ {
			selectNumbers = append(selectNumbers, in.GenerateNumberFunc(ctx))
		}
		m := g.Model(in.TableName).Ctx(ctx).Fields(in.FieldName).WhereIn(in.FieldName, selectNumbers)
		if len(in.DeleteTime) > 0 {
			m = m.Where(in.DeleteTime, 0)
		}
		values, err := m.Limit(nn).Array()
		if err != nil {
			glog.Error(ctx, err)
			continue
		}
		valStr := make([]string, 0, len(values))
		for _, value := range values {
			valStr = append(valStr, value.String())
		}
		for _, number := range selectNumbers {
			if !slices.Contains(valStr, number) {
				res = number
				break OutLoop
			}
		}

	}
	return
}

// Md5Encode 使用MD5算法对传入的字符串进行加密，并返回加密后的字符串
func (s *sUtility) Md5Encode(str string) string {
	encryptString, _ := gmd5.EncryptString(str)
	return encryptString
}

// Base64Encode 使用Base64算法对传入的字符串进行加密，并返回加密后的字符串
func (s *sUtility) Base64Encode(str string) string {
	return gbase64.EncodeToString([]byte(str))
}

// ComputeHmac256 计算Hmac256
func (s *sUtility) ComputeHmac256(message string, secret string) string {
	key := []byte(secret)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(message))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func (s *sUtility) GetIPCity(ctx context.Context, ip string) (addr string) {
	province, city := iploc.IPLoc(ip)
	if g.IsEmpty(city) {
		return province
	}
	if g.IsEmpty(province) {
		return city
	}
	return fmt.Sprintf("%s-%s", province, city)
}

// 函数名为 IsAlphaNumeric，接收一个字符串参数 str，返回一个布尔值
func (s *sUtility) IsAlphaNumeric(str string) bool {
	// 定义正则表达式模式，只匹配英文字母
	pattern := "^[a-zA-Z0-9]+$"
	regex := regexp.MustCompile(pattern)

	// 使用正则表达式进行匹配
	return regex.MatchString(str)
}

// GetPinyin 获取中文字符串的拼音
func (s *sUtility) GetPinyin(text string) (py string) {
	if val, ok := s.pinyinMap.Load(text); ok {
		return val.(string)
	}

	// 先校验text，如果是英文，直接返回
	if s.IsAlphaNumeric(text) {
		return text
	}

	a := pinyin.NewArgs()
	a.Fallback = func(r rune, a pinyin.Args) []string {
		return []string{string(r)}
	}
	for _, r := range pinyin.Pinyin(text, a) {
		py += r[0]
	}
	s.pinyinMap.Store(text, py)
	return
}

func (s *sUtility) GetPinyinFirstLetter(text string) (py string) {
	a := pinyin.NewArgs()
	a.Style = pinyin.FirstLetter
	a.Fallback = func(r rune, a pinyin.Args) []string {
		return []string{string(r)}
	}
	for _, r := range pinyin.Pinyin(text, a) {
		py += r[0]
	}
	return
}

func (s *sUtility) ExtractAndGenerate(content string, typ int) (string, string) {
	// 定义正则表达式来匹配 {letter-3} 和 {number-2} 之类的模式
	re := regexp.MustCompile(`\{(letter)-(\d+)\}`)
	if typ == 2 {
		re = regexp.MustCompile(`\{(number)-(\d+)\}`)
	}
	var generatedStrings []string

	// 替换匹配的部分
	result := re.ReplaceAllStringFunc(content, func(match string) string {
		// 提取类型和数量
		submatches := re.FindStringSubmatch(match)
		if len(submatches) == 3 {
			count, err := strconv.Atoi(submatches[2])
			if err != nil {
				return match
			}
			var generatedString string
			if submatches[1] == "letter" {
				generatedString = s.GenerateRandomLetters(count)
			} else if submatches[1] == "number" {
				generatedString = s.GenerateRandomNumbers(count)
			}
			generatedStrings = append(generatedStrings, generatedString)
			return generatedString
		}
		return match
	})

	return result, strings.Join(generatedStrings, ", ")
}

func (s *sUtility) GenerateRandomLetters(n int) string {
	letters := "abcdefghijklmnopqrstuvwxyz"
	result := make([]byte, n)
	for i := range result {
		result[i] = letters[rand.Intn(len(letters))]
	}
	return string(result)
}

// GenerateRandomNumbers 生成指定数量的随机数字
func (s *sUtility) GenerateRandomNumbers(n int) string {
	numbers := "0123456789"
	result := make([]byte, n)
	for i := range result {
		result[i] = numbers[rand.Intn(len(numbers))]
	}
	return string(result)
}

// 获取今天0点到24点的毫秒时间戳
func (s *sUtility) GetTodayTimestamps() (startOfDay, endOfDay int64) {
	// 获取当前时间
	now := time.Now()

	// 创建今天0点时间
	start := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 创建明天0点时间
	end := start.AddDate(0, 0, 1)

	// 转换为毫秒时间戳
	startOfDay = start.UnixNano() / int64(time.Millisecond)
	endOfDay = end.UnixNano() / int64(time.Millisecond)

	return startOfDay, endOfDay
}

// 获取昨天的0点到24点的毫秒时间戳
func (s *sUtility) GetYesterdayTimestamps() (startOfYesterday, endOfYesterday int64) {
	// 获取当前时间
	now := time.Now()

	// 创建今天0点时间
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 创建昨天0点时间
	yesterdayStart := todayStart.AddDate(0, 0, -1)

	// 创建昨天24点时间（也就是今天0点时间）
	yesterdayEnd := todayStart

	// 转换为毫秒时间戳
	startOfYesterday = yesterdayStart.UnixNano() / int64(time.Millisecond)
	endOfYesterday = yesterdayEnd.UnixNano() / int64(time.Millisecond)

	return startOfYesterday, endOfYesterday
}

// GetMonthTimestamps 获取本月的开始和结束时间的毫秒时间戳
func (s *sUtility) GetMonthTimestamps() (startOfMonthMillis, endOfMonthMillis int64) {
	// 获取当前时间
	now := time.Now()

	// 获取本月的第一天
	firstOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// 获取下个月的第一天
	firstOfNextMonth := firstOfMonth.AddDate(0, 1, 0)

	// 获取本月的最后一天
	lastOfMonth := firstOfNextMonth.Add(-time.Nanosecond)

	// 转换为毫秒时间戳
	startOfMonthMillis = firstOfMonth.UnixNano() / int64(time.Millisecond)
	endOfMonthMillis = lastOfMonth.UnixNano() / int64(time.Millisecond)

	return startOfMonthMillis, endOfMonthMillis
}

func (s *sUtility) RemoveTrailingLeadingSlash(str string) string {
	// 如果字符串首字符是 '/'，则去掉它
	if len(str) > 0 && str[0] == '/' {
		str = str[1:]
	}

	// 如果字符串最后一个字符是 '/'，则去掉它
	if len(str) > 0 && str[len(str)-1] == '/' {
		str = str[:len(str)-1]
	}
	return str
}

// RemoveSpacesAndSymbols 函数用于去除字符串中的空格和符号
func (s *sUtility) RemoveSpacesAndSymbols(input string) string {
	// 使用 strings.Builder 更高效地构建字符串
	var builder strings.Builder

	// 遍历输入字符串中的每一个字符
	for _, r := range input {
		// 如果字符不是空白字符且不是圆括号，则将其写入 builder
		if r != ' ' && r != '(' && r != ')' {
			builder.WriteRune(r)
		}
	}

	// 返回构建的字符串
	return builder.String()
}

// 解析自定义的内容
func (s *sUtility) ParseCustomContent(content string, params ...string) (c string) {
	for idx, word := range consts.DynamicWords {
		if len(params) <= idx {
			continue
		}
		content = strings.ReplaceAll(content, word, params[idx])
	}
	c = content
	return
}

func (s *sUtility) FormatPath(str string) string {
	str = s.CleanURL(str)
	str = s.RemoveTrailingLeadingSlash(str)

	// 如果字符串最后一个字符不是 '/'，则添加它
	if len(str) > 0 && str[len(str)-1] != '/' {
		str = str + "/"
	}

	return str
}

// CleanURL 去除 URL 中连续的多个斜杠，保留一个
func (s *sUtility) CleanURL(url string) string {
	// 定义正则表达式来匹配连续的多个斜杠
	re := regexp.MustCompile(`/+`)
	// 将连续的多个斜杠替换为单个斜杠
	cleanedURL := re.ReplaceAllString(url, "/")
	// 确保协议头部后的 :// 不被影响
	return strings.Replace(cleanedURL, ":/", "://", 1)
}

// AddWWWPrefix 如果url没有www前缀，则加上www.
func (s *sUtility) AddWWWPrefix(url string) string {
	if !strings.HasPrefix(url, "www.") {
		url = "www." + url
	}
	return url
}

// IsToday 是否今天
func (s *sUtility) IsToday(timestamp int64) bool {
	// 将时间戳从毫秒转换为纳秒
	t := time.Unix(0, timestamp*int64(time.Millisecond))

	// 获取当前时间
	now := time.Now()

	// 检查年份、月份和日期是否相同
	return t.Year() == now.Year() && t.Month() == now.Month() && t.Day() == now.Day()
}

// MinInt 返回两个整数中的较小值
func (s *sUtility) MinInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func (s *sUtility) CutString(content string, num int) (extractedChars string) {
	// 使用正则表达式去除HTML标签和空格
	htmlTagRegex := regexp.MustCompile(`(<[^>]+>)|&nbsp;|[\s　]+`)
	processedString := htmlTagRegex.ReplaceAllString(content, "")

	count := 0
	// 提取的字符
	extractedChars = ""
	// 遍历字符串
	for _, runeValue := range processedString {
		// 累加字符计数器
		count++
		// 添加到提取的字符串中
		extractedChars += string(runeValue)

		// 如果提取了50个字符，则跳出循环
		if count >= num {
			break
		}
	}
	return
}

func (s *sUtility) RemoveEmojis(input string) string {
	// 定义一个正则表达式模式，匹配常见的表情符号
	emojiRegex := regexp.MustCompile("[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]+")
	return emojiRegex.ReplaceAllString(input, "")
}

func (s *sUtility) EncryptPassword(secret, cryptoKey string) (string, error) {
	aesBytes, err := gaes.Encrypt([]byte(secret), []byte(cryptoKey))
	if err != nil {
		return "", err
	}

	return hex.EncodeToString(aesBytes), nil
}

func (s *sUtility) DecryptPassword(aesPwd, cryptoKey string) (string, error) {
	aesBytes, err := hex.DecodeString(aesPwd)
	if err != nil {
		return "", err
	}

	pwdBytes, err := gaes.Decrypt(aesBytes, []byte(cryptoKey))
	if err != nil {
		return "", err
	}
	return string(pwdBytes), nil
}
