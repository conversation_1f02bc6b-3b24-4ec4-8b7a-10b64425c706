package spider

//
//import (
//	"context"
//	"github.com/gogf/gf/v2/database/gdb"
//	"github.com/gogf/gf/v2/errors/gerror"
//	"github.com/gogf/gf/v2/frame/g"
//	"github.com/gogf/gf/v2/os/gctx"
//	"github.com/gogf/gf/v2/os/gtime"
//	"github.com/gogf/gf/v2/os/gtimer"
//	"github.com/gogf/gf/v2/util/gconv"
//	v1 "gtcms/api/v1"
//	"gtcms/internal/consts"
//	"gtcms/internal/dao"
//	"gtcms/internal/model"
//	"gtcms/internal/model/entity"
//	"gtcms/internal/service"
//	"gtcms/utility"
//	"os/exec"
//	"strings"
//	"time"
//)
//
//var ctx = gctx.New()
//
//type sSpider struct {
//	interval time.Duration
//}
//
//func init() {
//	service.RegisterSpider(New())
//}
//
//func New() *sSpider {
//	s := &sSpider{}
//	cfg := g.Cfg()
//	s.interval = cfg.MustGet(ctx, "spider.interval").Duration()
//	if s.interval == 0 {
//		s.interval = 60
//	}
//	//s.timer()
//	return s
//}
//
//func (s *sSpider) timer() {
//	gtimer.DelayAdd(ctx, s.interval*time.Second, s.interval*time.Second, func(ctx context.Context) {
//		s.run()
//	})
//}
//
//// 清除一个月前的蜘蛛日志
//func (s *sSpider) run() {
//	// 一个月前的时间
//	oneMonthAgo := gtime.Now().AddDate(0, -1, 0)
//	array, err := dao.SpiderLog.Ctx(ctx).Fields(dao.SpiderLog.Columns().Id).WhereLT(dao.SpiderLog.Columns().AccessTime, oneMonthAgo.UnixMilli()).
//		OrderAsc(dao.SpiderLog.Columns().Id).Limit(10000).Array()
//	if err != nil {
//		return
//	}
//	ids := make([]int, 0, len(array))
//	for _, v := range array {
//		ids = append(ids, gconv.Int(v))
//	}
//	if len(ids) < 1 {
//		return
//	}
//	_, _ = dao.SpiderLog.Ctx(ctx).WhereIn(dao.SpiderLog.Columns().Id, ids).Delete()
//}
//
//func (s *sSpider) List(ctx context.Context, req *v1.SpiderListReq) (res *v1.SpiderListRes, err error) {
//	res = new(v1.SpiderListRes)
//	res.Current = req.Current
//	res.List = make([]v1.SpiderItem, 0)
//
//	m := dao.Spider.Ctx(ctx)
//	if req.Status != nil {
//		m = m.Where(dao.Spider.Columns().Status, req.Status)
//	}
//	if req.Name != nil {
//		m = m.WhereLike(dao.Spider.Columns().Name, utility.WhereLike(*req.Name))
//	}
//
//	m = m.Where(dao.Spider.Columns().DeleteTime, 0)
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.Spider.Table() + "." + dao.Spider.Columns().Sort)
//	}
//
//	// 查询列表
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.SpiderItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.SpiderItem, 0, total)
//	}
//
//	err = m.Scan(&res.List)
//	if err != nil {
//		return
//	}
//	res.Total = total
//	return
//}
//
//func (s *sSpider) Add(ctx context.Context, req *v1.SpiderAddReq) (res *v1.SpiderAddRes, err error) {
//	res = new(v1.SpiderAddRes)
//
//	var entityData *entity.Spider
//	if err = gconv.Scan(req, &entityData); err != nil {
//		return
//	}
//	entityData.CreateTime = time.Now().UnixMilli()
//	self, err := service.Utility().GetSelf(ctx)
//	if err != nil {
//		return
//	}
//	entityData.Creater = self.Id
//
//	id, err := dao.Spider.Ctx(ctx).Cache(gdb.CacheOption{
//		Duration: -1,
//		Name:     consts.SpiderList,
//		Force:    false, // nil也缓存
//	}).InsertAndGetId(entityData)
//	if err != nil {
//		return
//	}
//
//	res = &v1.SpiderAddRes{
//		Id: uint(id),
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: entityData,
//	})
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sSpider) Edit(ctx context.Context, req *v1.SpiderEditReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//
//	if req.Status != nil {
//		update[dao.Spider.Columns().Status] = req.Status
//	}
//	if req.Name != nil {
//		update[dao.Spider.Columns().Name] = req.Name
//	}
//	if req.Logo != nil {
//		update[dao.Spider.Columns().Logo] = req.Logo
//	}
//	if req.IdentificationCriteria != nil {
//		update[dao.Spider.Columns().IdentificationCriteria] = req.IdentificationCriteria
//	}
//	if req.BlockIp != nil {
//		update[dao.Spider.Columns().BlockIp] = req.BlockIp
//	}
//	if req.Sort != nil {
//		update[dao.Spider.Columns().Sort] = req.Sort
//	}
//
//	// 取旧数据
//	oldData, err := s.One(ctx, &v1.SpiderOneReq{
//		Id: req.Id,
//	})
//
//	update[dao.Spider.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	if _, err = dao.Spider.Ctx(ctx).Cache(gdb.CacheOption{
//		Duration: -1,
//		Name:     consts.SpiderList,
//		Force:    false, // nil也缓存
//	}).Where(dao.Spider.Columns().Id, req.Id).Update(update); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		OldRecordOrAttr: oldData,
//		NewRecordOrAttr: req,
//	})
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sSpider) Delete(ctx context.Context, req *v1.SpiderDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	update[dao.Spider.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	update[dao.Spider.Columns().DeleteTime] = gtime.Now().UnixMilli()
//
//	var ids []uint
//	if req.Id != 0 {
//		ids = append(ids, req.Id)
//	}
//	if len(req.Ids) > 0 {
//		ids = append(ids, req.Ids...)
//	}
//	if len(ids) < 1 {
//		err = gerror.New("id is empty")
//		return
//	}
//
//	// 取旧数据
//	oldData, err := s.One(ctx, &v1.SpiderOneReq{
//		Id: req.Id,
//	})
//
//	if _, err = dao.Spider.Ctx(ctx).Cache(gdb.CacheOption{
//		Duration: -1,
//		Name:     consts.SpiderList,
//		Force:    false, // nil也缓存
//	}).Where(dao.Spider.Columns().Id, ids).Data(update).Update(); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: oldData,
//	})
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sSpider) One(ctx context.Context, req *v1.SpiderOneReq) (res *v1.SpiderOneRes, err error) {
//	res = new(v1.SpiderOneRes)
//
//	sg, err := dao.Spider.Ctx(ctx).Where(dao.Spider.Columns().Id, req.Id).One()
//	if err != nil {
//		return
//	}
//
//	var item v1.SpiderItem
//	err = gconv.Scan(sg, &item)
//	if err != nil {
//		return
//	}
//	res = &v1.SpiderOneRes{
//		SpiderItem: item,
//	}
//	return
//}
//
//func (s *sSpider) FirewallList(ctx context.Context, req *v1.SpiderFirewallListReq) (res *v1.SpiderFirewallListRes, err error) {
//	res = new(v1.SpiderFirewallListRes)
//	res.Current = req.Current
//	res.List = make([]v1.SpiderFirewallItem, 0)
//
//	m := dao.SpiderFirewall.Ctx(ctx).LeftJoin(dao.SiteGroup.Table(),
//		dao.SpiderFirewall.Table()+"."+dao.SpiderFirewall.Columns().BelongGroupId+" = "+dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Id)
//	if req.Status != nil {
//		m = m.Where(dao.SpiderFirewall.Columns().Status, req.Status)
//	}
//	if req.BelongGroupId != nil {
//		m = m.Where(dao.SpiderFirewall.Columns().BelongGroupId, req.BelongGroupId)
//	}
//
//	m = m.Where(dao.SpiderFirewall.Columns().DeleteTime, 0)
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.SpiderFirewall.Table() + "." + dao.SpiderFirewall.Columns().Id) // 最新在前面
//	}
//
//	// 查询列表
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.SpiderFirewallItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.SpiderFirewallItem, 0, total)
//	}
//
//	err = m.Fields(dao.SpiderFirewall.Table()+".*",
//		dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Name+" as belongGroupName").Scan(&res.List)
//	if err != nil {
//		return
//	}
//	res.Total = total
//	return
//}
//
//func (s *sSpider) FirewallAdd(ctx context.Context, req *v1.SpiderFirewallAddReq) (res *v1.SpiderFirewallAddRes, err error) {
//	res = new(v1.SpiderFirewallAddRes)
//
//	var entityData *entity.SpiderFirewall
//	if err = gconv.Scan(req, &entityData); err != nil {
//		return
//	}
//	entityData.CreateTime = time.Now().UnixMilli()
//
//	id, err := dao.SpiderFirewall.Ctx(ctx).Cache(gdb.CacheOption{
//		Duration: -1,
//		Name:     consts.SpiderFirewallList,
//		Force:    false, // nil也缓存
//	}).InsertAndGetId(entityData)
//	if err != nil {
//		return
//	}
//
//	res = &v1.SpiderFirewallAddRes{
//		Id: uint(id),
//	}
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sSpider) deleteCache(ctx context.Context) {
//	g.Go(ctx, func(ctx context.Context) {
//		// 延迟一秒钟
//		<-time.After(1 * time.Second)
//
//		service.DbCache().PublishUpdate(ctx, model.MemoryUpdateMessage{
//			ClassId: consts.SpiderFirewallUpdate,
//			Key:     "",
//		})
//	}, nil)
//}
//
//func (s *sSpider) FirewallEdit(ctx context.Context, req *v1.SpiderFirewallEditReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//
//	if req.Status != nil {
//		update[dao.SpiderFirewall.Columns().Status] = req.Status
//	}
//	if req.BelongGroupId != nil {
//		update[dao.SpiderFirewall.Columns().BelongGroupId] = req.BelongGroupId
//	}
//	if req.Config != nil {
//		update[dao.SpiderFirewall.Columns().Config] = req.Config
//	}
//
//	update[dao.SpiderFirewall.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	if _, err = dao.SpiderFirewall.Ctx(ctx).Cache(gdb.CacheOption{
//		Duration: -1,
//		Name:     consts.SpiderFirewallList,
//		Force:    false, // nil也缓存
//	}).Where(dao.SpiderFirewall.Columns().Id, req.Id).Update(update); err != nil {
//		return
//	}
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sSpider) FirewallDelete(ctx context.Context, req *v1.SpiderFirewallDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	update[dao.SpiderFirewall.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	update[dao.SpiderFirewall.Columns().DeleteTime] = gtime.Now().UnixMilli()
//
//	if _, err = dao.SpiderFirewall.Ctx(ctx).Cache(gdb.CacheOption{
//		Duration: -1,
//		Name:     consts.SpiderFirewallList,
//		Force:    false, // nil也缓存
//	}).Where(dao.SpiderFirewall.Columns().Id, req.Id).Data(update).Update(); err != nil {
//		return
//	}
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sSpider) LogList(ctx context.Context, req *v1.SpiderLogListReq) (res *v1.SpiderLogListRes, err error) {
//	res = new(v1.SpiderLogListRes)
//	res.Current = req.Current
//	res.List = make([]v1.SpiderLogItem, 0)
//
//	m := dao.SpiderLog.Ctx(ctx)
//	if req.StartTime != nil {
//		m = m.WhereGTE(dao.SpiderLog.Columns().AccessTime, req.StartTime)
//	} else {
//		// 默认昨天
//		m = m.WhereGTE(dao.SpiderLog.Columns().AccessTime, gtime.Now().AddDate(0, 0, -1).UnixMilli())
//	}
//	if req.EndTime != nil {
//		m = m.WhereLTE(dao.SpiderLog.Columns().AccessTime, req.EndTime)
//	} else {
//		// 默认今天
//		m = m.WhereLTE(dao.SpiderLog.Columns().AccessTime, gtime.Now().UnixMilli())
//	}
//	if req.SpiderName != nil {
//		m = m.Where(dao.SpiderLog.Columns().Name, *req.SpiderName)
//	}
//	if req.DomainName != nil {
//		m = m.WhereLike(dao.SpiderLog.Columns().AccessUrl, utility.WhereLike(*req.DomainName))
//	}
//	if req.Ip != nil {
//		m = m.Where(dao.SpiderLog.Columns().Ip, req.Ip)
//	}
//	if req.StatusCode != nil {
//		m = m.Where(dao.SpiderLog.Columns().StatusCode, req.StatusCode)
//	}
//
//	self, err := service.Utility().GetSelf(ctx)
//	if err != nil {
//		return
//	}
//	ac, err := service.RoleV2().Detail(ctx, self.RoleId)
//	if err != nil {
//		return
//	}
//	if consts.RoleLevelAdmin != ac.RoleLevel {
//		// 取出当前用户所拥有的域名
//		domainList, _ := service.Domain().Options3(ctx)
//		var domainIds []uint
//		for _, v := range domainList {
//			domainIds = append(domainIds, gconv.Uint(v.Value))
//		}
//		m = m.Where(dao.SpiderLog.Columns().DomainId, domainIds)
//	}
//
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.SpiderLog.Table() + "." + dao.SpiderLog.Columns().AccessTime) // 最新在前面
//	}
//
//	// 查询列表
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.SpiderLogItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.SpiderLogItem, 0, total)
//	}
//
//	err = m.Scan(&res.List)
//	if err != nil {
//		return
//	}
//	res.Total = total
//	return
//}
//
//func (s *sSpider) LogPullBlack(ctx context.Context, req *v1.SpiderLogPullBlackReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	var list []*entity.SpiderLog
//	err = dao.SpiderLog.Ctx(ctx).Where(dao.SpiderLog.Columns().Id, req.Ids).Scan(&list)
//	if err != nil {
//		return
//	}
//	var ips []entity.SpiderLogBlackIp
//	for _, item := range list {
//		ips = append(ips, entity.SpiderLogBlackIp{
//			Ip: item.Ip,
//		})
//	}
//	_, err = dao.SpiderLogBlackIp.Ctx(ctx).Replace(ips)
//	if err != nil {
//		return
//	}
//
//	s.deleteCache(ctx)
//	return
//}
//
//// 蜘蛛访问统计
//func (s *sSpider) SpiderAccessStatisticsToday(ctx context.Context, req model.HomePageInfo) (res []*v1.SpiderEngine, err error) {
//	self, err := service.Utility().GetSelf(ctx)
//	if err != nil {
//		return
//	}
//
//	return
//	var all gdb.Result
//	all, err = dao.Domain.Ctx(ctx).Fields(dao.Domain.Columns().Id).
//		Where(dao.Domain.Columns().DeleteTime, 0).
//		Where(dao.Domain.Columns().Used, 1).
//		Where(dao.Domain.Columns().BelongUserId, self.Id).
//		All()
//	if err != nil {
//		return
//	}
//
//	ac, err := service.RoleV2().Detail(ctx, self.RoleId)
//	if err != nil {
//		return
//	}
//
//	var domainIds []uint
//	for _, v := range all {
//		domainIds = append(domainIds, gconv.Uint(v["id"]))
//	}
//
//	m := dao.SpiderLog.Ctx(ctx).Fields(dao.SpiderLog.Columns().SpiderId+" as engineId ",
//		dao.SpiderLog.Columns().Name+" as title ", "count(*) as numVisit").
//		WhereGT(dao.SpiderLog.Columns().AccessTime, req.StartOfDay).
//		WhereLT(dao.SpiderLog.Columns().AccessTime, req.EndOfDay).
//		Group(dao.SpiderLog.Columns().Name)
//
//	if consts.RoleLevelAdmin != ac.RoleLevel {
//		m = m.WhereIn(dao.SpiderLog.Columns().DomainId, domainIds)
//	}
//	err = m.Scan(&res)
//	if err != nil {
//		return
//	}
//	return
//}
//
//// 蜘蛛访问统计
//func (s *sSpider) SpiderAccessStatisticsYesterday(ctx context.Context) (res []*v1.SpiderEngine, err error) {
//	startOfDay, endOfDay := service.Utility().GetYesterdayTimestamps()
//	err = dao.SpiderLog.Ctx(ctx).Fields(dao.SpiderLog.Columns().SpiderId+" as engineId ",
//		dao.SpiderLog.Columns().Name+" as title ", "count(*) as numVisit").WhereGT(dao.SpiderLog.Columns().SpiderId, 0).
//		WhereGT(dao.SpiderLog.Columns().AccessTime, startOfDay).WhereLT(dao.SpiderLog.Columns().AccessTime, endOfDay).
//		Group(dao.SpiderLog.Columns().Name).OrderAsc(dao.SpiderLog.Columns().SpiderId).Scan(&res)
//	if err != nil {
//		return
//	}
//	return
//}
//
//func (s *sSpider) LogBlackList(ctx context.Context, req *v1.SpiderLogBlackListReq) (res *v1.SpiderLogBlackListRes, err error) {
//	res = new(v1.SpiderLogBlackListRes)
//
//	err = dao.SpiderLogBlackIp.Ctx(ctx).Scan(&res.List)
//	if err != nil {
//		return
//	}
//	return
//}
//
//func (s *sSpider) LogRemoveBlack(ctx context.Context, req *v1.SpiderLogRemoveBlackReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//	if len(req.Ids) > 0 {
//		_, err = dao.SpiderLogBlackIp.Ctx(ctx).WhereIn(dao.SpiderLogBlackIp.Columns().Id, req.Ids).Delete()
//		if err != nil {
//			return
//		}
//	}
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sSpider) SpiderLogCheck(ctx context.Context, req *v1.SpiderLogCheckReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	one, err := dao.SpiderLog.Ctx(ctx).Fields(
//		dao.SpiderLog.Table()+"."+dao.SpiderLog.Columns().Ip,
//		dao.Spider.Table()+"."+dao.Spider.Columns().Logo,
//	).
//		LeftJoin(dao.Spider.Table(), dao.Spider.Table()+"."+dao.Spider.Columns().Id+"="+dao.SpiderLog.Table()+"."+dao.SpiderLog.Columns().SpiderId).
//		Where(dao.SpiderLog.Table()+"."+dao.SpiderLog.Columns().Id, req.Id).One()
//	if err != nil {
//		return
//	}
//
//	g.Go(ctx, func(ctx context.Context) {
//		// 定义要执行的命令和参数
//		cmd := exec.Command("nslookup", gconv.String(one["ip"]))
//
//		// 获取命令的输出
//		output, err := cmd.CombinedOutput()
//		if err != nil {
//			return
//		}
//		str := gconv.String(output)
//		logo := strings.ToLower(gconv.String(one["logo"]))
//		logos := strings.Split(logo, "|")
//		for _, v := range logos {
//			if !strings.Contains(str, v) {
//				// 不存在
//				_, _ = s.LogPullBlack(ctx, &v1.SpiderLogPullBlackReq{
//					Ids: []uint{req.Id},
//				})
//				_, _ = service.DbCache().DeleteDbCache(ctx, consts.SpiderBlackIpList)
//			}
//		}
//	}, nil)
//	return
//}
