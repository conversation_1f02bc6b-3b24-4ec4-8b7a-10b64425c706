package attachment

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"gtcms/internal/errno"

	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	do "gtcms/internal/model/do/admin"

	"time"
)

// CategoryOne 获取指定条件的分类
func (s sAttachment) CategoryOne(ctx context.Context, in model.CategoryOneInput) (data *model.AttachmentCategory, err error) {
	m := dao.AttachmentCategory.Ctx(ctx)
	if in.PId > 0 {
		m = m.Where(dao.AttachmentCategory.Columns().Pid, in.PId)
	}
	if in.Id > 0 {
		m = m.Where(dao.AttachmentCategory.Columns().Id, in.Id)
	}
	m = m.Where(dao.AttachmentCategory.Columns().DeleteTime, 0)
	err = m.Scan(&data)
	if data == nil {
		err = errno.CodeNotFound
	}
	return
}

// CategoryCount 获取指定条件的分类数量
func (s sAttachment) CategoryCount(ctx context.Context, in model.AttachmentCategoryCountInput) (count int, err error) {
	m := dao.AttachmentCategory.Ctx(ctx)
	if len(in.PIds) > 0 {
		m = m.WhereIn(dao.AttachmentCategory.Columns().Pid, in.PIds)
	}
	m = m.Where(dao.AttachmentCategory.Columns().DeleteTime, 0)
	count, err = m.Count()
	return
}

// CategoryList 附件分类列表
func (s sAttachment) CategoryList(ctx context.Context, in model.AttachmentCategoryListInput) (out *model.AttachmentCategoryListOutput, err error) {
	out = new(model.AttachmentCategoryListOutput)
	m := dao.AttachmentCategory.Ctx(ctx)
	if in.Id > 0 {
		m = m.Where(dao.AttachmentCategory.Columns().Id, in.Id)
	}
	if len(in.Name) > 0 {
		m = m.WhereLike(dao.AttachmentCategory.Columns().Name, in.Name+"%")
	}
	m = m.Where(dao.AttachmentCategory.Columns().DeleteTime, 0)
	out.Total, err = m.Count()
	if err != nil {
		return out, err
	}
	if out.Total < 1 {
		return out, nil
	}
	m = m.OrderDesc(dao.AttachmentCategory.Columns().OrderBy).OrderDesc(dao.AttachmentCategory.Columns().Id)
	listSize := out.Total
	if in.Current > 0 && in.PageSize > 0 {
		m = m.Page(in.Current, in.PageSize)
		listSize = in.PageSize
	}
	out.List = make([]model.AttachmentCategory, 0, listSize)
	if err = m.Scan(&out.List); err != nil {
		return out, err
	}
	return out, nil
}

// CategoryAdd 附件分类新增
func (s sAttachment) CategoryAdd(ctx context.Context, in model.AttachmentCategoryAddInput) (id uint, err error) {
	doData := do.AttachmentCategory{
		Pid:           in.Pid,
		Type:          in.Type,
		Name:          in.Name,
		OrderBy:       in.OrderBy,
		CreateTime:    gtime.Now().UnixMilli(),
		CreateAccount: in.CreateAccount,
		UpdateTime:    gtime.Now().UnixMilli(),
		UpdateAccount: in.CreateAccount,
		DeleteTime:    0,
	}
	insertId, err := dao.AttachmentCategory.Ctx(ctx).Data(&doData).InsertAndGetId()
	if err != nil {
		return
	}
	id = uint(insertId)
	return
}

// CategoryEdit 附件分类编辑
func (s sAttachment) CategoryEdit(ctx context.Context, in model.AttachmentCategoryEditInput) (err error) {
	update := g.Map{}
	if len(in.Name) > 0 {
		update[dao.AttachmentCategory.Columns().Name] = in.Name
	}
	if in.OrderBy != nil {
		update[dao.AttachmentCategory.Columns().OrderBy] = in.OrderBy
	}
	if len(update) < 1 {
		glog.Debugf(ctx, "sAttachment.CategoryEdit: no update data, %+v", in)
		return nil
	}
	if len(in.UpdateAccount) > 0 {
		update[dao.AttachmentCategory.Columns().UpdateTime] = time.Now().UnixMilli()
		update[dao.AttachmentCategory.Columns().UpdateAccount] = in.UpdateAccount
	}
	_, err = dao.AttachmentCategory.Ctx(ctx).Where(dao.AttachmentCategory.Columns().Id, in.Id).Data(&update).Update()
	return
}

// CategoryDelete 附件分类删除
func (s sAttachment) CategoryDelete(ctx context.Context, in model.AttachmentCategoryDeleteInput) (err error) {
	update := g.Map{}
	if len(in.Ids) < 1 {
		glog.Debugf(ctx, "sAttachment.CategoryDelete: no update data, %+v", in)
		return nil
	}
	update[dao.AttachmentCategory.Columns().DeleteTime] = time.Now().UnixMilli()
	if len(in.UpdateAccount) > 0 {
		update[dao.AttachmentCategory.Columns().UpdateTime] = time.Now().UnixMilli()
		update[dao.AttachmentCategory.Columns().UpdateAccount] = in.UpdateAccount
	}
	_, err = dao.AttachmentCategory.Ctx(ctx).WhereIn(dao.AttachmentCategory.Columns().Id, in.Ids).Where(dao.AttachmentCategory.Columns().DeleteTime, 0).Data(&update).Update()
	return
}
