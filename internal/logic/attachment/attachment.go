package attachment

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	do "gtcms/internal/model/do/admin"

	"gtcms/internal/service"
	"time"
)

type sAttachment struct{}

func init() {
	service.RegisterAttachment(New())
}

func New() service.IAttachment {
	return &sAttachment{}
}

// AttachmentCount 获取指定条件的附件数量
func (s sAttachment) AttachmentCount(ctx context.Context, in model.AttachmentCountInput) (count int, err error) {
	m := dao.Attachment.Ctx(ctx)
	if len(in.CategoryIds) > 0 {
		m = m.Where(dao.Attachment.Columns().CategoryId, in.CategoryIds)
	}
	m = m.Where(dao.Attachment.Columns().DeleteTime, 0)
	count, err = m.Count()
	return
}

// List 附件列表
func (s sAttachment) List(ctx context.Context, in model.AttachmentListInput) (out *model.AttachmentListOutput, err error) {
	out = new(model.AttachmentListOutput)
	m := dao.Attachment.Ctx(ctx)
	if in.Id > 0 {
		m = m.Where(dao.Attachment.Columns().Id, in.Id)
	}
	if len(in.Name) > 0 {
		m = m.WhereLike(dao.Attachment.Columns().Name, in.Name+"%")
	}
	m = m.Where(dao.Attachment.Columns().DeleteTime, 0)
	out.Total, err = m.Count()
	if err != nil {
		return out, err
	}
	if out.Total < 1 {
		return out, nil
	}
	m = m.OrderDesc(dao.Attachment.Columns().Id)
	listSize := out.Total
	if in.Current > 0 && in.PageSize > 0 {
		m = m.Page(in.Current, in.PageSize)
		listSize = in.PageSize
	}
	out.List = make([]model.Attachment, 0, listSize)
	if err = m.Scan(&out.List); err != nil {
		return out, err
	}
	return out, nil
}

// Add 附件新增
func (s sAttachment) Add(ctx context.Context, in model.AttachmentAddInput) (id uint, err error) {
	size := in.Size
	if size < 1 { // 大小为0则到对象存储获取对象信息
		out, getErr := service.File().GetObjectAttributes(ctx, in.Key)
		if getErr != nil {
			err = getErr
			return
		}
		size = out.ObjectSize
	}
	doData := do.Attachment{
		CategoryId:    in.CategoryId,
		Name:          in.Name,
		Key:           service.File().HandleUpload(ctx, in.Key),
		Size:          size,
		Type:          in.Type,
		CreateTime:    gtime.Now().UnixMilli(),
		CreateAccount: in.CreateAccount,
		UpdateTime:    gtime.Now().UnixMilli(),
		UpdateAccount: in.CreateAccount,
		DeleteTime:    0,
	}
	insertId, err := dao.Attachment.Ctx(ctx).Data(&doData).InsertAndGetId()
	if err != nil {
		return
	}
	id = uint(insertId)

	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
		NewRecordOrAttr: in,
	})
	return
}

// Edit 附件编辑
func (s sAttachment) Edit(ctx context.Context, in model.AttachmentEditInput) (err error) {
	update := g.Map{}
	if len(in.Name) > 0 {
		update[dao.Attachment.Columns().Name] = in.Name
	}
	if in.CategoryId > 0 {
		update[dao.Attachment.Columns().CategoryId] = in.CategoryId
	}
	if len(update) < 1 {
		glog.Debugf(ctx, "sAttachment.Edit: no update data, %+v", in)
		return nil
	}
	if len(in.UpdateAccount) > 0 {
		update[dao.Attachment.Columns().UpdateTime] = time.Now().UnixMilli()
		update[dao.Attachment.Columns().UpdateAccount] = in.UpdateAccount
	}
	_, err = dao.Attachment.Ctx(ctx).WhereIn(dao.Attachment.Columns().Id, in.Ids).Data(&update).Update()
	return
}

// Delete 附件删除
func (s sAttachment) Delete(ctx context.Context, in model.AttachmentDeleteInput) (err error) {
	update := g.Map{}
	if len(in.Ids) < 1 {
		glog.Debugf(ctx, "sAttachment.Delete: no update data, %+v", in)
		return nil
	}
	keys, err := dao.Attachment.Ctx(ctx).Fields(dao.Attachment.Columns().Key).WhereIn(dao.Attachment.Columns().Id, in.Ids).Array()
	if err != nil {
		return
	}
	update[dao.Attachment.Columns().DeleteTime] = time.Now().UnixMilli()
	if len(in.UpdateAccount) > 0 {
		update[dao.Attachment.Columns().UpdateTime] = time.Now().UnixMilli()
		update[dao.Attachment.Columns().UpdateAccount] = in.UpdateAccount
	}
	_, err = dao.Attachment.Ctx(ctx).WhereIn(dao.Attachment.Columns().Id, in.Ids).Where(dao.Attachment.Columns().DeleteTime, 0).Data(&update).Update()
	if err == nil { // 删除文件
		deleteImages := make([]string, 0, len(keys))
		for _, key := range keys {
			deleteImages = append(deleteImages, key.String())
		}
		if removeErr := service.File().Remove(ctx, deleteImages...); removeErr != nil {
			g.Log().Error(ctx, removeErr)
		}
	}
	return
}
