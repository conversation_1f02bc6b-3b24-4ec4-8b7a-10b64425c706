package cmsLoginRisk

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	v1 "gtcms/api/v1"

	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	do "gtcms/internal/model/do/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
)

type (
	sCmsLoginRisk struct{}
)

func init() {
	service.RegisterCmsLoginRisk(New())
}

var cl = dao.CmsLoginRisk.Columns()

func New() service.ICmsLoginRisk {
	return &sCmsLoginRisk{}
}

func (s *sCmsLoginRisk) Create(ctx context.Context, in *entity.CmsLoginRisk) (err error) {
	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		in.CreateAccount = admin.Account
	}
	in.CreateTime = time.Now().UnixMilli()
	_, err = dao.CmsLoginRisk.Ctx(ctx).Data(in).Insert()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			NewRecordOrAttr: in,
		})
	}

	g.Go(ctx, func(ctx context.Context) {
		// 延迟一秒钟
		<-time.After(1 * time.Second)
		service.Nginx().Update(ctx)
	}, nil)
	return
}

func (s *sCmsLoginRisk) Delete(ctx context.Context, id uint) (err error) {
	var (
		isExisted bool
	)
	isExisted, err = s.IsIdExist(ctx, id)
	if err != nil {
		return err
	}
	if !isExisted {
		return gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}

	admin, _ := service.Utility().GetSelf(ctx)
	now := time.Now().UnixMilli()
	lb := &do.CmsLoginRisk{
		DeleteTime: now,
		UpdateTime: now,
	}
	if admin != nil {
		lb.UpdateAccount = admin.Account
	}

	var oldData entity.CmsLoginRisk
	_ = dao.CmsLoginRisk.Ctx(ctx).WherePri(id).Scan(&oldData)

	_, err = dao.CmsLoginRisk.Ctx(ctx).WherePri(id).Delete()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
		})
	}
	g.Go(ctx, func(ctx context.Context) {
		// 延迟一秒钟
		<-time.After(1 * time.Second)
		service.Nginx().Update(ctx)
	}, nil)
	return
}

func (s *sCmsLoginRisk) Edit(ctx context.Context, id uint, in *entity.CmsLoginRisk) (err error) {
	var oldData entity.CmsLoginRisk
	_ = dao.CmsLoginRisk.Ctx(ctx).WherePri(id).Scan(&oldData)

	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		in.UpdateAccount = admin.Account
	}
	in.UpdateTime = time.Now().UnixMilli()

	_, err = dao.CmsLoginRisk.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Data(in).Update()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
			NewRecordOrAttr: in,
		})
	}
	g.Go(ctx, func(ctx context.Context) {
		// 延迟一秒钟
		<-time.After(1 * time.Second)
		service.Nginx().Update(ctx)
	}, nil)
	return
}

func (s *sCmsLoginRisk) Detail(ctx context.Context, id uint) (out *v1.CmsLoginRiskDetailRes, err error) {
	if id == 0 {
		admin, err := service.Utility().GetSelf(ctx)
		if err != nil {
			return nil, err
		}
		id = admin.Id
	}

	err = dao.CmsLoginRisk.Ctx(ctx).WherePri(id).Scan(&out)
	if out == nil {
		return nil, gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}
	return
}

func (s *sCmsLoginRisk) List(ctx context.Context, in *v1.CmsLoginRiskListReq) (out *v1.CmsLoginRiskListRes, err error) {
	out = new(v1.CmsLoginRiskListRes)
	out.Current = in.Current
	md := dao.CmsLoginRisk.Ctx(ctx).Where(cl.DeleteTime, 0).Where(cl.TabType, in.TabType).
		Where(cl.IsOpen, in.IsOpen).OmitEmptyWhere().OmitNilWhere()

	if in.StartTime > 0 {
		md = md.WhereGTE(cl.CreateTime, in.StartTime)
	}
	if in.EndTime > 0 {
		md = md.WhereLTE(cl.CreateTime, in.EndTime)
	}

	out.Total, err = md.Count()
	if err != nil {
		return nil, err
	}
	err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&out.List)

	if in.Key != "" {
		// 过滤结果以匹配in.Key
		var filteredList []*v1.CmsLoginRiskVo
		for _, item := range out.List {
			if service.Utility().IsInIps([]string{item.Content}, in.Key) {
				filteredList = append(filteredList, item)
			}
		}
		out.List = filteredList
	}
	return
}

func (s *sCmsLoginRisk) IsIdExist(ctx context.Context, id uint) (bool, error) {
	count, err := dao.CmsLoginRisk.Ctx(ctx).WherePri(id).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
