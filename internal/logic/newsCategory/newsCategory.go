package newsCategory

import (
	"context"
	"database/sql"
	"errors"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"

	dao "gtcms/internal/dao/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"

	"gtcms/internal/service"
	"time"
)

type sNewsCategory struct{}

func init() {
	service.RegisterNewsCategory(New())
}

func New() *sNewsCategory {
	return &sNewsCategory{}
}

// 新增
func (s *sNewsCategory) Add(ctx context.Context, req *v1.NewsCategoryAddReq) error {
	// 参数校验
	if 0 > req.Sort {
		return gerror.New("排序值不能小于0")
	}
	admin, _ := service.Utility().GetSelf(ctx)
	currentTime := time.Now().UnixMilli()
	categoryData := entity.NewsCategory{
		CoverImgs:  req.CoverImgs,
		Creater:    admin.Id,
		CreateName: admin.Account,
		CreateTime: currentTime,
		UpdateTime: currentTime,
		ParentId:   req.ParentId,
		Remark:     req.Remark,
		Sort:       req.Sort,
		Status:     gconv.Uint(req.Status),
		IsAppShow:  req.IsAppShow,
	}
	var categoryLanguageData = make([]entity.NewsCategoryLanguage, 0)
	// 校验文章名称和内容
	for _, item := range req.NameArr {
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return gerror.New("语言类型错误")
		}
		one := entity.NewsCategoryLanguage{
			Name:       item.CategoryName,
			CreateTime: currentTime,
			LanguageId: gconv.Uint(item.LanguageType),
		}
		if consts.Zero == item.LanguageType {
			categoryData.IsZh = consts.One
		}
		if consts.One == item.LanguageType {
			categoryData.IsEn = consts.One
		}
		if consts.Two == item.LanguageType {
			categoryData.IsId = consts.One
		}
		categoryLanguageData = append(categoryLanguageData, one)
	}
	// 写入表
	err := dao.NewsCategory.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		id, err1 := dao.NewsCategory.Ctx(ctx).Data(categoryData).InsertAndGetId()
		if err1 != nil {
			return err1
		}
		// 组装数据
		for key := range categoryLanguageData {
			categoryLanguageData[key].CategoryId = gconv.Uint(id)
		}
		// 批量插入
		_, err1 = dao.NewsCategoryLanguage.Ctx(ctx).Data(categoryLanguageData).Insert()
		return err1
	})
	return err
}

// 编辑
func (s *sNewsCategory) Edit(ctx context.Context, req *v1.NewsCategoryEditReq) error {
	// 参数校验
	if 0 >= req.Id {
		return gerror.New("分类id不能为空")
	}
	// 获取分类信息
	var category entity.NewsCategory
	err := dao.NewsCategory.Ctx(ctx).Where(dao.NewsCategory.Columns().Id, req.Id).Scan(&category)
	if err != nil {
		return err
	}
	if 0 >= category.Id {
		return gerror.New("分类不存在")
	}
	admin, _ := service.Utility().GetSelf(ctx)
	currentTime := time.Now().UnixMilli()
	categoryData := g.Map{
		dao.NewsCategory.Columns().ParentId:   req.ParentId,
		dao.NewsCategory.Columns().AdminId:    category.AdminId, // 分类负责人id
		dao.NewsCategory.Columns().CoverImgs:  req.CoverImgs,
		dao.NewsCategory.Columns().Creater:    admin.Id,
		dao.NewsCategory.Columns().CreateName: admin.Account,
		dao.NewsCategory.Columns().UpdateTime: currentTime,
		dao.NewsCategory.Columns().Remark:     req.Remark,
		dao.NewsCategory.Columns().Sort:       req.Sort,
		dao.NewsCategory.Columns().Status:     req.Status,
		dao.NewsCategory.Columns().IsAppShow:  req.IsAppShow,
		dao.NewsCategory.Columns().IsZh:       consts.Zero,
		dao.NewsCategory.Columns().IsEn:       consts.Zero,
		dao.NewsCategory.Columns().IsId:       consts.Zero,
	}
	var categoryLanguageData = make([]entity.NewsCategoryLanguage, 0)
	// 校验文章名称和内容
	for _, item := range req.NameArr {
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return gerror.New("语言类型错误")
		}
		one := entity.NewsCategoryLanguage{
			Name:       item.CategoryName,
			CreateTime: category.CreateTime,
			UpdateTime: currentTime,
			LanguageId: gconv.Uint(item.LanguageType),
		}
		if consts.Zero == item.LanguageType {
			categoryData[dao.NewsCategory.Columns().IsZh] = consts.One
		}
		if consts.One == item.LanguageType {
			categoryData[dao.NewsCategory.Columns().IsEn] = consts.One
		}
		if consts.Two == item.LanguageType {
			categoryData[dao.NewsCategory.Columns().IsId] = consts.One
		}
		categoryLanguageData = append(categoryLanguageData, one)
	}
	// 写入表
	err = dao.NewsCategory.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		_, err1 = dao.NewsCategory.Ctx(ctx).Where(dao.NewsCategory.Columns().Id, req.Id).Data(categoryData).Update()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.NewsCategoryLanguage.Ctx(ctx).Where(dao.NewsCategoryLanguage.Columns().CategoryId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		// 组装数据
		for key := range categoryLanguageData {
			categoryLanguageData[key].CategoryId = gconv.Uint(req.Id)
		}
		// 批量插入
		_, err1 = dao.NewsCategoryLanguage.Ctx(ctx).Data(categoryLanguageData).Insert()
		return err1
	})
	return err
}

// 详情
func (s *sNewsCategory) Info(ctx context.Context, req *v1.NewsCategoryInfoReq) (out *v1.NewsCategoryInfoRes, err error) {
	out = &v1.NewsCategoryInfoRes{}
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("参数错误")
	}
	var category *entity.NewsCategory
	err = dao.NewsCategory.Ctx(ctx).Where(req.Id).Scan(&category)
	if err != nil {
		return out, err
	}
	if 0 >= category.Id {
		return out, gerror.New("分类不存在")
	}
	// 获取分类名称
	var categoryLanguage []*entity.NewsCategoryLanguage
	err = dao.NewsCategoryLanguage.Ctx(ctx).Where(dao.NewsCategoryLanguage.Columns().CategoryId, category.Id).Scan(&categoryLanguage)
	if err != nil {
		return out, err
	}
	var nameArr = make([]v1.NewsCategoryNameArrItem, 0)
	for _, v := range categoryLanguage {
		nameArr = append(nameArr, v1.NewsCategoryNameArrItem{
			LanguageType: gconv.Int(v.LanguageId), // 0特殊处理
			CategoryName: v.Name,
		})
	}
	out = &v1.NewsCategoryInfoRes{
		CoverImgs: category.CoverImgs,
		IsAppShow: gconv.Uint(category.IsAppShow),
		NameArr:   nameArr,
		ParentId:  category.ParentId,
		Remark:    category.Remark,
		Sort:      category.Sort,
		Status:    gconv.Int(category.Status),
	}
	return out, nil
}

// 列表
func (s *sNewsCategory) List(ctx context.Context, req *v1.NewsCategoryListReq) (out *v1.NewsCategoryListRes, err error) {
	out = new(v1.NewsCategoryListRes)
	// 获取语言
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))
	// 查询条件，保证父级分类
	orm := dao.NewsCategory.Ctx(ctx).
		Where(dao.NewsCategory.Columns().ParentId, consts.Zero).
		Where(dao.NewsCategory.Columns().DeleteTime, consts.Zero)
	if "" != req.CategoryName {
		type categoryLanguage struct {
			CategoryId uint `json:"category_id"`
		}
		var categoryLanguageList []*categoryLanguage
		err = dao.NewsCategoryLanguage.Ctx(ctx).
			WhereLike(dao.NewsCategoryLanguage.Columns().Name, "%"+req.CategoryName+"%").
			Where(dao.NewsCategoryLanguage.Columns().DeleteTime, consts.Zero).
			Scan(&categoryLanguageList)
		if err != nil {
			return out, err
		}
		// 提取分类id
		categoryIds := gutil.ListItemValuesUnique(categoryLanguageList, "CategoryId")
		if len(categoryIds) > 0 {
			orm = orm.Where(dao.NewsCategory.Columns().Id, categoryIds)
		}
	}
	if req.Status != -1 {
		orm = orm.Where(dao.NewsCategory.Columns().Status, req.Status)
	}
	// 获取总数
	out.Total, err = orm.Count()
	if err != nil {
		out.List = []v1.NewsCategoryListItem{}
		return out, err
	}
	var categoryList []*entity.NewsCategory
	err = orm.Page(req.Current, req.PageSize).OrderDesc(dao.NewsCategory.Columns().CreateTime).Scan(&categoryList)
	if err != nil {
		return out, err
	}
	// 提取分类id
	categoryIds := gutil.ListItemValuesUnique(categoryList, "Id")
	// 查询分类名称
	type categoryLanguage struct {
		CategoryId int    `json:"category_id"`
		LanguageId int    `json:"language_id"`
		Name       string `json:"name"`
	}
	var categoryLanguageList []*categoryLanguage
	err = dao.NewsCategoryLanguage.Ctx(ctx).WhereIn(dao.NewsCategoryLanguage.Columns().CategoryId, categoryIds).Scan(&categoryLanguageList)
	if err != nil {
		return
	}
	categoryLanguageMap := make(map[int]map[int]string)
	for _, v := range categoryLanguageList {
		if _, ok := categoryLanguageMap[v.CategoryId]; !ok {
			categoryLanguageMap[v.CategoryId] = make(map[int]string)
		}
		categoryLanguageMap[v.CategoryId][v.LanguageId] = v.Name
	}
	// 组装返回值
	for _, v := range categoryList {
		one := v1.NewsCategoryListItem{
			Id:         v.Id,
			Sort:       v.Sort,
			Status:     gconv.Int(v.Status),
			StatusText: consts.GetNewCategoryStatusText(gconv.Int(v.Status)),
			Children:   []*v1.NewsCategoryListItem{},
		}
		// 支持的语言
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Zero,
			LanguageTypeText: "ZH",
			IsSupport:        gconv.Int(v.IsZh),
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.One,
			LanguageTypeText: "EN",
			IsSupport:        gconv.Int(v.IsEn),
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Two,
			LanguageTypeText: "ID",
			IsSupport:        gconv.Int(v.IsId),
		})
		// 展示当前语言的分类名称
		one.CategoryName = categoryLanguageMap[gconv.Int(v.Id)][currentLang]
		// 获取子分类
		var children []*entity.NewsCategory
		err = dao.NewsCategory.Ctx(ctx).Where(dao.NewsCategory.Columns().ParentId, v.Id).Scan(&children)
		if err != nil {
			return out, err
		}
		if len(children) > 0 {
			// 组装子分类
			for _, item := range children {
				child := v1.NewsCategoryListItem{
					Id:         item.Id,
					Sort:       item.Sort,
					Status:     gconv.Int(item.Status),
					StatusText: consts.GetNewCategoryStatusText(gconv.Int(item.Status)),
					Children:   []*v1.NewsCategoryListItem{},
				}
				// 支持的语言
				child.LanguageArr = append(child.LanguageArr, v1.LanguageArrItem{
					LanguageType:     consts.Zero,
					LanguageTypeText: "ZH",
					IsSupport:        gconv.Int(item.IsZh),
				})
				child.LanguageArr = append(child.LanguageArr, v1.LanguageArrItem{
					LanguageType:     consts.One,
					LanguageTypeText: "EN",
					IsSupport:        gconv.Int(item.IsEn),
				})
				child.LanguageArr = append(child.LanguageArr, v1.LanguageArrItem{
					LanguageType:     consts.Two,
					LanguageTypeText: "ID",
					IsSupport:        gconv.Int(item.IsId),
				})
				// 分类名称
				type childCategory struct {
					Name string `json:"name"`
				}
				var childCategoryInfo childCategory
				// 获取分类名称
				err = dao.NewsCategoryLanguage.Ctx(ctx).
					Where(dao.NewsCategoryLanguage.Columns().CategoryId, item.Id).
					Where(dao.NewsCategoryLanguage.Columns().LanguageId, currentLang).
					Scan(&childCategoryInfo)
				if err != nil && !errors.Is(err, sql.ErrNoRows) {
					return out, err
				}
				child.CategoryName = childCategoryInfo.Name
				one.Children = append(one.Children, &child)
			}
		}

		out.List = append(out.List, one)
	}
	return
}

// 删除
func (s *sNewsCategory) Delete(ctx context.Context, req *v1.NewsCategoryDeleteReq) error {
	// 参数校验
	if 1 > len(req.Ids) {
		return gerror.New("请选择要删除的分类")
	}
	err := dao.NewsCategory.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := dao.NewsCategory.Ctx(ctx).WhereIn(dao.NewsCategory.Columns().Id, req.Ids).Data(g.Map{
			dao.NewsCategory.Columns().DeleteTime: time.Now().UnixMilli(),
		}).Update()
		if err != nil {
			return err
		}
		_, err = dao.NewsCategoryLanguage.Ctx(ctx).
			WhereIn(dao.NewsCategoryLanguage.Columns().CategoryId, req.Ids).
			Data(g.Map{
				dao.NewsCategoryLanguage.Columns().DeleteTime: time.Now().UnixMilli(),
			}).Update()
		return err
	})
	return err
}
