package stat

//
//import (
//	"context"
//	"github.com/gogf/gf/v2/frame/g"
//	"github.com/gogf/gf/v2/util/gconv"
//	v1 "gtcms/api/v1"
//	"gtcms/internal/consts"
//	"gtcms/internal/dao"
//	"gtcms/internal/model"
//	"gtcms/internal/model/entity"
//	"gtcms/internal/service"
//	"strings"
//)
//
//type sStat struct {
//}
//
//func init() {
//	service.RegisterStat(New())
//}
//
//func New() *sStat {
//	s := &sStat{}
//	return s
//}
//
//func (s *sStat) Run(ctx context.Context) {
//	self, err := service.Utility().GetSelf(ctx)
//	if err != nil {
//		return
//	}
//
//	ac, err := service.RoleV2().Detail(ctx, self.RoleId)
//	if err != nil {
//		return
//	}
//
//	var rs []model.StatInfo
//	adminOne := model.StatInfo{
//		Name: "管理员",
//	}
//	startTime, endTime := service.Utility().GetTodayTimestamps()
//
//	scRes, err := service.SelectorConfig().List(ctx, &v1.SelectorConfigListReq{
//		SelectType: "seo-status",
//	})
//	if err != nil {
//		return
//	}
//	var seoStatus uint
//	for _, v := range scRes.List {
//		if strings.Contains(v.Title, "排名站") {
//			seoStatus = v.Id
//			break
//		}
//	}
//
//	mStartTime, mEndTime := service.Utility().GetMonthTimestamps()
//	var accountList []*v1.AccountVo
//	if consts.RoleLevelAdmin == ac.RoleLevel {
//		// 今天分配的域名
//		list, err := service.Domain().List(ctx, &v1.DomainListReq{RegisterTimeStart: &startTime, RegisterTimeEnd: &endTime})
//		if err != nil {
//			return
//		}
//		adminOne.TAllotDomainCount = list.Total
//		var domainIds []uint
//		for _, v := range list.List {
//			domainIds = append(domainIds, v.Id)
//		}
//		// 今日蜘蛛量
//		count, err := dao.SpiderLog.Ctx(ctx).WhereGTE(dao.SpiderLog.Columns().AccessTime, startTime).
//			WhereLT(dao.SpiderLog.Columns().AccessTime, endTime).WhereIn(dao.SpiderLog.Columns().DomainId, domainIds).Count()
//		if err != nil {
//			return
//		}
//		adminOne.TSpiderCount = count
//
//		// 昨天新建站点
//		var siteList []*entity.Site
//		err = dao.Site.Ctx(ctx).WhereGTE(dao.Site.Columns().CreateTime, startTime).WhereLT(dao.Site.Columns().CreateTime, endTime).Scan(&siteList)
//		if err != nil {
//			return
//		}
//		adminOne.YNewSiteCount = len(siteList)
//
//		// 排名个数
//		rankCount := 0
//		for _, v := range siteList {
//			if v.SeoStatus == seoStatus {
//				rankCount++
//			}
//		}
//		adminOne.YRankSiteCount = rankCount
//
//		// 本月建站数
//		var siteList2 []*entity.Site
//		var cnCount, idCount int
//		err = dao.Site.Ctx(ctx).WhereGTE(dao.Site.Columns().CreateTime, mStartTime).WhereLT(dao.Site.Columns().CreateTime, mEndTime).Scan(&siteList2)
//		for _, v := range siteList2 {
//			if v.Language == "cn" {
//				cnCount++
//			} else if v.Language == "id" {
//				idCount++
//			}
//		}
//		adminOne.MSiteCnCount = cnCount
//		adminOne.MSiteIdCount = idCount
//
//		// 未建站域名个数
//		var noUsedCount int
//		noUsedCount, err = dao.Domain.Ctx(ctx).Where(dao.Domain.Columns().Used, 2).Where(dao.Domain.Columns().DeleteTime, 0).Count()
//		if err != nil {
//			return
//		}
//		adminOne.NoUsedDomainCount = noUsedCount
//		rs = append(rs, adminOne)
//
//		// seo角色
//		roleOne, err := dao.RolePermissionConfig.Ctx(ctx).Fields(dao.RolePermissionConfig.Columns().Id).Where(dao.RolePermissionConfig.Columns().RoleLevel, consts.RoleLevelSiteLeader).One()
//		if err != nil {
//			return
//		}
//
//		roleId := gconv.Uint(roleOne["id"])
//		seoList, err := service.Account().List(ctx, &v1.AccountListReq{
//			RoleId: &roleId,
//		})
//		if err != nil {
//			return
//		}
//		accountList = seoList.List
//	} else {
//		var a *v1.AccountVo
//		err = gconv.Struct(self, &a)
//		if err != nil {
//			return
//		}
//		accountList = append(accountList, a)
//	}
//
//	for _, v := range accountList {
//		seoOne := model.StatInfo{
//			Name: v.NickName,
//		}
//		// 今天分配的域名
//		sDomainList, _ := service.Domain().List(ctx, &v1.DomainListReq{RegisterTimeStart: &startTime, RegisterTimeEnd: &endTime, BelongUserId: &v.Id})
//		seoOne.TAllotDomainCount = sDomainList.Total
//
//		var sDomainIds []uint
//		for _, vv := range sDomainList.List {
//			sDomainIds = append(sDomainIds, vv.Id)
//		}
//		// 今日蜘蛛量
//		sCount, _ := dao.SpiderLog.Ctx(ctx).WhereGTE(dao.SpiderLog.Columns().AccessTime, startTime).
//			WhereLT(dao.SpiderLog.Columns().AccessTime, endTime).WhereIn(dao.SpiderLog.Columns().DomainId, sDomainIds).Count()
//		seoOne.TSpiderCount = sCount
//
//		// 昨天新建站点
//		var sSiteList []*entity.Site
//		_ = dao.Site.Ctx(ctx).WhereGTE(dao.Site.Columns().CreateTime, startTime).
//			WhereLT(dao.Site.Columns().CreateTime, endTime).Where(dao.Site.Columns().Creater, v.Id).Scan(&sSiteList)
//		seoOne.YNewSiteCount = len(sSiteList)
//
//		// 排名个数
//		sRankCount := 0
//		for _, vv := range sSiteList {
//			if vv.SeoStatus == seoStatus {
//				sRankCount++
//			}
//		}
//		seoOne.YRankSiteCount = sRankCount
//
//		// 本月建站数
//		var sSiteList2 []*entity.Site
//		var sCnCount, sIdCount int
//		_ = dao.Site.Ctx(ctx).WhereGTE(dao.Site.Columns().CreateTime, mStartTime).
//			WhereLT(dao.Site.Columns().CreateTime, mEndTime).Where(dao.Site.Columns().Creater, v.Id).Scan(&sSiteList2)
//		for _, vv := range sSiteList2 {
//			if vv.Language == "cn" {
//				sCnCount++
//			} else if vv.Language == "id" {
//				sIdCount++
//			}
//		}
//		seoOne.MSiteCnCount = sCnCount
//		seoOne.MSiteIdCount = sIdCount
//
//		// 未建站域名个数
//		var sNoUsedCount int
//		sNoUsedCount, _ = dao.Domain.Ctx(ctx).Where(dao.Domain.Columns().Used, 2).Where(dao.Domain.Columns().BelongUserId, v.Id).Where(dao.Domain.Columns().DeleteTime, 0).Count()
//		seoOne.NoUsedDomainCount = sNoUsedCount
//
//		rs = append(rs, seoOne)
//	}
//
//	// excel
//	if err = service.FileProcessor().Export2ExcelCn(ctx, rs); err != nil {
//		g.Log().Line().Error(ctx, err, "导出excel数据失败")
//		return
//	}
//	return
//}
