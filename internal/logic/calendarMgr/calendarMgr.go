package calendarMgr

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/hablullah/go-hijri"

	islamicDao "gtcms/internal/dao/islamic_content_svc"
	islamicDo "gtcms/internal/model/do/islamic_content_svc"

	"gtcms/internal/service"
)

// sCalendarMgr 日历管理服务实现
type sCalendarMgr struct{}

func init() {
	service.RegisterCalendarMgr(New())
}

func New() service.ICalendarMgr {
	return &sCalendarMgr{}
}

// Init 初始化日历数据
func (s *sCalendarMgr) Init(ctx context.Context, year int) error {
	// 检查年份是否已经初始化过
	count, err := islamicDao.CalendarHijriah.Ctx(ctx).Where(islamicDo.CalendarHijriah{
		GregorianYear: year,
	}).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		g.Log().Warning(ctx, "年份 %d 的日历数据已存在", year)
		return nil
	}

	// 生成该年份的所有日期数据
	var records []islamicDo.CalendarHijriah
	startDate := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Date(year+1, 1, 1, 0, 0, 0, 0, time.UTC)

	for currentDate := startDate; currentDate.Before(endDate); currentDate = currentDate.AddDate(0, 0, 1) {
		// 计算 UMMUL_QURA 方法的伊斯兰历
		ummAlQuraDate, err := hijri.CreateUmmAlQuraDate(currentDate)
		if err != nil {
			g.Log().Errorf(ctx, "计算 UMMUL_QURA 日期失败: %v, 日期: %s", err, currentDate.Format("2006-01-02"))
			return err
		}

		// 计算 Pasaran
		pasaranIndex := calculatePasaran(currentDate)

		// 添加 UMMUL_QURA 记录
		records = append(records, islamicDo.CalendarHijriah{
			GregorianYear:  currentDate.Year(),
			GregorianMonth: int(currentDate.Month()),
			GregorianDay:   currentDate.Day(),
			HijriahYear:    int(ummAlQuraDate.Year),
			HijriahMonth:   int(ummAlQuraDate.Month),
			HijriahDay:     int(ummAlQuraDate.Day),
			MethodCode:     "UMMUL_QURA",
			Weekday:        int(currentDate.Weekday()),
			Pasaran:        pasaranIndex,
			//CreatedAt:      gtime.Now(),
			//UpdatedAt:      gtime.Now(),
		})

		// 计算 LFNU 方法的伊斯兰历（使用标准算法）
		lfnuDate, err := hijri.CreateHijriDate(currentDate, hijri.Default)
		if err != nil {
			g.Log().Errorf(ctx, "计算 LFNU 日期失败: %v, 日期: %s", err, currentDate.Format("2006-01-02"))
			return err
		}

		// 添加 LFNU 记录
		records = append(records, islamicDo.CalendarHijriah{
			GregorianYear:  currentDate.Year(),
			GregorianMonth: int(currentDate.Month()),
			GregorianDay:   currentDate.Day(),
			HijriahYear:    int(lfnuDate.Year),
			HijriahMonth:   int(lfnuDate.Month),
			HijriahDay:     int(lfnuDate.Day),
			MethodCode:     "LFNU",
			Weekday:        int(currentDate.Weekday()),
			Pasaran:        pasaranIndex,
			//CreatedAt:      gtime.Now(),
			//UpdatedAt:      gtime.Now(),
		})
	}

	// 批量插入数据
	if len(records) > 0 {
		_, err = islamicDao.CalendarHijriah.Ctx(ctx).Data(records).Insert()
		if err != nil {
			return err
		}
	}

	return nil
}

// calculatePasaran 计算指定日期的 Pasaran 值
// Pasaran 是爪哇历法中的五日循环系统
// 参数: date - 公历日期
// 返回: Pasaran 索引 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
func calculatePasaran(date time.Time) int {
	// 基准日期：1968-12-03 是 Slasa Kliwon (JDN = 2440213)
	baseDate := time.Date(1968, 12, 3, 0, 0, 0, 0, time.UTC)
	basePasaran := 0 // Kliwon

	// 计算目标日期的天数差
	daysDiff := int(date.Sub(baseDate).Hours() / 24)
	pasaranIndex := (basePasaran + daysDiff) % 5
	if pasaranIndex < 0 {
		pasaranIndex += 5
	}

	return pasaranIndex
}
