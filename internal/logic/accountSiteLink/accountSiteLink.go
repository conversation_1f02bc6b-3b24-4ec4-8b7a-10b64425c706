package accountSiteLink

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/util/gconv"
	model "gtcms/internal/model/admin"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	v1 "gtcms/api/v1"
	dao "gtcms/internal/dao/admin"
	do "gtcms/internal/model/do/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
)

type (
	sAccountSiteLink struct{}
)

func init() {
	service.RegisterAccountSiteLink(New())
}

var cl = dao.AccountSiteLink.Columns()

func New() service.IAccountSiteLink {
	return &sAccountSiteLink{}
}

func (s *sAccountSiteLink) Create(ctx context.Context, in *entity.AccountSiteLink) (err error) {
	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		in.CreateAccount = admin.Account
		in.CreateTime = time.Now().UnixMilli()
	}
	_, err = dao.AccountSiteLink.Ctx(ctx).Data(in).Save()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			NewRecordOrAttr: in,
		})
	}
	return
}

func (s *sAccountSiteLink) Delete(ctx context.Context, id, accountId uint) (err error) {
	var (
		isExisted bool
	)
	isExisted, err = s.IsIdExist(ctx, id)
	if err != nil {
		return err
	}
	if !isExisted {
		return gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}

	var oldData entity.AccountSiteLink
	_ = dao.AccountSiteLink.Ctx(ctx).WherePri(id).Scan(&oldData)
	if oldData.AccountId != accountId {
		return gerror.New(fmt.Sprintf("id=%d accountId=%d not match!", id, accountId))
	}

	admin, _ := service.Utility().GetSelf(ctx)
	now := time.Now().UnixMilli()
	lb := &do.AccountSiteLink{
		UpdateAccount: admin.Account,
		UpdateTime:    now,
	}
	if admin != nil {
		lb.UpdateAccount = admin.Account
	}

	_, err = dao.AccountSiteLink.Ctx(ctx).WherePri(id).Delete()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
		})
	}
	return
}

func (s *sAccountSiteLink) Edit(ctx context.Context, id uint, in *entity.AccountSiteLink) (err error) {
	var oldData entity.AccountSiteLink
	_ = dao.AccountSiteLink.Ctx(ctx).WherePri(id).Scan(&oldData)

	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		in.UpdateAccount = admin.Account
	}
	in.UpdateTime = time.Now().UnixMilli()

	_, err = dao.AccountSiteLink.Ctx(ctx).WherePri(id).Data(in).Update()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
			NewRecordOrAttr: in,
		})
	}
	return
}

func (s *sAccountSiteLink) Detail(ctx context.Context, id uint) (out *v1.AccountSiteLinkDetailRes, err error) {
	if id == 0 {
		admin, err := service.Utility().GetSelf(ctx)
		if err != nil {
			return nil, err
		}
		id = admin.Id
	}

	err = dao.AccountSiteLink.Ctx(ctx).WherePri(id).Scan(&out)
	if out == nil {
		return nil, gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}
	return
}

func (s *sAccountSiteLink) List(ctx context.Context, in *v1.AccountSiteLinkListReq) (ret map[uint]*v1.AccountSiteLinkVo, err error) {
	//out = new(v1.AccountSiteLinkListRes)
	//out.Current = in.Current
	var recs []*v1.AccountSiteLinkVo
	md := dao.AccountSiteLink.Ctx(ctx).Where(cl.AccountId, in.AccountId).Where(cl.GroupId, in.GroupId).
		Where(cl.SiteId, in.SiteId).OmitNilWhere().OmitEmptyWhere()
	//if in.StartTime > 0 {
	//	md = md.WhereGTE(cl.CreateTime, in.StartTime)
	//}
	//if in.EndTime > 0 {
	//	md = md.WhereLTE(cl.CreateTime, in.EndTime)
	//}
	//if len(in.Key) > 0 {
	//	md = md.WhereLike(cl.Word, "%"+in.Key+"%")
	//}
	//out.Total, err = md.Count()
	//if err != nil {
	//	return nil, err
	//}
	err = md.Page(in.Current, in.PageSize).OrderDesc(cl.Id).Scan(&recs)
	if err != nil {
		return nil, err
	}
	//mapCategoryIDName, _ := s.mapCategoryIDName(ctx)
	//if g.IsEmpty(mapCategoryIDName) {
	//	return
	//}
	//

	ret = make(map[uint]*v1.AccountSiteLinkVo, len(recs))
	for _, v := range recs {
		ret[v.SiteId] = v
	}
	return
}

func (s *sAccountSiteLink) GetSiteIDsByAccount(ctx context.Context, accountID uint) (rets []uint, err error) {
	vals, err := dao.AccountSiteLink.Ctx(ctx).Fields(cl.SiteId).Where(cl.AccountId, accountID).Where(cl.IsAffect, 1).
		Array()
	if err != nil {
		return
	}
	rets = gconv.Uints(vals)
	return
}

func (s *sAccountSiteLink) GetGroupIDsByAccount(ctx context.Context, accountID uint) (rets []uint, err error) {
	vals, err := dao.AccountSiteLink.Ctx(ctx).Fields(cl.GroupId).Where(cl.AccountId, accountID).
		Where(cl.IsAffect, 1).Array()
	if err != nil {
		return
	}
	rets = gconv.Uints(vals)
	return
}

func (s *sAccountSiteLink) IsIdExist(ctx context.Context, id uint) (bool, error) {
	count, err := dao.AccountSiteLink.Ctx(ctx).WherePri(id).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

//func (s *sAccountSiteLink) mapCategoryIDName(ctx context.Context) (ret map[int]string, err error) {
//	var recs []*entity.AccountSiteLinkCategory
//	cl := dao.AccountSiteLinkCategory.Columns()
//	err = dao.AccountSiteLinkCategory.Ctx(ctx).Where(cl.IsOpen, 1).Scan(&recs)
//
//	ret = make(map[int]string, len(recs))
//	for _, v := range recs {
//		ret[v.CategoryId] = v.Name
//	}
//	return
//}
