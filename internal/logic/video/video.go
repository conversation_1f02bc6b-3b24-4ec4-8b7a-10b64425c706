package video

import (
	"context"
	"database/sql"
	"errors"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	"gtcms/internal/service"
	"time"
)

type sVideo struct{}

func init() {
	service.RegisterVideo(New())
}

func New() *sVideo {
	return &sVideo{}
}

// 添加
func (s *sVideo) Add(ctx context.Context, req *v1.VideoAddReq) error {
	// 参数校验
	if 0 >= req.CategoryId {
		return gerror.New("请选择分类")
	}
	if "" == req.CoverImgs {
		return gerror.New("请上传封面图")
	}
	if "" == req.VideoUrl {
		return gerror.New("请上传视频")
	}
	admin, _ := service.Utility().GetSelf(ctx)
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	// 时间转成时间戳，毫秒级
	var publishTime uint64
	if "" != req.PublishTime {
		tmp, _ := time.ParseInLocation("2006-01-02 15:04:05", req.PublishTime, time.Local)
		publishTime = gconv.Uint64(tmp.UnixMilli())
	}
	videoData := entity.Videos{
		CategoryId:    req.CategoryId,
		VideoUrl:      req.VideoUrl,
		VideoSize:     0,
		VideoDuration: 0,
		VideoFormat:   "",
		VideoCoverUrl: req.CoverImgs,
		CreatorName:   admin.Account,
		Author:        req.Author,
		PublishState:  0,
		IsRecommended: req.IsRecommend,
		CreateTime:    currentTime,
		PublishTime:   publishTime,
		UpdateTime:    currentTime,
	}
	if req.IsPublish == 1 {
		videoData.PublishState = consts.One
		videoData.IsDraft = consts.Zero
	}
	if req.IsDraft == 1 {
		videoData.IsDraft = consts.One
		videoData.PublishState = consts.Zero
	}
	var videoLanguageData = make([]entity.VideoLanguages, 0)
	for _, item := range req.VideoTitleArr {
		if "" == item.VideoTitle {
			return gerror.New("视频标题不能为空")
		}
		if "" == item.Description {
			return gerror.New("视频简介不能为空")
		}
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return gerror.New("语言类型错误")
		}
		one := entity.VideoLanguages{
			LanguageId:  gconv.Uint(item.LanguageType),
			Title:       item.VideoTitle,
			Description: item.Description,
			CreateTime:  currentTime,
			UpdateTime:  currentTime,
		}
		videoLanguageData = append(videoLanguageData, one)
	}
	// 写入表
	err := dao.Videos.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		id, err1 := dao.Videos.Ctx(ctx).Data(videoData).InsertAndGetId()
		if err1 != nil {
			return err1
		}
		for key := range videoLanguageData {
			videoLanguageData[key].VideoId = gconv.Uint(id)
		}
		_, err1 = dao.VideoLanguages.Ctx(ctx).Data(videoLanguageData).Insert()
		return err1
	})

	return err
}

// 编辑
func (s *sVideo) Edit(ctx context.Context, req *v1.VideoEditReq) error {
	// 参数校验
	if 0 >= req.Id {
		return gerror.New("视频id不能为空")
	}
	// 获取视频信息
	var video entity.Videos
	err := dao.Videos.Ctx(ctx).Where(dao.Videos.Columns().Id, req.Id).Scan(&video)
	if err != nil {
		return err
	}
	if 0 >= video.Id {
		return gerror.New("视频不存在")
	}
	if 0 >= req.CategoryId {
		return gerror.New("请选择分类")
	}
	if "" == req.CoverImgs {
		return gerror.New("请上传封面图")
	}
	if "" == req.VideoUrl {
		return gerror.New("请上传视频")
	}
	admin, _ := service.Utility().GetSelf(ctx)
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	// 时间转成时间戳，毫秒级
	var publishTime uint64
	if "" != req.PublishTime {
		tmp, _ := time.ParseInLocation("2006-01-02 15:04:05", req.PublishTime, time.Local)
		publishTime = gconv.Uint64(tmp.UnixMilli())
	}
	videoData := g.Map{
		dao.Videos.Columns().CategoryId:    req.CategoryId,
		dao.Videos.Columns().VideoUrl:      req.VideoUrl,
		dao.Videos.Columns().VideoCoverUrl: req.CoverImgs,
		dao.Videos.Columns().CreatorName:   admin.Account,
		dao.Videos.Columns().Author:        req.Author,
		dao.Videos.Columns().IsRecommended: req.IsRecommend,
		dao.Videos.Columns().PublishTime:   publishTime,
		dao.Videos.Columns().UpdateTime:    currentTime,
	}
	if consts.One == req.IsPublish {
		videoData[dao.Videos.Columns().PublishState] = consts.One
		videoData[dao.Videos.Columns().IsDraft] = consts.Zero
	}
	if consts.One == req.IsDraft {
		videoData[dao.Videos.Columns().IsDraft] = consts.One
		videoData[dao.Videos.Columns().PublishState] = consts.Zero
	}
	var videoLanguageData = make([]entity.VideoLanguages, 0)
	for _, item := range req.VideoTitleArr {
		if "" == item.VideoTitle {
			return gerror.New("视频标题不能为空")
		}
		if "" == item.Description {
			return gerror.New("视频简介不能为空")
		}
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return gerror.New("语言类型错误")
		}
		one := entity.VideoLanguages{
			LanguageId:  gconv.Uint(item.LanguageType),
			Title:       item.VideoTitle,
			Description: item.Description,
			CreateTime:  currentTime,
			UpdateTime:  currentTime,
		}
		videoLanguageData = append(videoLanguageData, one)
	}
	// 写入表
	err = dao.Videos.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		_, err1 = dao.Videos.Ctx(ctx).Where(dao.Videos.Columns().Id, req.Id).Data(videoData).Update()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.VideoLanguages.Ctx(ctx).Where(dao.VideoLanguages.Columns().VideoId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		for key := range videoLanguageData {
			videoLanguageData[key].VideoId = gconv.Uint(req.Id)
		}
		_, err1 = dao.VideoLanguages.Ctx(ctx).Data(videoLanguageData).Insert()
		return err1
	})
	return err
}

// 详情
func (s *sVideo) Info(ctx context.Context, req *v1.VideoInfoReq) (out *v1.VideoInfoRes, err error) {
	out = new(v1.VideoInfoRes)
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("参数错误")
	}
	var video entity.Videos
	err = dao.Videos.Ctx(ctx).Where(dao.Videos.Columns().Id, req.Id).Scan(&video)
	if err != nil {
		return out, err
	}
	if 0 >= video.Id {
		return out, gerror.New("视频不存在")
	}
	// 获取多语言列表
	var videoLanguageList []entity.VideoLanguages
	err = dao.VideoLanguages.Ctx(ctx).Where(dao.VideoLanguages.Columns().VideoId, req.Id).Scan(&videoLanguageList)
	if err != nil {
		return out, err
	}
	var videoTitleArr = make([]v1.VideoTitleItem, 0)
	for _, v := range videoLanguageList {
		videoTitleArr = append(videoTitleArr, v1.VideoTitleItem{
			LanguageType: gconv.Int(v.LanguageId),
			VideoTitle:   v.Title,
			Description:  v.Description,
		})
	}
	// 将int转成日期格式
	var publishTime string
	if 0 < video.PublishTime {
		publishTime = time.Unix(gconv.Int64(video.PublishTime/1000), 0).Format("2006-01-02 15:04:05")
	}
	out = &v1.VideoInfoRes{
		Video: v1.Video{
			Author:        video.Author,
			CategoryId:    video.CategoryId,
			PublishTime:   publishTime,
			CoverImgs:     video.VideoCoverUrl,
			VideoTitleArr: videoTitleArr,
			IsRecommend:   video.IsRecommended,
			VideoUrl:      video.VideoUrl,
		},
	}
	return
}

// 列表
func (s *sVideo) List(ctx context.Context, req *v1.VideoListReq) (out *v1.VideoListRes, err error) {
	out = new(v1.VideoListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	orm := dao.Videos.Ctx(ctx).Where(dao.Videos.Columns().DeleteTime, consts.Zero)
	if "" != req.VideoName {
		type videoLanguage struct {
			VideoId int `json:"video_id"`
		}
		var videoLanguageList []videoLanguage
		err = dao.VideoLanguages.Ctx(ctx).
			WhereLike(dao.VideoLanguages.Columns().Title, "%"+req.VideoName+"%").
			Where(dao.VideoCategoryLanguages.Columns().DeleteTime, consts.Zero).
			Scan(&videoLanguageList)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return
		}
		videoIds := gutil.ListItemValuesUnique(videoLanguageList, "VideoId")
		if 0 < len(videoIds) {
			orm = orm.Where(dao.VideoCategories.Columns().Id, videoIds)
		}
	}
	// 状态
	if -1 != req.VideoStatus {
		orm = orm.Where(dao.Videos.Columns().PublishState, req.VideoStatus)
	}
	// 分类
	if -1 != req.CategoryId {
		orm = orm.Where(dao.Videos.Columns().CategoryId, req.CategoryId)
	}
	// 是否推荐
	if -1 != req.IsRecommend {
		orm = orm.Where(dao.Videos.Columns().IsRecommended, req.IsRecommend)
	}
	// 草稿箱
	if consts.One == req.IsDraft {
		orm = orm.Where(dao.Videos.Columns().IsDraft, req.IsDraft)
	}
	// 创建时间
	if "" != req.CreateTimeBegin && "" != req.CreateTimeEnd {
		// 转成时间戳
		createTimeBegin := gtime.NewFromStr(req.CreateTimeBegin).TimestampStr()
		createTimeEnd := gtime.NewFromStr(req.CreateTimeEnd).TimestampStr()
		// 判断时间范围是否合法
		if createTimeBegin > createTimeEnd {
			return nil, gerror.New("开始时间不能大于结束时间")
		}
		orm = orm.WhereBetween(dao.Videos.Columns().CreateTime, createTimeBegin, createTimeEnd)
	}
	// 更新时间
	if "" != req.UpdateTimeBegin && "" != req.UpdateTimeEnd {
		// 转成时间戳
		updateTimeBegin := gtime.NewFromStr(req.UpdateTimeBegin).TimestampStr()
		updateTimeEnd := gtime.NewFromStr(req.CreateTimeEnd).TimestampStr()
		// 判断时间范围是否合法
		if updateTimeBegin > updateTimeEnd {
			return nil, gerror.New("开始时间不能大于结束时间")
		}
		orm = orm.WhereBetween(dao.Videos.Columns().CreateTime, updateTimeBegin, updateTimeEnd)
	}
	// 获取总数
	total, err := orm.Count()
	if err != nil {
		return out, gerror.Wrap(err, "获取总数失败")
	}
	if total == 0 {
		out.List = []v1.VideoListItem{}
		return out, nil
	}
	out.Total = total
	// 发布时间倒序
	orm = orm.OrderDesc(dao.Videos.Columns().PublishTime)
	var videoList []*entity.Videos
	err = orm.Page(req.Current, req.PageSize).Scan(&videoList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		out.List = []v1.VideoListItem{}
		return out, err
	}
	// 提取视频id
	videoIds := gutil.ListItemValuesUnique(videoList, "Id")
	// 提取视频分类id
	categoryIds := gutil.ListItemValuesUnique(videoList, "CategoryId")
	// 查询文章分类
	type category struct {
		CategoryId int    `json:"category_id"`
		LanguageId int    `json:"language_id"`
		Name       string `json:"name"`
	}
	var categoryList []*category
	err = dao.VideoCategoryLanguages.Ctx(ctx).WhereIn(dao.VideoCategoryLanguages.Columns().CategoryId, categoryIds).Scan(&categoryList)
	if err != nil {
		return out, err
	}
	var categoryMap = make(map[int]map[int]string)
	for _, v := range categoryList {
		if _, ok := categoryMap[v.CategoryId]; !ok {
			categoryMap[v.CategoryId] = make(map[int]string)
		}
		categoryMap[v.CategoryId][v.LanguageId] = v.Name
	}
	// 查询文章名称
	type videoLanguage struct {
		VideoId    int    `json:"video_id"`
		LanguageId int    `json:"language_id"`
		Title      string `json:"title"`
	}
	var videoLanguageList []*videoLanguage
	err = dao.VideoLanguages.Ctx(ctx).WhereIn(dao.VideoLanguages.Columns().VideoId, videoIds).Scan(&videoLanguageList)
	if err != nil {
		return
	}
	videoLanguageListMap := make(map[int]map[int]string)
	for _, v := range videoLanguageList {
		if _, ok := videoLanguageListMap[v.VideoId]; !ok {
			videoLanguageListMap[v.VideoId] = make(map[int]string)
		}
		videoLanguageListMap[v.VideoId][v.LanguageId] = v.Title
	}
	// 获取语言
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))
	// 计算序号开始值
	serialNumber := (req.Current-1)*req.PageSize + 1
	// 组装数据
	for _, v := range videoList {
		// 将int转成日期格式，时间戳是13位，需要除1000
		var createTime, publishTime string
		if 0 < v.PublishTime {
			publishTime = time.Unix(gconv.Int64(v.PublishTime)/1000, 0).Format("2006-01-02 15:04:05")
		}
		if 0 < v.CreateTime {
			createTime = time.Unix(gconv.Int64(v.CreateTime)/1000, 0).Format("2006-01-02 15:04:05")
		}
		video := v1.VideoListItem{
			Id:              v.Id,
			CategoryId:      v.CategoryId,
			VideoStatus:     gconv.Int(v.PublishState),
			VideoStatusText: consts.GetVideoStatusText(gconv.Int(v.PublishState)),
			IsRecommend:     gconv.Int(v.IsRecommended),
			CreateTime:      createTime,
			PublishTime:     publishTime,
			ViewNum:         gconv.Int(v.ViewCount),
			ShareNum:        gconv.Int(v.ShareCount),
			CollectNum:      gconv.Int(v.CollectCount),
		}
		_, ok := videoLanguageListMap[gconv.Int(v.Id)][consts.Zero]
		var IsZh, IsEn, IsId int
		if ok {
			IsZh = consts.One
		}
		_, ok = videoLanguageListMap[gconv.Int(v.Id)][consts.One]
		if ok {
			IsEn = consts.One
		}
		_, ok = videoLanguageListMap[gconv.Int(v.Id)][consts.Two]
		if ok {
			IsId = consts.One
		}
		// 支持的语言
		video.LanguageArr = append(video.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.One,
			LanguageTypeText: "ZH",
			IsSupport:        IsZh,
		})
		video.LanguageArr = append(video.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Two,
			LanguageTypeText: "EN",
			IsSupport:        IsEn,
		})
		video.LanguageArr = append(video.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Three,
			LanguageTypeText: "ID",
			IsSupport:        IsId,
		})
		// 展示当前语言对应的文章名称
		video.VideoName = videoLanguageListMap[gconv.Int(v.Id)][currentLang]
		video.CategoryName = categoryMap[gconv.Int(v.CategoryId)][currentLang]
		video.IsRecommendText = consts.GetArticleIsRecommendText(gconv.Int(v.IsRecommended))
		video.SerialNum = serialNumber
		out.List = append(out.List, video)
		serialNumber++
	}
	return out, nil
}

// 删除
func (s *sVideo) Delete(ctx context.Context, req *v1.VideoDeleteReq) error {
	// 校验参数
	if 1 > len(req.Ids) {
		return gerror.New("请选择要删除的视频")
	}
	currentTime := time.Now().UnixMilli()
	err := dao.Videos.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 把状态改称删除
		var err1 error
		_, err1 = dao.Videos.Ctx(ctx).
			WhereIn(dao.Videos.Columns().Id, req.Ids).
			Data(g.Map{
				dao.Videos.Columns().UpdateTime: currentTime,
				dao.Videos.Columns().DeleteTime: currentTime,
			}).
			Update()
		if err1 != nil {
			return err1
		}
		_, err1 = dao.VideoLanguages.Ctx(ctx).
			Where(dao.VideoLanguages.Columns().VideoId, req.Ids).
			Data(g.Map{
				dao.VideoLanguages.Columns().UpdateTime: currentTime,
				dao.VideoLanguages.Columns().DeleteTime: currentTime,
			}).
			Update()
		return err1
	})
	return err
}

// 上线
func (s *sVideo) SetOnline(ctx context.Context, req *v1.VideoSetOnlineReq) error {
	// 校验参数
	if 1 > len(req.Ids) {
		return gerror.New("请选择要下线的视频")
	}
	_, err := dao.Videos.Ctx(ctx).WhereIn(dao.Videos.Columns().Id, req.Ids).Data(g.Map{
		dao.Videos.Columns().PublishState: consts.One,
		dao.Videos.Columns().IsDraft:      consts.Zero,
		dao.Videos.Columns().UpdateTime:   time.Now().UnixMilli(),
	}).Update()
	return err
}

// 下线
func (s *sVideo) SetOffline(ctx context.Context, req *v1.VideoSetOfflineReq) error {
	// 校验参数
	if 1 > len(req.Ids) {
		return gerror.New("请选择要下线的视频")
	}
	_, err := dao.Videos.Ctx(ctx).WhereIn(dao.Videos.Columns().Id, req.Ids).Data(g.Map{
		dao.Videos.Columns().PublishState: consts.Two,
		dao.Videos.Columns().IsDraft:      consts.Zero,
		dao.Videos.Columns().UpdateTime:   time.Now().UnixMilli(),
		dao.Videos.Columns().PublishTime:  time.Now().UnixMilli(),
	}).Update()
	return err
}
