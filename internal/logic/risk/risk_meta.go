package risk

import (
	"context"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/consts"
	"time"

	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
)

func (s *sRisk) RiskTabList(ctx context.Context) (tabs []entity.RiskControlTab, err error) {
	tabs = make([]entity.RiskControlTab, 0)

	cl := dao.RiskControlTab.Columns()
	err = dao.RiskControlTab.Ctx(ctx).Where(cl.DeleteTime, 0).Scan(&tabs)

	return
}

func (s *sRisk) RiskTabEditSwitch(ctx context.Context, tabType uint, isOpen int) (err error) {
	cl := dao.RiskControlTab.Columns()
	attrs := g.Map{
		cl.IsOpen:     isOpen,
		cl.UpdateTime: time.Now().UnixMilli(),
	}
	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		attrs[cl.UpdateAccount] = admin.Account
	}

	var oldData entity.RiskControlTab
	err = dao.RiskControlTab.Ctx(ctx).Where(cl.TabType, tabType).Scan(&oldData)
	if oldData.IsList != 1 {
		return gerror.New("Not a list type. Editing is prohibited!")
	}

	_, err = dao.RiskControlTab.Ctx(ctx).Cache(gdb.CacheOption{
		Duration: -1,
		Name:     consts.RiskRiskTabList,
		Force:    false,
	}).Where(cl.TabType, tabType).Update(attrs)
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			ModifyItem:      oldData.TabName + "：状态",
			OldRecordOrAttr: ConvertOpen(oldData.IsOpen),
			NewRecordOrAttr: ConvertOpen(isOpen),
		})
	}

	s.deleteCache(ctx)
	return
}

func (s *sRisk) RiskTabEditMetas(ctx context.Context, tabType uint, metas string) (err error) {
	cl := dao.RiskControlTab.Columns()
	attrs := g.Map{
		cl.Metas:      metas,
		cl.UpdateTime: time.Now().UnixMilli(),
	}
	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		attrs[cl.UpdateAccount] = admin.Account
	}

	var oldData entity.RiskControlTab
	err = dao.RiskControlTab.Ctx(ctx).Where(cl.TabType, tabType).Scan(&oldData)
	if oldData.IsList != 2 {
		return gerror.New("Not a metas type. Editing is prohibited!")
	}

	_, err = dao.RiskControlTab.Ctx(ctx).Cache(gdb.CacheOption{
		Duration: -1,
		Name:     consts.RiskRiskTabList,
		Force:    false,
	}).Where(cl.TabType, tabType).Update(attrs)
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			ModifyItem:      oldData.TabName,
			OldRecordOrAttr: gvar.New(oldData.Metas).MapStrStr(),
			NewRecordOrAttr: gvar.New(metas).MapStrStr(),
		})
	}

	s.deleteCache(ctx)
	return
}
