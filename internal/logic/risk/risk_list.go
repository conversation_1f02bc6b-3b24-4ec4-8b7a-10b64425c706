package risk

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/consts"
	"time"

	v1 "gtcms/api/v1"

	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	do "gtcms/internal/model/do/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
)

type (
	sRisk struct{}
)

func init() {
	service.RegisterRisk(New())
}

func New() service.IRisk {
	return &sRisk{}
}

func (s *sRisk) RiskContentList(ctx context.Context, tabType int, key string, isOpen int, startTime, endTime int64, page *v1.ListReq) (labels []entity.RiskControlContent, listRes v1.ListRes, err error) {
	labels = make([]entity.RiskControlContent, 0)
	listRes = v1.ListRes{
		Current: page.Current,
	}
	var del int64 = 0
	cl := dao.RiskControlContent.Columns()
	lM := dao.RiskControlContent.Ctx(ctx).Where(cl.TabType, tabType).Where(cl.IsOpen, isOpen).Where(cl.DeleteTime, &del).OmitEmptyWhere()
	if len(key) > 0 {
		lM = lM.WhereLike(cl.Content, "%"+key+"%")
	}

	if startTime > 0 {
		lM = lM.WhereGTE(cl.CreateTime, startTime)
	}
	if endTime > 0 {
		lM = lM.WhereLTE(cl.CreateTime, endTime)
	}

	listRes.Total, err = lM.Count()
	if err != nil {
		return
	}
	if page.OrderBy != "" {
		lM = lM.Order(page.OrderBy)
	} else {
		lM = lM.OrderAsc(cl.Id)
	}
	err = lM.Page(page.Current, page.PageSize).Scan(&labels)
	return
}

func (s *sRisk) RiskContentEdit(ctx context.Context, id uint, do *do.RiskControlContent) (err error) {
	tab := &entity.RiskControlTab{}
	clt := dao.RiskControlTab.Columns()
	err = dao.RiskControlTab.Ctx(ctx).Where(clt.TabType, do.TabType).Scan(tab)
	if err != nil {
		return
	}
	if tab.IsList != 1 {
		return gerror.New("Not a list type. Editing is prohibited!")
	}

	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		do.UpdateAccount = admin.Account
	}
	do.UpdateTime = time.Now().UnixMilli()

	oldData := &entity.RiskControlContent{}
	err = dao.RiskControlContent.Ctx(ctx).WherePri(id).Scan(&oldData)

	cl := dao.RiskControlContent.Columns()
	_, err = dao.RiskControlContent.Ctx(ctx).Cache(gdb.CacheOption{
		Duration: -1,
		Name:     consts.RiskRiskContentList,
		Force:    false,
	}).Where(cl.Id, id).Data(do).Update()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			ModifyItem:      tab.TabName,
			OldRecordOrAttr: oldData,
			NewRecordOrAttr: do,
		})
	}
	s.deleteCache(ctx)
	return
}

func (s *sRisk) deleteCache(ctx context.Context) {
	g.Go(ctx, func(ctx context.Context) {
		// 延迟一秒钟
		<-time.After(1 * time.Second)

		service.DbCache().PublishUpdate(ctx, model.MemoryUpdateMessage{
			ClassId: consts.RiskUpdate,
			Key:     "",
		})
	}, nil)
}

func (s *sRisk) RiskContentAdd(ctx context.Context, do *do.RiskControlContent) (err error) {
	tab := &entity.RiskControlTab{}
	clt := dao.RiskControlTab.Columns()
	err = dao.RiskControlTab.Ctx(ctx).Where(clt.TabType, do.TabType).Scan(tab)
	if err != nil {
		return
	}
	if tab.Id == 0 {
		return gerror.New(fmt.Sprintf("TabType=%d not exist!", do.TabType))
	}
	if tab.IsList != 1 {
		return gerror.New("Not a list type. Add is prohibited!")
	}

	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		do.CreateAccount = admin.Account
	}
	do.CreateTime = time.Now().UnixMilli()
	_, err = dao.RiskControlContent.Ctx(ctx).Cache(gdb.CacheOption{
		Duration: -1,
		Name:     consts.RiskRiskContentList,
		Force:    false,
	}).Data(do).Insert()

	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			ModifyItem:      tab.TabName,
			NewRecordOrAttr: do,
		})
	}
	s.deleteCache(ctx)
	return
}

func (s *sRisk) RiskContentDelete(ctx context.Context, id uint) (err error) {
	oldData := make([]*entity.RiskControlContent, 0)
	cl := dao.RiskControlContent.Columns()
	err = dao.RiskControlContent.Ctx(ctx).Where(cl.Id, id).Scan(&oldData)
	if len(oldData) == 0 {
		return
	}
	tabType := oldData[0].TabType

	tab := &entity.RiskControlTab{}
	clt := dao.RiskControlTab.Columns()
	err = dao.RiskControlTab.Ctx(ctx).Where(clt.TabType, tabType).Scan(tab)
	if err != nil {
		return
	}
	if tab.IsList != 1 {
		return gerror.New("Not a list type. Delete is prohibited!")
	}

	admin, _ := service.Utility().GetSelf(ctx)
	now := time.Now().UnixMilli()
	lb := &do.RiskControlContent{
		DeleteTime: now,
		UpdateTime: now,
	}
	if admin != nil {
		lb.UpdateAccount = admin.Account
	}

	_, err = dao.RiskControlContent.Ctx(ctx).Cache(gdb.CacheOption{
		Duration: -1,
		Name:     consts.RiskRiskContentList,
		Force:    false,
	}).Where(cl.Id, id).Data(lb).Update()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			ModifyType:      model.AdminAdjustTypeDel,
			ModifyItem:      tab.TabName,
			OldRecordOrAttr: oldData,
		})
	}
	s.deleteCache(ctx)
	return
}
