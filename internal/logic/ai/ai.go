package ai

import (
	"context"
	v1 "gtcms/api/v1"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"
	"gtcms/internal/service"
	"gtcms/internal/utils/ai_article"
	"strings"
	"sync"
	"time"
)

type sAi struct {
	mu sync.Mutex
}

func init() {
	service.RegisterAi(New())
}

func New() *sAi {
	s := &sAi{}
	return s
}

// 通过接口生成文章
func (s *sAi) Gen(ctx context.Context, req *v1.AiGenReq) (res *v1.AiGenRes, err error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	res = &v1.AiGenRes{}

	var rs *v1.SelectorConfigListRes
	rs, err = service.SelectorConfig().List(ctx, &v1.SelectorConfigListReq{
		SelectType: "ai-api",
	})
	if err != nil {
		return
	}
	aiApi := ""
	if len(rs.List) > 0 {
		aiApi = rs.List[0].Title
	}

	for _, v := range req.Items {
		if v.Describe == "" {
			return
		}
		var newsList []*model.CrawlerNewsRaw
		for _, vv := range v.Keywords {
			describe := strings.ReplaceAll(v.Describe, "{keyword}", vv)
			art := ai_article.Generate(aiApi, describe)

			news := &model.CrawlerNewsRaw{
				CrawlerNewsRaw: entity.CrawlerNewsRaw{
					Title:      vv,
					CreateTime: time.Now().UnixMilli(),
					UpdateTime: time.Now().UnixMilli(),
					Content:    art,
				},
			}
			newsList = append(newsList, news)
		}
		if len(newsList) > 0 {
			//err = service.News().AutoPublish(ctx, newsList, true, &v1.AiGenItem{
			//	ColumnId:    v.ColumnId,
			//	SiteGroupId: v.SiteGroupId,
			//})
			//if err != nil {
			//	return
			//}
			//res.Num += len(newsList)
		}
	}

	//self, err := service.Utility().GetSelf(ctx)
	//if err != nil {
	//	return
	//}
	//aiLog := &entity.AiLog{
	//	Creater:    gconv.Int(self.Id),
	//	CreateTime: time.Now().UnixMilli(),
	//	Content:    gconv.String(req.Items),
	//}
	//_, err = dao.AiLog.Ctx(ctx).Insert(aiLog)
	//if err != nil {
	//	return
	//}
	return
}
