package ad

import (
	"context"
	"encoding/json"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "gtcms/api/v1"

	"gtcms/internal/service"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

type sAd struct {
	nginxDomainList []map[string]string
}

func init() {
	service.RegisterAd(New())

}

func New() *sAd {
	s := &sAd{}
	//s.getNginxLogDomains()
	return s
}
func (s *sAd) getNginxLogDomains() {
	ctx := gctx.New()
	cfg := g.Cfg()
	var out []map[string]string
	if cfg == nil {
		return
	}
	confList := cfg.MustGet(ctx, "nginx-log-domains").Strings()
	for _, domain := range confList {
		rt, _ := s.NginxWebOne(domain)
		out = append(out, map[string]string{
			"url":  domain,
			"name": rt.Websites[0].Name,
			"id":   gconv.String(rt.Websites[0].Id),
		})
	}
	s.nginxDomainList = out
	return
}

func (s *sAd) NginxWebsList(ctx context.Context, reqp *v1.NginxWebsReq) (res *v1.NginxWebsRes, err error) {

	req, err := http.NewRequest("GET", s.nginxDomainList[0]["url"]+"/api/websites", nil)
	if err != nil {
		return res, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return res, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return res, err
	}
	jsonString := strings.TrimPrefix(string(body), "Response: ")
	// 将 JSON 解码到 map 中
	err = json.Unmarshal([]byte(jsonString), &res)
	if err != nil {
		return res, err
	}
	return res, nil
}

func (s *sAd) NginxWebOne(oneUrl string) (res *v1.NginxWebsRes, err error) {

	req, err := http.NewRequest("GET", oneUrl+"/api/websites", nil)
	if err != nil {
		return res, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return res, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return res, err
	}
	jsonString := strings.TrimPrefix(string(body), "Response: ")
	// 将 JSON 解码到 map 中
	err = json.Unmarshal([]byte(jsonString), &res)
	if err != nil {
		return res, err
	}
	return res, nil
}
func (s *sAd) NginxOverallList(ctx context.Context, reqp *v1.NginxOverallReq) (res *v1.NginxOverallRes, err error) {

	res = new(v1.NginxOverallRes)
	for _, one := range s.nginxDomainList {
		reqp.Id = one["id"]
		rt, err := s.getOneNginxOverall(one["url"], reqp)
		if err != nil {
			continue
		}
		//累加
		res.Pv += rt.Pv
		res.Uv += rt.Uv
		res.Traffic += rt.Traffic
	}
	return res, nil
}

func (s *sAd) getOneNginxOverall(url string, reqp *v1.NginxOverallReq) (res *v1.NginxOverallRes, err error) {
	req, err := http.NewRequest("GET", url+"/api/stats/overall?id="+reqp.Id+"&timeRange="+reqp.TimeRange, nil)
	if err != nil {
		return res, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return res, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return res, err
	}
	jsonString := strings.TrimPrefix(string(body), "Response: ")
	// 将 JSON 解码到 map 中
	err = json.Unmarshal([]byte(jsonString), &res)
	if err != nil {
		return res, err
	}
	return res, nil
}

func (s *sAd) NginxSeriesList(ctx context.Context, reqp *v1.NginxSeriesReq) (res *v1.NginxSeriesRes, err error) {
	res = new(v1.NginxSeriesRes)
	for key, one := range s.nginxDomainList {
		reqp.Id = one["id"]
		rt, err := s.getOneNginxSeries(one["url"], reqp)
		if err != nil {
			continue
		}
		if key == 0 {
			res.Labels = append(res.Labels, rt.Labels...)
			res.Visitors = append(res.Visitors, rt.Visitors...)
			res.Pageviews = append(res.Pageviews, rt.Pageviews...)
			res.PvMinusUv = append(res.PvMinusUv, rt.PvMinusUv...)
		} else {
			for i := 0; i < len(rt.Visitors); i++ {
				res.Visitors[i] += rt.Visitors[i]
			}
			for i := 0; i < len(rt.Pageviews); i++ {
				res.Pageviews[i] += rt.Pageviews[i]
			}
			for i := 0; i < len(rt.PvMinusUv); i++ {
				res.PvMinusUv[i] += rt.PvMinusUv[i]
			}
		}
	}
	return res, nil
}

func (s *sAd) getOneNginxSeries(url string, reqp *v1.NginxSeriesReq) (res *v1.NginxSeriesRes, err error) {
	req, err := http.NewRequest("GET", url+"/api/stats/timeseries?id="+reqp.Id+"&timeRange="+reqp.TimeRange+"&viewType="+reqp.ViewType, nil)

	if err != nil {
		return res, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return res, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return res, err
	}
	jsonString := strings.TrimPrefix(string(body), "Response: ")
	// 将 JSON 解码到 map 中
	err = json.Unmarshal([]byte(jsonString), &res)
	if err != nil {
		return res, err
	}
	return res, nil
}

// nginx access record
func (s *sAd) NginxList(ctx context.Context, req *v1.NginxReq) (res *v1.NginxRes, err error) {
	//3 5 10 30 60 and input
	var records int
	//返回5分钟前的那一分钟的时间戳，不需要到秒
	fiveMinutesAgo := time.Now().Add(-time.Duration(req.InputMinute) * time.Minute)
	minuteTimestamp := time.Date(
		fiveMinutesAgo.Year(),
		fiveMinutesAgo.Month(),
		fiveMinutesAgo.Day(),
		fiveMinutesAgo.Hour(),
		fiveMinutesAgo.Minute(),
		0, 0, fiveMinutesAgo.Location(),
	)
	// 获取该时间的 Unix 时间戳（秒）
	unixTimestamp := minuteTimestamp.Unix()
	//unixTimestamp 到当前时间的时间戳的分钟数
	minutes := int((time.Now().Unix() - unixTimestamp) / 60)
	for i := 0; i <= minutes; i++ {
		redisKey := gconv.String(unixTimestamp + int64(i)*60)
		value, _ := g.Redis().Get(ctx, redisKey)
		if value != nil {
			records += gconv.Int(value)
		}
	}
	res = new(v1.NginxRes)
	res.Records = records
	return
}

//
//func (s *sAd) ClicksList(ctx context.Context, req *v1.AdClicksReq) (res *v1.AdClicksRes, err error) {
//	res = new(v1.AdClicksRes)
//	res.Current = req.Current
//	res.List = make([]v1.AdClicksItem, 0)
//	clickInfo := make([]entity.AdClickRecord, 0)
//	// 获取上个月的第一天 查询所有符合条件的数据
//	lastMonth := time.Now().AddDate(0, -1, 0)
//	formattedDate := time.Date(lastMonth.Year(), lastMonth.Month(), 1, 0, 0, 0, 0, lastMonth.Location()).Format("2006-01-02")
//
//	m := dao.AdClickRecord.Ctx(ctx)
//	m = m.WhereGTE(dao.AdClickRecord.Columns().RecordDay, formattedDate)
//	err = m.Scan(&clickInfo)
//	if err != nil {
//		return nil, err
//	}
//	if len(clickInfo) == 0 {
//		return
//	}
//	adIdList := make([]int, 0)
//	adMap := make(map[int][]entity.AdClickRecord)
//	for _, v := range clickInfo {
//		adIdList = append(adIdList, v.AdId)
//		adMap[v.AdId] = append(adMap[v.AdId], v)
//
//	}
//	//复用之前的数据返回结构
//	//分组站点
//	adListInfo := make([]v1.AdItem, 0)
//	err = dao.Ad.Ctx(ctx).LeftJoin(dao.SiteGroup.Table()+" on "+dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Id+"= "+dao.Ad.Table()+"."+dao.Ad.Columns().BelongId).Handler(utility.CreatorFilter).Where(dao.Ad.Columns().DeleteTime, 0).Where(dao.Ad.Columns().Belong, 1).WhereIn(dao.Ad.Columns().Id, adIdList).Fields(
//		dao.Ad.Table()+".*",
//		dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Name+" as groupName",
//	).Scan(&adListInfo)
//
//	if err != nil {
//		return res, err
//	}
//	//clickInfo 按belong_id 分组
//	grouped := make(map[uint][]v1.AdItem)
//	for _, record := range adListInfo {
//		grouped[record.BelongId] = append(grouped[record.BelongId], record)
//	}
//	for key, grp := range grouped {
//		item := v1.AdClicksItem{}
//		item.Id = int(key)
//		item.Name = grouped[key][0].GroupName
//		for _, v := range grp {
//			value, exists := adMap[int(v.Id)]
//			if exists {
//				item.Today, item.Yesterday, item.Week, item.LastWeek, item.Month, item.LastMonth = getDataByTime(value)
//			}
//		}
//		res.List = append(res.List, item)
//	}
//	if len(res.List) == 0 {
//		return
//	}
//	//分页组装 未使用数据库分页
//	// 按时间倒序
//	sort.Slice(res.List, func(i, j int) bool {
//		return res.List[i].Id > res.List[j].Id
//	})
//	res.Total = len(res.List)
//	res.List, _, _, err = paginate(res.List, req.Current, req.PageSize)
//	if err != nil {
//		res.List = make([]v1.AdClicksItem, 0)
//		return
//	}
//	return
//}
//func paginate(data []v1.AdClicksItem, page, pageSize int) ([]v1.AdClicksItem, int, int, error) {
//	// 获取总数据条数
//	totalItems := len(data)
//	// 计算总页数
//	totalPages := int(math.Ceil(float64(totalItems) / float64(pageSize)))
//
//	// 如果页码小于1或大于总页数，返回错误
//	if page <= 0 || page > totalPages {
//		return nil, 0, 0, fmt.Errorf("invalid page number: %d, valid range is 1 to %d", page, totalPages)
//	}
//
//	// 计算当前页的起始索引和结束索引
//	startIndex := (page - 1) * pageSize
//	endIndex := startIndex + pageSize
//	// 防止 endIndex 超过数据的长度
//	if endIndex > totalItems {
//		endIndex = totalItems
//	}
//	// 获取当前页的数据
//	pageData := data[startIndex:endIndex]
//	// 返回分页数据，当前页和总页数
//	return pageData, page, totalPages, nil
//}
//
//func getDataByTime(Records []entity.AdClickRecord) (int, int, int, int, int, int) {
//	var Today, Yesterday, Week, LastWeek, Month, LastMonth int
//	for _, v := range Records {
//		todayStart := time.Now().Format("2006-01-02")
//		//今天开始时间
//		if v.RecordDay.Format("Y-m-d") >= todayStart {
//			Today += v.Clicks
//		}
//		yesterdayStart := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
//		//昨天开始时间
//		if v.RecordDay.Format("Y-m-d") >= yesterdayStart && v.RecordDay.Format("Y-m-d") < todayStart {
//			Yesterday += v.Clicks
//		}
//		weekday := time.Now().Weekday()
//		daysToMonday := int(weekday) - int(time.Monday)
//		if daysToMonday < 0 {
//			daysToMonday += 7 // 如果是周日，则需要减去 6 天
//		}
//		monday := time.Now().AddDate(0, 0, -daysToMonday)
//		firstDayOfWeek := monday.Format("2006-01-02")
//		//本周开始时间
//		if v.RecordDay.Format("Y-m-d") >= firstDayOfWeek {
//			Week += v.Clicks
//		}
//		weekday = time.Now().Weekday()
//		daysToLastMonday := int(weekday) + 6 // 加6是因为我们需要推回到上周的周一
//		lastMonday := time.Now().AddDate(0, 0, -daysToLastMonday)
//		lastSunday := lastMonday.AddDate(0, 0, 6)
//		startOfLastWeek := lastMonday.Format("2006-01-02")
//		endOfLastWeek := lastSunday.Format("2006-01-02")
//		//上周开始时间和结束时间
//		if v.RecordDay.Format("Y-m-d") >= startOfLastWeek && v.RecordDay.Format("Y-m-d") <= endOfLastWeek {
//			LastWeek += v.Clicks
//		}
//
//		now := time.Now()
//		firstDayOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
//		startOfMonth := firstDayOfMonth.Format("2006-01-02")
//		//本月开始时间
//		if v.RecordDay.Format("Y-m-d") >= startOfMonth {
//			Month += v.Clicks
//		}
//
//		lastMonth := time.Now().AddDate(0, -1, 0)
//		lastDayOfLastMonth := time.Date(lastMonth.Year(), lastMonth.Month()+1, 0, 23, 59, 59, 999999999, lastMonth.Location())
//		endOfLastMonth := lastDayOfLastMonth.Format("2006-01-02")
//		//上月结束时间(是从上月开始时间查询的所有数据)
//		if v.RecordDay.Format("Y-m-d") <= endOfLastMonth {
//			LastMonth += v.Clicks
//		}
//	}
//	return Today, Yesterday, Week, LastWeek, Month, LastMonth
//}
//
//func (s *sAd) ClicksExport(ctx context.Context, req *v1.AdClicksExportReq) (res *v1.EmptyDataRes, err error) {
//	list := make([]v1.AdClicksExportItem, 0)
//
//	clickInfo := make([]entity.AdClickRecord, 0)
//	m := dao.AdClickRecord.Ctx(ctx)
//
//	if req.StartTime > 0 && req.EndTime > 0 {
//		startTime := time.Unix(req.StartTime/1000, (req.StartTime%1000)*1000000).Format("2006-01-02")
//		endTime := time.Unix(req.EndTime/1000, (req.EndTime%1000)*1000000).Format("2006-01-02")
//		m = m.WhereGTE(dao.AdClickRecord.Columns().RecordDay, startTime).WhereLTE(dao.AdClickRecord.Columns().RecordDay, endTime)
//	} else {
//		return nil, gerror.New("未选择查询起止时间")
//	}
//	err = m.Scan(&clickInfo)
//	if err != nil {
//		return nil, err
//	}
//	if len(clickInfo) == 0 {
//		return
//	}
//	adIdList := make([]int, 0)
//	adMap := make(map[int][]entity.AdClickRecord)
//	for _, v := range clickInfo {
//		adIdList = append(adIdList, v.AdId)
//		adMap[v.AdId] = append(adMap[v.AdId], v)
//
//	}
//	//复用之前的数据返回结构
//	//分组站点
//	adListInfo := make([]v1.AdItem, 0)
//	err = dao.Ad.Ctx(ctx).LeftJoin(dao.SiteGroup.Table()+" on "+dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Id+"= "+dao.Ad.Table()+"."+dao.Ad.Columns().BelongId).Handler(utility.CreatorFilter).Where(dao.Ad.Columns().DeleteTime, 0).Where(dao.Ad.Columns().Belong, 1).WhereIn(dao.Ad.Columns().Id, adIdList).Fields(
//		dao.Ad.Table()+".*",
//		dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Name+" as groupName",
//	).Scan(&adListInfo)
//
//	if err != nil {
//		return res, err
//	}
//	//clickInfo 按belong_id 分组
//	grouped := make(map[uint][]v1.AdItem)
//	for _, record := range adListInfo {
//		grouped[record.BelongId] = append(grouped[record.BelongId], record)
//	}
//
//	//组织返回数据
//	for key, grp := range grouped {
//		item := v1.AdClicksExportItem{}
//		item.Id = int(key)
//		item.Name = grouped[key][0].GroupName
//
//		for _, v := range grp {
//			value, exists := adMap[int(v.Id)]
//			if exists {
//				item.Clicks = calculateClicks(value)
//			}
//		}
//		list = append(list, item)
//	}
//	if len(list) == 0 {
//		return
//	}
//	if err = service.FileProcessor().Export2Excel(ctx, &list); err != nil {
//		return
//	}
//	return
//}
//
//func calculateClicks(Records []entity.AdClickRecord) int {
//	var clicks int
//	for _, v := range Records {
//		clicks += v.Clicks
//	}
//	return clicks
//}
//
//func (s *sAd) List(ctx context.Context, req *v1.AdListReq) (res *v1.AdListRes, err error) {
//	res = new(v1.AdListRes)
//	res.Current = req.Current
//	res.List = make([]v1.AdItem, 0)
//
//	if req.Belong == 1 {
//
//		m := dao.Ad.Ctx(ctx).
//			LeftJoin(dao.SiteGroup.Table() + " on " + dao.SiteGroup.Table() + "." + dao.SiteGroup.Columns().Id + "= " + dao.Ad.Table() + "." + dao.Ad.Columns().BelongId).
//			LeftJoin(dao.VideoProduct.Table() + " ON " + dao.Ad.Table() + "." + dao.Ad.Columns().Url + "=" +
//				dao.VideoProduct.Table() + "." + dao.VideoProduct.Columns().Id).Handler(utility.CreatorFilter)
//
//		if req.Language != nil {
//			m = m.WhereLike(dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Language, utility.WhereLike(*req.Language))
//		}
//
//		err = m.Fields(
//			dao.Ad.Table()+".*",
//			dao.VideoProduct.Table()+"."+dao.VideoProduct.Columns().Url+" as urlName",
//		).Scan(&res.List)
//		if err != nil {
//			return res, err
//		}
//
//		groupAd := make(map[uint]v1.AdItem)
//		for _, item := range res.List {
//			groupAd[item.BelongId] = item
//		}
//
//		res.List = make([]v1.AdItem, 0)
//		groupList, _ := service.SiteGroup().List(ctx, &v1.SiteGroupListReq{})
//		for _, item := range groupList.List {
//			if v, ok := groupAd[item.Id]; ok {
//				v.BelongId = item.Id
//				v.GroupName = item.Name
//				v.Language = item.Language
//				res.List = append(res.List, v)
//			} else {
//				// 如果站点分组没有广告，默认创建一条广告
//				add, _ := service.Ad().Add(ctx, &v1.AdAddReq{
//					Type:       2,
//					Name:       "ad",
//					Url:        0,
//					ExpireTime: 0,
//					Belong:     1,
//					BelongId:   &item.Id,
//				})
//
//				res.List = append(res.List, v1.AdItem{
//					Ad: entity.Ad{
//						Id:       add.Id,
//						BelongId: item.Id,
//					},
//					GroupName: item.Name,
//					Language:  item.Language,
//				})
//			}
//		}
//
//		res.Total = groupList.Total
//	} else {
//		m := dao.Ad.Ctx(ctx).LeftJoin(dao.VideoProduct.Table()+" ON "+dao.Ad.Table()+"."+dao.Ad.Columns().Url+"="+
//			dao.VideoProduct.Table()+"."+dao.VideoProduct.Columns().Id).
//			Where(dao.Ad.Columns().Belong, req.Belong).Handler(utility.CreatorFilter)
//		if req.Name != nil {
//			m = m.WhereLike(dao.Ad.Columns().Name, utility.WhereLike(*req.Name))
//		}
//		if req.Type != nil {
//			m = m.Where(dao.Ad.Columns().Type, *req.Type)
//		}
//		if req.Belong != 0 {
//			m = m.Where(dao.Ad.Columns().BelongId, req.BelongId)
//		}
//		m = m.Where(dao.Ad.Columns().DeleteTime, 0)
//		total, err := m.Count()
//		if err != nil {
//			return res, err
//		}
//		if total < 1 {
//			return res, err
//		}
//		if len(req.OrderBy) > 0 {
//			m = m.Order(req.OrderBy)
//		} else {
//			m = m.OrderDesc(dao.Ad.Table() + "." + dao.Ad.Columns().Id) // 最新在前面
//		}
//
//		if req.Current > 0 && req.PageSize > 0 {
//			m = m.Page(req.Current, req.PageSize)
//			res.List = make([]v1.AdItem, 0, req.PageSize)
//		} else {
//			res.List = make([]v1.AdItem, 0, total)
//		}
//
//		err = m.Fields(
//			dao.Ad.Table()+".*",
//			dao.VideoProduct.Table()+"."+dao.VideoProduct.Columns().Url+" as urlName",
//		).Scan(&res.List)
//		if err != nil {
//			return res, err
//		}
//		res.Total = total
//	}
//
//	return
//}
//
//func (s *sAd) updateModel(ctx context.Context, belong int, belongId *uint) (m *gdb.Model) {
//	m = dao.Ad.Ctx(ctx)
//	name := ""
//	switch belong {
//	case 1:
//		name = consts.AdGroupList + gconv.String(belongId)
//	case 2:
//		name = consts.AdSiteList + gconv.String(belongId)
//	}
//	m = m.Cache(gdb.CacheOption{
//		Duration: -1,
//		Name:     name,
//		Force:    false, // nil也缓存
//	})
//	return
//}
//
//func (s *sAd) deleteCache(ctx context.Context) {
//	g.Go(ctx, func(ctx context.Context) {
//		// 延迟一秒钟
//		<-time.After(1 * time.Second)
//
//		// 执行缓存更新操作
//		service.DbCache().PublishUpdate(ctx, model.MemoryUpdateMessage{
//			ClassId: consts.AdUpdate,
//			Key:     "",
//		})
//	}, nil)
//}
//
//func (s *sAd) Add(ctx context.Context, req *v1.AdAddReq) (res *v1.AdAddRes, err error) {
//	res = new(v1.AdAddRes)
//
//	m := s.updateModel(ctx, req.Belong, req.BelongId)
//	m = m.Where(dao.Ad.Columns().Belong, req.Belong)
//	var entityData *entity.Ad
//	if err = gconv.Scan(req, &entityData); err != nil {
//		return
//	}
//	entityData.CreateTime = gtime.Now().UnixMilli()
//	self, err := service.Utility().GetSelf(ctx)
//	if err != nil {
//		return
//	}
//	entityData.Creater = self.Id
//
//	id, err := m.InsertAndGetId(entityData)
//	if err != nil {
//		return
//	}
//
//	res = &v1.AdAddRes{
//		Id: uint(id),
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: entityData,
//	})
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sAd) Edit(ctx context.Context, req *v1.AdEditReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	if req.Name != nil {
//		update[dao.Ad.Columns().Name] = req.Name
//	}
//	if req.Url != nil {
//		update[dao.Ad.Columns().Url] = req.Url
//	}
//	if req.ExpireTime != nil {
//		update[dao.Ad.Columns().ExpireTime] = req.ExpireTime
//	}
//	if req.Size != nil {
//		update[dao.Ad.Columns().Size] = req.Size
//	}
//	if req.Content != nil {
//		update[dao.Ad.Columns().Content] = req.Content
//	}
//	if req.Image != nil {
//		update[dao.Ad.Columns().Image] = req.Image
//	}
//	if req.ImageMobile != nil {
//		update[dao.Ad.Columns().ImageMobile] = req.ImageMobile
//	}
//	if req.Remark != nil {
//		update[dao.Ad.Columns().Remark] = req.Remark
//	}
//	if req.Type != nil {
//		update[dao.Ad.Columns().Type] = req.Type
//	}
//	if req.ShowPc != nil {
//		update[dao.Ad.Columns().ShowPc] = req.ShowPc
//	}
//	if req.ShowMobile != nil {
//		update[dao.Ad.Columns().ShowMobile] = req.ShowMobile
//	}
//	if req.OpenReferer != nil {
//		update[dao.Ad.Columns().OpenReferer] = req.OpenReferer
//	}
//
//	// 取旧数据
//	oldData, err := s.One(ctx, &v1.AdOneReq{
//		Id: req.Id,
//	})
//	m := s.updateModel(ctx, oldData.Belong, &oldData.BelongId)
//	update[dao.Ad.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	if _, err = m.Where(dao.Ad.Columns().Id, req.Id).Update(update); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		OldRecordOrAttr: oldData,
//		NewRecordOrAttr: req,
//	})
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sAd) Delete(ctx context.Context, req *v1.AdDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	update[dao.Ad.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	update[dao.Ad.Columns().DeleteTime] = gtime.Now().UnixMilli()
//
//	var ids []uint
//	if req.Id != 0 {
//		ids = append(ids, req.Id)
//	}
//	if len(req.Ids) > 0 {
//		ids = append(ids, req.Ids...)
//	}
//	if len(ids) < 1 {
//		err = gerror.New("id is empty")
//		return
//	}
//
//	// 取旧数据
//	oldData, err := s.One(ctx, &v1.AdOneReq{
//		Id: ids[0],
//	})
//
//	m := s.updateModel(ctx, oldData.Belong, &oldData.BelongId)
//	if _, err = m.Where(dao.Ad.Columns().Id, ids).Data(update).Update(); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: oldData,
//	})
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sAd) One(ctx context.Context, req *v1.AdOneReq) (res *v1.AdOneRes, err error) {
//	res = new(v1.AdOneRes)
//
//	sg, err := dao.Ad.Ctx(ctx).Where(dao.Ad.Columns().Id, req.Id).One()
//	if err != nil {
//		return
//	}
//
//	var item v1.AdItem
//	err = gconv.Scan(sg, &item)
//	if err != nil {
//		return
//	}
//	res = &v1.AdOneRes{
//		AdItem: item,
//	}
//	return
//}
//
//func (s *sAd) GroupSiteAllList(ctx context.Context, req *v1.AdGroupSiteAllListReq) (res *v1.AdGroupSiteAllListRes, err error) {
//	list, err := s.List(ctx, &v1.AdListReq{
//		Belong:   req.Belong,
//		BelongId: req.BelongId,
//	})
//	if err != nil {
//		return
//	}
//	var ids string
//	for _, item := range list.List {
//		ids += gconv.String(item.SelfId) + ","
//	}
//	if len(ids) > 0 {
//		ids = ids[:len(ids)-1]
//	}
//	if ids == "" {
//		ids = "-1"
//	}
//
//	res = new(v1.AdGroupSiteAllListRes)
//	res.Current = req.Current
//	res.List = make([]v1.AdItem, 0)
//
//	m := dao.Ad.Ctx(ctx)
//	if req.Name != nil {
//		m = m.WhereLike(dao.Ad.Columns().Name, utility.WhereLike(*req.Name))
//	}
//	if req.Type != nil {
//		m = m.Where(dao.Ad.Columns().Type, *req.Type)
//	}
//
//	m = m.Where(dao.Ad.Columns().DeleteTime, 0).
//		Where(dao.Ad.Columns().Belong, 0)
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.Ad.Table() + "." + dao.Ad.Columns().Id) // 最新在前面
//	}
//
//	// 查询列表
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.AdItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.AdItem, 0, total)
//	}
//
//	m = m.Fields(dao.Ad.Table()+".*",
//		"CASE WHEN "+dao.Ad.Columns().Id+" IN ("+ids+") THEN 1 ELSE 2 END AS ChooseStatus")
//	err = m.Scan(&res.List)
//	if err != nil {
//		return
//	}
//	res.Total = total
//	return
//}
//
//func (s *sAd) GroupSiteAdd(ctx context.Context, req *v1.AdGroupSiteAddReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//	var insertData []entity.Ad
//	m := s.updateModel(ctx, req.Belong, &req.BelongId)
//	if _, err = basicLib.GroupSiteAdd(ctx, req.GroupSiteAddReqIn, basicLib.GroupSiteAddParams{
//		M: m,
//		T: dao.Ad.Table(),
//		B: req.Belong,
//	}, insertData); err != nil {
//		return
//	}
//
//	s.deleteCache(ctx)
//	return
//}
//
//// 批量新增时，删除原来的，新增新的，保证每个分组仅存在一个广告
//func (s *sAd) AddBatch(ctx context.Context, req *v1.AdAddBatchReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
//		_, err = dao.Ad.Ctx(ctx).Where(dao.Ad.Columns().Belong, 1).Where(dao.Ad.Columns().BelongId, req.GroupIds).Delete()
//		if err != nil {
//			return err
//		}
//		for _, v := range req.GroupIds {
//			_, err = service.Ad().Add(ctx, &v1.AdAddReq{
//				Name:        req.Name,
//				Url:         req.Url,
//				ExpireTime:  req.ExpireTime,
//				Content:     req.Content,
//				Image:       req.Image,
//				ImageMobile: req.ImageMobile,
//				Remark:      req.Remark,
//				Type:        req.Type,
//				ShowPc:      req.ShowPc,
//				ShowMobile:  req.ShowMobile,
//				OpenReferer: req.OpenReferer,
//				Belong:      1,
//				BelongId:    &v,
//			})
//		}
//		return nil
//	})
//	return
//}
