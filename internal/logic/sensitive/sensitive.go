package sensitive

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	v1 "gtcms/api/v1"
	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	do "gtcms/internal/model/do/admin"
	entity "gtcms/internal/model/entity/admin"
	"gtcms/internal/service"
)

type (
	sSensitive struct{}
)

func init() {
	service.RegisterSensitive(New())
}

var cl = dao.Sensitive.Columns()

func New() service.ISensitive {
	return &sSensitive{}
}

func (s *sSensitive) Create(ctx context.Context, in *entity.Sensitive) (err error) {
	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		in.CreateAccount = admin.Account
	}
	_, err = dao.Sensitive.Ctx(ctx).Data(in).Insert()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			NewRecordOrAttr: in,
		})
	}
	return
}

func (s *sSensitive) Delete(ctx context.Context, id uint) (err error) {
	var (
		isExisted bool
	)
	isExisted, err = s.IsIdExist(ctx, id)
	if err != nil {
		return err
	}
	if !isExisted {
		return gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}

	admin, _ := service.Utility().GetSelf(ctx)
	now := time.Now().UnixMilli()
	lb := &do.Sensitive{
		DeleteTime: now,
		UpdateTime: now,
	}
	if admin != nil {
		lb.UpdateAccount = admin.Account
	}

	var oldData entity.Sensitive
	_ = dao.Sensitive.Ctx(ctx).WherePri(id).Scan(&oldData)

	_, err = dao.Sensitive.Ctx(ctx).WherePri(id).Delete()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
		})
	}
	return
}

func (s *sSensitive) Edit(ctx context.Context, id uint, in *entity.Sensitive) (err error) {
	var oldData entity.Sensitive
	_ = dao.Sensitive.Ctx(ctx).WherePri(id).Scan(&oldData)

	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		in.UpdateAccount = admin.Account
	}
	in.UpdateTime = time.Now().UnixMilli()

	_, err = dao.Sensitive.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Data(in).Update()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
			NewRecordOrAttr: in,
		})
	}
	return
}

func (s *sSensitive) Detail(ctx context.Context, id uint) (out *v1.SensitiveDetailRes, err error) {
	if id == 0 {
		admin, err := service.Utility().GetSelf(ctx)
		if err != nil {
			return nil, err
		}
		id = admin.Id
	}

	err = dao.Sensitive.Ctx(ctx).WherePri(id).Scan(&out)
	if out == nil {
		return nil, gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}
	return
}

func (s *sSensitive) List(ctx context.Context, in *v1.SensitiveListReq) (out *v1.SensitiveListRes, err error) {
	out = new(v1.SensitiveListRes)
	out.Current = in.Current
	md := dao.Sensitive.Ctx(ctx).Where(cl.DeleteTime, 0).Where(cl.ClassId, in.ClassId).
		Where(cl.IsOpen, in.IsOpen).OmitNilWhere().OmitEmptyWhere()

	if in.StartTime > 0 {
		md = md.WhereGTE(cl.CreateTime, in.StartTime)
	}
	if in.EndTime > 0 {
		md = md.WhereLTE(cl.CreateTime, in.EndTime)
	}

	if len(in.Key) > 0 {
		md = md.WhereLike(cl.Word, "%"+in.Key+"%")
	}

	out.Total, err = md.Count()
	if err != nil {
		return nil, err
	}
	err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&out.List)

	mapCategoryIDName, _ := s.mapCategoryIDName(ctx)
	if g.IsEmpty(mapCategoryIDName) {
		return
	}

	for _, v := range out.List {
		v.ClassName = mapCategoryIDName[v.ClassId]
	}
	return
}

func (s *sSensitive) IsIdExist(ctx context.Context, id uint) (bool, error) {
	count, err := dao.Sensitive.Ctx(ctx).WherePri(id).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *sSensitive) Options(ctx context.Context) (options []model.SelectOption, err error) {
	options = make([]model.SelectOption, 0)
	cl := dao.SensitiveCategory.Columns()
	err = dao.SensitiveCategory.Ctx(ctx).Fields(fmt.Sprintf("%s as label, %s as value", cl.Name, cl.CategoryId)).
		OrderAsc(cl.CategoryId).Where(cl.IsOpen, 1).Scan(&options)
	return
}

func (s *sSensitive) mapCategoryIDName(ctx context.Context) (ret map[int]string, err error) {
	var recs []*entity.SensitiveCategory
	cl := dao.SensitiveCategory.Columns()
	err = dao.SensitiveCategory.Ctx(ctx).Where(cl.IsOpen, 1).Scan(&recs)

	ret = make(map[int]string, len(recs))
	for _, v := range recs {
		ret[v.CategoryId] = v.Name
	}
	return
}
