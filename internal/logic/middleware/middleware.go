package middleware

import (
	"context"
	"encoding/json"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/util/gconv"
	"gtcms/internal/consts"

	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
	"net/http"
	"reflect"
	"strings"
	"unsafe"
)

type (
	sMiddleware struct{}
)

func init() {
	service.RegisterMiddleware(New())
}

func New() service.IMiddleware {
	return &sMiddleware{}
}

// 2.5版本兼容:Ctx传播给异步流程或者保持和之前逻辑兼容
func (s *sMiddleware) NeverDoneCtx(r *ghttp.Request) {
	r.SetCtx(r.GetNeverDoneCtx())
	r.Middleware.Next()
}

// 记录访问请求头，请求体
func (s *sMiddleware) LogRequest(r *ghttp.Request) {
	// g.Log().Info(r.Context(), g.Map{"uid": 100, "name": "john"}) //或者Print，Debug，Info
	g.Log().Line().Debug(r.Context(), "\n\n")
	g.Log().Line().Debug(r.Context(), "--------------------------------------------------")
	ip := r.GetClientIp()
	g.Log().Line().Debug(r.Context(), ip, ",", r.RemoteAddr, ",", r.Header.Get("X-Real-IP"), ",", r.Header.Get("X-Forwarded-For"))
	g.Log().Line().Debug(r.Context(), r.Method, r.URL)
	g.Log().Line().Debug(r.Context(), r.Header)

	var bodyBytes []byte
	if r.Body != nil {
		bodyBytes = r.GetBody()
		tmpFile := r.GetUploadFile("file")
		if strings.HasSuffix(r.URL.Path, "/file") || tmpFile != nil {
			if r.PostForm == nil {
				bodyBytes = make([]byte, 0)
			} else {
				bodyBytes, _ = json.Marshal(r.PostForm)
			}
		}
	}
	if len(bodyBytes) > 0 {
		g.Log().Line().Debug(r.Context(), "Request.Body:", unsafe.String(unsafe.SliceData(bodyBytes), len(bodyBytes)))
	}
	g.Log().Line().Debug(r.Context(), "--------------------------------------------------")

	r.Middleware.Next()
}

// 多语言
func (s *sMiddleware) I18n(r *ghttp.Request) {
	lang := r.Header.Get(consts.Lang)
	if lang == "" {
		lang = r.GetQuery(consts.Lang).String()
	}
	// 默认语言
	if lang == "" {
		lang = consts.LangDefaultCode
	}
	r.SetCtx(gi18n.WithLanguage(r.Context(), lang))
	r.Middleware.Next()
}

// 身份认证
func (s *sMiddleware) Auth(r *ghttp.Request) {
	token := r.Header.Get("Authorization")
	token = strings.Replace(token, "Bearer ", "", -1)
	// 兼容参数模式
	if token == "" {
		token = r.Get("token").String()
	}

	// 验证jwt
	jwtd, err := service.Utility().ParseJWT(token, consts.JWTSecretKey)
	if err != nil {
		r.Response.ClearBuffer()
		r.Response.WriteJsonExit(g.Map{
			"code":    1100,
			"message": g.I18n().T(r.GetCtx(), `admin.token.invalid`),
		})
		return
	}

	// 验证redis中的token
	key := consts.KeyAuthToken(jwtd.Id)
	redisToken, err := g.Redis().Get(r.Context(), key)
	if err != nil {
		g.Log().Line().Warningf(r.Context(), "Get redis value failed! key =%s, err =%s", key, err)
		r.Response.ClearBuffer()
		r.Response.WriteJsonExit(g.Map{
			"code":    1200,
			"message": g.I18n().T(r.GetCtx(), `redis.get.error`),
		})
		return
	}
	if token != redisToken.String() {
		g.Log().Line().Warningf(r.Context(), "Input token =%s, redis =%s key =%s", token, redisToken, key)
		r.Response.ClearBuffer()
		r.Response.WriteJsonExit(g.Map{
			"code":    1101,
			"message": g.I18n().T(r.GetCtx(), `admin.token.invalid`),
		})
		return
	}

	// 查看有token剩余有效时间
	ex, _ := g.Redis().Do(r.Context(), "TTL", key)
	if gconv.Int(ex) < consts.TokenUpdateEx {
		// 根据有效期少于20分钟的更新，(或者在读的时候就用GetEX?)
		// 更新token有效期
		err = g.Redis().SetEX(r.Context(), key, token, consts.TokenRedisEx)
		if err != nil {
			/*
				r.Response.ClearBuffer()
				r.Response.WriteJsonExit(g.Map{
					"code":    1201,
					"message": g.I18n().T(r.GetCtx(), `redis.set.error`),
				})
				return
			*/
		}
	}
	r.SetCtxVar(consts.ContextKeyUrl, r.URL.Path)

	// 将用户信息写入context
	var self *entity.Account
	self, err = service.Account().GetAdminFromMasterCache(r.Context(), jwtd.Id)
	if err != nil {
		r.Response.ClearBuffer()
		r.Response.WriteJsonExit(g.Map{
			"code":    1250,
			"message": g.I18n().T(r.GetCtx(), `db.get.error`),
		})
		return
	}
	r.SetCtxVar(consts.KeyHttpContext, self)

	roleConfig, _ := service.RoleV2().Detail(r.Context(), self.RoleId)
	if roleConfig.RoleLevel != consts.RoleLevelAdmin {
		role := &model.AccountRole{
			RoleLevel: roleConfig.RoleLevel,
			SelfId:    self.Id,
			ParentId:  self.Creater,
			ChildIds:  nil,
		}
		role.ChildIds, _ = service.Account().ChildID(r.Context(), self.Id)
		r.SetCtxVar(consts.KeyContextRole, role)
	}

	r.Middleware.Next()
}

// 记录接口、域名、ip访问次数 (定时任务每天0点清除集合)
func (s *sMiddleware) Counter(r *ghttp.Request) {
	api := r.URL.Path
	host := r.Host
	ip := service.Utility().GetClientIp(r)
	// 可异步并发，用go协程
	_ = grpool.AddWithRecover(context.Background(), func(ctx context.Context) {
		var check *gvar.Var
		// 接口
		check, _ = g.Redis().Get(r.Context(), "checkApiCounter_1")
		if check.Int() == 1 {
			g.Redis().Do(r.Context(), "ZINCRBY", "apiCounter_1", 1, api)
		}
		// 域名
		check, _ = g.Redis().Get(r.Context(), "checkHostCounter_1")
		if check.Int() == 1 {
			g.Redis().Do(r.Context(), "ZINCRBY", "hostCounter_1", 1, host)
		}
		// ip
		check, _ = g.Redis().Get(r.Context(), "checkIpCounter_1")
		if check.Int() == 1 {
			g.Redis().Do(r.Context(), "ZINCRBY", "ipCounter_1", 1, ip)
		}
	}, nil)
	r.Middleware.Next()
}

// 拒绝黑名单ip
func (s *sMiddleware) CheckBlackIp(r *ghttp.Request) {
	ip := service.Utility().GetClientIp(r)
	if ip != "" {
		var check *gvar.Var
		check, _ = g.Redis().Get(r.Context(), "checkBlackIp")
		if check.Int() == 1 {
			value, _ := g.Redis().Do(r.Context(), "ZSCORE", "ipBlackList", ip)
			if value.Int() > 0 {
				r.Response.ClearBuffer()
				r.Response.WriteJsonExit(g.Map{
					"code":    9001,
					"message": g.I18n().T(r.GetCtx(), `security.ip.blocked`),
				})
				return
			}
		}
	}
	r.Middleware.Next()
}

func (s *sMiddleware) HandlerResponse(r *ghttp.Request) {
	r.Middleware.Next()

	// There's custom buffer content, it then exits current handler.
	if r.Response.BufferLength() > 0 {
		return
	}

	var (
		msg  string
		err  = r.GetError()
		res  = r.GetHandlerResponse()
		code = gerror.Code(err)
	)
	if err != nil {
		if code == gcode.CodeNil {
			code = gcode.CodeInternalError
		}
		if code.Code() == gcode.CodeDbOperationError.Code() { // 不返回SQL错误信息
			msg = gi18n.T(r.Context(), "internal.error")
		} else {
			msg = err.Error()
		}
	} else {
		if r.Response.Status > 0 && r.Response.Status != http.StatusOK {
			msg = http.StatusText(r.Response.Status)
			switch r.Response.Status {
			case http.StatusNotFound:
				code = gcode.CodeNotFound
			case http.StatusForbidden:
				code = gcode.CodeNotAuthorized
			default:
				code = gcode.CodeUnknown
			}
			// It creates error as it can be retrieved by other middlewares.
			err = gerror.NewCode(code, msg)
			r.SetError(err)
		} else {
			code = gcode.CodeOK
		}
	}

	lev := g.Log().GetLevel()
	deb := glog.LEVEL_DEBU
	if lev&deb > 1 {
		by, _ := gjson.Marshal(r.GetHandlerResponse())
		g.Log().Debug(r.Context(), "Response = ", string(by))
	}

	// r.Response.DefaultCORSOptions()
	// s.handleSensitiveFields(r, res) // 回包处理脱敏字段
	// s.maskResponseFields(r, res) // 回包处理脱敏字段
	s.maskResponseFields(r, res) // 回包处理脱敏字段
	type resp struct {
		ghttp.DefaultHandlerResponse
		Meta struct {
			TraceId string `json:"trace_id"`
		} `json:"meta"`
	}
	r.Response.WriteJson(resp{
		DefaultHandlerResponse: ghttp.DefaultHandlerResponse{
			Code:    code.Code(),
			Message: msg,
			Data:    res,
		},
		Meta: struct {
			TraceId string `json:"trace_id"`
		}{
			TraceId: gctx.CtxId(r.Context()),
		},
	})
	//r.Response.WriteJson(ghttp.DefaultHandlerResponse{
	//	Code:    code.Code(),
	//	Message: msg,
	//	Data:    res,
	//})
}
func (s *sMiddleware) SetCORSOptions(r *ghttp.Request) {
	corsOpts := r.Response.DefaultCORSOptions()
	// corsOpts.AllowDomain = []string{"http://192.168.10.34:5173"}
	// corsOpts.AllowOrigin = "http://192.168.10.34:5173"
	r.Response.CORS(corsOpts)
	r.Middleware.Next()
}

func (s *sMiddleware) AccessControlCheck(r *ghttp.Request) {
	// 获取请求头中的 Accept-Language
	lang := r.Header.Get("Accept-Language")
	var language int
	if strings.Contains(lang, "zh-CN") {
		language = 0 // 简体中文
	} else if strings.Contains(lang, "en-US") {
		language = 1 // 英语
	} else if strings.Contains(lang, "id-ID") {
		language = 2 // 印尼语
	} else {
		language = 0 // 默认语言
	}
	ctx := r.Context()
	r.SetCtxVar(consts.LanguageId, language)

	admin, err := service.Utility().GetSelf(ctx)
	if admin == nil {
		g.Log().Line().Error(ctx, "AccessControlCheck get account empty! error=", err)
		return
	}
	if "admin" == admin.Account || admin.Account == "gogoTest" {
		r.Middleware.Next()
		return
	}

	g.Log().Line().Info(ctx, "AccessControlCheck begin roleId=", admin.RoleId, "Account=", admin.Account, "Url=", r.URL)
	// 获取前端自定义操作路径
	nodePath := r.Header.Get("Opernodepath")

	isPassed, fieldPaths, err := service.RoleV2().AccessCheck(ctx, admin.RoleId, nodePath, r.URL.Path)
	if err != nil {
		g.Log().Line().Error(ctx, "AccessControlCheck end: AccessCheck error ", err)
		return
	}

	if !isPassed {
		g.Log().Line().Warning(ctx, "AccessControlCheck end: permission denied! account=", admin.Account, "url=", r.URL)
		r.Response.ClearBuffer()
		r.Response.WriteJsonExit(g.Map{
			"code":    1120,
			"message": g.I18n().T(r.GetCtx(), `No access permission`),
		})
		return
	}

	g.Log().Line().Info(ctx, "AccessControlCheck end: passed! account=", admin.Account, "url=", r.URL, "fieldPaths=", fieldPaths)
	if !g.IsEmpty(fieldPaths) {
		roleCtx := &model.RolePermissionInfo{
			// PId:              0,
			UrlPath: r.URL.String(),
			// PermissionSet:    "",
			MaskedPathFields: fieldPaths,
		}
		r.SetCtxVar(consts.ContextKeyRolePerm, roleCtx)
	}

	r.Middleware.Next()
}

func (s *sMiddleware) maskResponseFields(r *ghttp.Request, res interface{}) {
	defer func() {
		r.Middleware.Next()
	}()
	if res == nil {
		return
	}

	ctx := r.Context()
	rolePerm, err := service.Utility().GetRolePermission(ctx)
	if err != nil || rolePerm == nil {
		return
	}

	g.Log().Line().Infof(ctx, "maskResponseFields begin: rolePermission=[%+v]", rolePerm)
	var integerMaskFields []string
	var isIntegerMask bool
	for _, filePath := range rolePerm.MaskedPathFields {
		var fieldPaths []string
		fieldPaths = strings.Split(filePath, "-")
		// if len(fieldPaths) == 0 {
		//	//兼容老格式
		//	fieldPaths = strings.Split(filePath, "-")
		// }
		service.PermissionV2().MaskingRespPacket(ctx, reflect.ValueOf(res).Elem(), fieldPaths, 0, &isIntegerMask)
		if isIntegerMask {
			lowPath := ConvertLowercase(filePath)
			integerMaskFields = append(integerMaskFields, lowPath)
			isIntegerMask = false
		}
	}

	if !g.IsEmpty(integerMaskFields) {
		// r.Response.SetHeader("Content-Type").String("application/x-www-form-urlencoded")
		interMask, err := gjson.Marshal(integerMaskFields)
		if err == nil {
			r.Response.Header().Set("Access-Control-Expose-Headers", "X-Mask-Fields")
			r.Response.Header().Set("X-Mask-Fields", string(interMask))
			g.Log().Line().Info(ctx, "maskResponseFields end: integer MaskFields =", interMask, "url=", rolePerm.UrlPath)
		} else {
			g.Log().Line().Warning(ctx, "maskResponseFields Marshal integerMaskFields =[%+v]", integerMaskFields, "err=", err)
		}
	}
}

func ConvertLowercase(in string) (out string) {
	fieldPaths := strings.Split(in, "-")
	length := len(fieldPaths)
	for i, v := range fieldPaths {
		if len(v) == 0 {
			return
		}
		lowVal := strings.ToLower(v[:1]) + v[1:]
		out += lowVal
		if i != length-1 {
			out += "-"
		}
	}

	return
}
