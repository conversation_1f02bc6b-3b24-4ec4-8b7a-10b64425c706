package news

//
//import (
//	"context"
//	"encoding/base64"
//	"fmt"
//	"github.com/chromedp/cdproto/page"
//	"github.com/chromedp/chromedp"
//	"github.com/gogf/gf/v2/container/gset"
//	"github.com/gogf/gf/v2/database/gdb"
//	"github.com/gogf/gf/v2/errors/gerror"
//	"github.com/gogf/gf/v2/frame/g"
//	"github.com/gogf/gf/v2/net/ghttp"
//	"github.com/gogf/gf/v2/os/gctx"
//	"github.com/gogf/gf/v2/os/gtime"
//	"github.com/gogf/gf/v2/os/gtimer"
//	"github.com/gogf/gf/v2/util/gconv"
//	v1 "gtcms/api/v1"
//	"gtcms/internal/consts"
//	"gtcms/internal/dao"
//	"gtcms/internal/logic/file"
//	_ "gtcms/internal/logic/redis"
//	"gtcms/internal/model"
//	"gtcms/internal/model/entity"
//	"gtcms/internal/service"
//	"gtcms/utility"
//	"gtcms/utility/slices"
//	"io"
//	"math/rand"
//	"os"
//	"path"
//	"regexp"
//	"strings"
//	"sync"
//	"time"
//)
//
//var ctx = gctx.New()
//
//type sNews struct {
//	excludeColId []int // 排除的栏目id，这些栏目不显示在列表中，例如：专题栏目
//}
//
//func init() {
//	service.RegisterNews(New())
//}
//
//func New() *sNews {
//	s := &sNews{}
//	//go s.initData()
//	s.timer()
//	return s
//}
//
//// 每60分钟，去craw_news_row表取文章发布
//func (s *sNews) timer() {
//	gtimer.DelayAdd(ctx, 1*time.Hour, 1*time.Hour, func(ctx context.Context) {
//		s.run()
//	})
//}
//
//func (s *sNews) run() {
//	err := s.AutoPublish(ctx, nil, false, nil)
//	if err != nil {
//		return
//	}
//}
//
//func (s *sNews) initData() {
//	nows := gtime.Now().Unix()
//	g.Log().Info(ctx, "init news")
//	array, err := dao.Columns.Ctx(ctx).Fields(dao.Columns.Columns().Id).
//		Where(dao.Columns.Columns().TdkTmplCategory, []int{consts.TdkTopic, consts.TdkChannel}).Array()
//	if err != nil {
//		return
//	}
//
//	for _, v := range array {
//		s.excludeColId = append(s.excludeColId, gconv.Int(v))
//	}
//
//	array, err = dao.ColumnsGroupRelation.Ctx(ctx).Fields(dao.ColumnsGroupRelation.Columns().Id).
//		Where(dao.ColumnsGroupRelation.Columns().TdkTmplCategory, []int{consts.TdkTopic, consts.TdkChannel}).Array()
//	if err != nil {
//		return
//	}
//	for _, v := range array {
//		s.excludeColId = append(s.excludeColId, gconv.Int(v))
//	}
//
//	var lastNewsID uint = 0 // 初始值为 0，表示从最早的记录开始加载
//	var firstNewsId uint
//	newsIds := gset.NewSet() // 存储新闻ID集合
//
//	// 分批加载新闻数据
//	for {
//		// 使用 lastNewsID 进行分页
//		m := dao.News.Ctx(ctx).Fields(
//			dao.News.Columns().Id,
//			dao.News.Columns().BelongColId,
//			dao.News.Columns().BelongSiteId,
//			dao.News.Columns().Title,
//			dao.News.Columns().Desc,
//			dao.News.Columns().Thumb,
//			dao.News.Columns().CreateTime,
//			dao.News.Columns().IsHot,
//			dao.News.Columns().Attr,
//		)
//		m = m.Where(dao.News.Columns().DeleteTime, 0).Where(dao.News.Columns().Status, 1)
//		if len(s.excludeColId) > 0 {
//			m = m.WhereNotIn(dao.News.Columns().BelongColId, s.excludeColId)
//		}
//		m = m.WhereGT(dao.News.Columns().Id, lastNewsID). // 使用 ID 进行分页
//									Order(dao.News.Columns().Id). // 保证按 ID 升序排列
//									Limit(consts.BatchSize)
//		rs, err := m.All()
//		if err != nil || len(rs) == 0 {
//			break // 无更多数据或发生错误时退出循环
//		}
//
//		//newsM := make(map[string]interface{})
//		for _, v := range rs {
//			one := s.makeData(v)
//			// 更新新闻ID
//			lastNewsID = one.Id
//			if firstNewsId == 0 {
//				firstNewsId = one.Id
//			}
//			newsIds.Add(one.Id)
//			//newsM["news_"+gconv.String(one.Id)] = one
//			//err = service.Redis().Del("news_" + gconv.String(one.Id))
//			//if err != nil {
//			//	g.Log().Error(ctx, "del err", err)
//			//	return
//			//}
//		}
//		//err = service.Redis().MSet(newsM)
//		//if err != nil {
//		//	g.Log().Error(ctx, "redis err", err)
//		//	return
//		//}
//	}
//
//	one, err := dao.NewsBody.Ctx(ctx).Fields(dao.NewsBody.Columns().Id).Where(dao.NewsBody.Columns().NewsId, firstNewsId).One()
//	if err != nil {
//		return
//	}
//	firstNewsId = uint(gconv.Int(one[dao.NewsBody.Columns().Id]))
//
//	for {
//		// 使用 lastBodyID 进行分页
//		rs, err := dao.NewsBody.Ctx(ctx).Fields(
//			dao.NewsBody.Columns().Id,
//			dao.NewsBody.Columns().NewsId,
//			dao.NewsBody.Columns().Body,
//		).
//			Where(dao.NewsBody.Columns().DeleteTime, 0).
//			WhereGT(dao.NewsBody.Columns().Id, firstNewsId). // 使用 ID 进行分页
//			Order(dao.NewsBody.Columns().Id).                // 保证按 ID 升序排列
//			Limit(consts.BatchSize * 2).All()
//		if err != nil || len(rs) == 0 {
//			break // 无更多数据或发生错误时退出循环
//		}
//
//		//newsBodyM := make(map[string]interface{})
//		for _, v := range rs {
//			one := s.makeBodyData(v)
//			// 更新新闻内容ID
//			firstNewsId = one.Id
//
//			// 将包含在 newsIds 中的新闻内容存储到 s.dataBody
//			if newsIds.Contains(one.NewsId) {
//				//newsBodyM["newsBody_"+gconv.String(one.NewsId)] = one
//			}
//			//service.Redis().Del("newsBody_" + gconv.String(one.NewsId))
//		}
//		//err = service.Redis().MSet(newsBodyM)
//		//if err != nil {
//		//	g.Log().Error(ctx, "redis err", g.Map{"err": err})
//		//	return
//		//}
//	}
//
//	g.Log().Info(ctx, "init news end, cost time:", gtime.Now().Unix()-nows)
//}
//
//func (s *sNews) makeData(v gdb.Record) (one *entity.News) {
//	one = &entity.News{
//		Id:           gconv.Uint(v[dao.News.Columns().Id]),
//		Title:        gconv.String(v[dao.News.Columns().Title]),
//		Desc:         gconv.String(v[dao.News.Columns().Desc]),
//		Thumb:        gconv.String(v[dao.News.Columns().Thumb]),
//		CreateTime:   gconv.Int64(v[dao.News.Columns().CreateTime]),
//		Attr:         gconv.String(v[dao.News.Columns().Attr]),
//		BelongSiteId: gconv.Uint(v[dao.News.Columns().BelongSiteId]),
//		BelongColId:  gconv.Uint(v[dao.News.Columns().BelongColId]),
//		IsHot:        gconv.Int(v[dao.News.Columns().IsHot]),
//	}
//	if one.Attr == "1" {
//		one.SeoKeyword = one.SeoTitle
//		one.SeoDesc = one.SeoTitle
//	}
//	return
//}
//
//func (s *sNews) makeBodyData(v gdb.Record) (one *entity.NewsBody) {
//	one = &entity.NewsBody{
//		Id:     gconv.Uint(v[dao.NewsBody.Columns().Id]),
//		NewsId: gconv.Uint(v[dao.NewsBody.Columns().NewsId]),
//		Body:   gconv.String(v[dao.NewsBody.Columns().Body]),
//	}
//	return
//}
//
//func (s *sNews) getRandomElementsFromSlice(slice []*model.CrawlerNewsRaw, n int) []*model.CrawlerNewsRaw {
//	r := rand.New(rand.NewSource(time.Now().UnixNano()))
//	shuffled := make([]*model.CrawlerNewsRaw, len(slice))
//	copy(shuffled, slice)
//	r.Shuffle(len(shuffled), func(i, j int) {
//		shuffled[i], shuffled[j] = shuffled[j], shuffled[i]
//	})
//
//	if n > len(slice) {
//		n = len(slice)
//	}
//	return shuffled[:n]
//}
//
//func (s *sNews) List(ctx context.Context, req *v1.NewsListReq) (res *v1.NewsListRes, err error) {
//	res = new(v1.NewsListRes)
//	res.Current = req.Current
//	res.List = make([]v1.NewsItem, 0)
//
//	m := dao.News.Ctx(ctx).Handler(utility.CreatorFilter)
//	if req.Title != nil {
//		m = m.WhereLike(dao.News.Columns().Title, utility.WhereLike(*req.Title))
//	}
//	if req.Status != nil {
//		m = m.Where(dao.News.Columns().Status, *req.Status)
//	}
//	if req.BelongColId != nil {
//		m = m.Where(dao.News.Columns().BelongColId, *req.BelongColId)
//	}
//	if req.BelongGroupId != nil {
//		m = m.Where(dao.News.Columns().BelongGroupId, *req.BelongGroupId)
//	}
//	if req.BelongSiteId != nil {
//		m = m.Where(dao.News.Columns().BelongSiteId, *req.BelongSiteId)
//	}
//
//	leftJoinSite := false
//	if req.DomainName != nil {
//		leftJoinSite = true
//		m = m.LeftJoin(dao.Site.Table() + " on " + dao.Site.Table() + "." + dao.Site.Columns().Id + " = " + dao.News.Table() + "." + dao.News.Columns().BelongSiteId)
//		m = m.LeftJoin(dao.Domain.Table() + " on " + dao.Domain.Table() + "." + dao.Domain.Columns().Id + " = " + dao.Site.Table() + "." + dao.Site.Columns().DomainId)
//		m = m.Where(dao.Domain.Table()+"."+dao.Domain.Columns().Name, *req.DomainName)
//	}
//	if req.IsHot != nil {
//		m = m.Where(dao.News.Columns().IsHot, *req.IsHot)
//	}
//	if req.IsAi != nil {
//		//m = m.Where(dao.News.Columns().IsAi, *req.IsAi)
//	}
//
//	m = m.Where(dao.News.Columns().DeleteTime, 0)
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.News.Table() + "." + dao.News.Columns().Id) // 最新在前面
//	}
//
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.NewsItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.NewsItem, 0, total)
//	}
//
//	m = m.Fields(dao.News.Table()+".*", dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Name+" as belongGroupName",
//		dao.Site.Table()+"."+dao.Site.Columns().Name+" as belongSiteName").
//		LeftJoin(dao.SiteGroup.Table(), dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Id+" = "+dao.News.Table()+"."+dao.News.Columns().BelongGroupId)
//	if !leftJoinSite {
//		m = m.LeftJoin(dao.Site.Table(), dao.Site.Table()+"."+dao.Site.Columns().Id+" = "+dao.News.Table()+"."+dao.News.Columns().BelongSiteId)
//	}
//	err = m.Scan(&res.List)
//	if err != nil {
//		return
//	}
//
//	res.Total = total
//	return
//}
//
//func (s *sNews) Add(ctx context.Context, req *v1.NewsAddReq, auto bool) (res *v1.NewsAddRes, err error) {
//	res = new(v1.NewsAddRes)
//
//	if req.Attr != "1" {
//		// 非视频，取200个字
//		req.Desc = service.Utility().CutString(req.Content, 200)
//	} else {
//		// 视频，取标题
//		req.Desc = req.Title
//	}
//
//	var entityData *entity.News
//	if err = gconv.Scan(req, &entityData); err != nil {
//		return
//	}
//
//	if !auto {
//		var self *entity.Account
//		self, err = service.Utility().GetSelf(ctx)
//		if err != nil {
//			return
//		}
//
//		entityData.CreateTime = gtime.Now().UnixMilli()
//		entityData.Creater = self.Id
//	}
//
//	now := time.Now().UnixNano()
//	entityData.Pdf = gconv.String(now)
//	id, err := dao.News.Ctx(ctx).InsertAndGetId(entityData)
//	if err != nil {
//		return
//	}
//
//	entityData2 := &entity.NewsBody{
//		NewsId: uint(id),
//		Body:   req.Content,
//	}
//	if !auto {
//		var self *entity.Account
//		self, err = service.Utility().GetSelf(ctx)
//		if err != nil {
//			return
//		}
//		entityData2.CreateTime = gtime.Now().UnixMilli()
//		entityData2.Creater = self.Id
//		s.updateCache(ctx, []int64{gconv.Int64(id)})
//	}
//	_, err = dao.NewsBody.Ctx(ctx).InsertAndGetId(entityData2)
//	if err != nil {
//		return
//	}
//
//	res = &v1.NewsAddRes{
//		Id: id,
//	}
//
//	//_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//	//	NewRecordOrAttr: req,
//	//})
//
//	err = s.deleteCache(ctx, req.BelongSiteId)
//	//s.deleteCache2(ctx, req.BelongColId)
//
//	//entityData.Id = gconv.Uint(id)
//	//entityData2.Id = gconv.Uint(id2)
//	//g.Redis().Set(ctx, "news_"+gconv.String(id), entityData)
//
//	//if entityData.Resource != "" {
//	//	g.Redis().Set(ctx, "newsBody_"+entityData.Resource, entityData2)
//	//} else {
//	//	g.Redis().Set(ctx, "newsBody_"+gconv.String(id), entityData2)
//	//}
//	g.Go(ctx, func(ctx context.Context) {
//		s.generateAndUploadPDF(entityData.Title, entityData2.Body, entityData.Pdf+".pdf")
//	}, nil)
//	return
//}
//
//func (s *sNews) deleteCache2(ctx context.Context, colId uint) {
//	var cols []*entity.Columns
//	dao.Columns.Ctx(ctx).Fields(dao.Columns.Columns().Id, dao.Columns.Columns().TdkTmplCategory).
//		WhereIn(dao.Columns.Columns().TdkTmplCategory, []int{consts.TdkTopic, consts.TdkChannel}).Scan(&cols)
//
//	var cols2 []*entity.Columns
//	dao.ColumnsGroupRelation.Ctx(ctx).Fields(dao.ColumnsGroupRelation.Columns().Id, dao.Columns.Columns().TdkTmplCategory).
//		WhereIn(dao.ColumnsGroupRelation.Columns().TdkTmplCategory, []int{consts.TdkTopic, consts.TdkChannel}).Scan(&cols2)
//
//	cols = append(cols, cols2...)
//
//	notTopicChannel := true
//	topic, channel := false, false
//	for _, v := range cols {
//		if v.Id == colId {
//			notTopicChannel = false
//			switch v.TdkTmplCategory {
//			case consts.TdkTopic:
//				topic = true
//			case consts.TdkChannel:
//				channel = true
//			default:
//				panic("unhandled default case")
//			}
//			break
//		}
//	}
//	if notTopicChannel {
//		return
//	}
//
//	classId := ""
//	redisKey := ""
//	if topic {
//		classId = consts.TopicUpdate
//		redisKey = consts.TopicListPage
//	} else if channel {
//		classId = consts.ChannelUpdate
//		redisKey = consts.ChannelListPage
//	}
//
//	for i := 0; i < 100; i++ {
//		key := redisKey + gconv.String(i)
//		_, _ = service.DbCache().DeleteDbCache(ctx, key)
//	}
//	g.Go(ctx, func(ctx context.Context) {
//		// 延迟一秒钟
//		<-time.After(1 * time.Second)
//
//		service.DbCache().PublishUpdate(ctx, model.MemoryUpdateMessage{
//			ClassId: classId,
//		})
//	}, nil)
//}
//
//func (s *sNews) updateCache(ctx context.Context, newsIds []int64) {
//	g.Go(ctx, func(ctx context.Context) {
//		// 延迟2秒钟
//		<-time.After(2 * time.Second)
//
//		idsStr := ""
//		for _, v := range newsIds {
//			idsStr += gconv.String(v) + ","
//		}
//
//		service.DbCache().PublishUpdate(ctx, model.MemoryUpdateMessage{
//			ClassId: consts.NewsUpdateNotAuto,
//			Key:     idsStr,
//		})
//	}, nil)
//}
//
//func (s *sNews) Edit(ctx context.Context, req *v1.NewsEditReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	if req.BelongColId != nil {
//		update[dao.News.Columns().BelongColId] = req.BelongColId
//	}
//	if req.BelongGroupId != nil {
//		update[dao.News.Columns().BelongGroupId] = req.BelongGroupId
//	}
//	if req.BelongSiteId != nil {
//		update[dao.News.Columns().BelongSiteId] = req.BelongSiteId
//	}
//	if req.BelongColName != nil {
//		update[dao.News.Columns().BelongColName] = req.BelongColName
//	}
//	if req.Thumb != nil {
//		update[dao.News.Columns().Thumb] = req.Thumb
//	}
//	if req.Title != nil {
//		update[dao.News.Columns().Title] = req.Title
//	}
//	if req.SeoTitle != nil {
//		update[dao.News.Columns().SeoTitle] = req.SeoTitle
//	}
//	if req.SeoDesc != nil {
//		update[dao.News.Columns().SeoDesc] = req.SeoDesc
//	}
//	if req.SeoKeyword != nil {
//		update[dao.News.Columns().SeoKeyword] = req.SeoKeyword
//	}
//	if req.Desc != nil {
//		update[dao.News.Columns().Desc] = req.Desc
//	}
//	if req.FileName != nil {
//		update[dao.News.Columns().FileName] = req.FileName
//	}
//	if req.Attr != nil {
//		update[dao.News.Columns().Attr] = req.Attr
//	}
//	if req.Label != nil {
//		update[dao.News.Columns().Label] = req.Label
//	}
//	if req.Resource != nil {
//		update[dao.News.Columns().Resource] = req.Resource
//	}
//	if req.Sort != nil {
//		update[dao.News.Columns().Sort] = req.Sort
//	}
//	if req.Status != nil {
//		update[dao.News.Columns().Status] = req.Status
//	}
//	if req.Author != nil {
//		update[dao.News.Columns().Author] = req.Author
//	}
//	if req.Content != nil {
//		if _, err = dao.NewsBody.Ctx(ctx).Where(dao.NewsBody.Columns().NewsId, req.Id).Update(g.Map{
//			dao.NewsBody.Columns().Body: req.Content,
//		}); err != nil {
//			return
//		}
//	}
//	if req.IsHot != nil {
//		update[dao.News.Columns().IsHot] = req.IsHot
//	}
//	if req.Label != nil {
//		update[dao.News.Columns().Label] = req.Label
//	}
//
//	// 取出原来数据
//	oldData, err := s.One(ctx, &v1.NewsOneReq{Id: req.Id})
//
//	update[dao.News.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	if _, err = dao.News.Ctx(ctx).Where(dao.News.Columns().Id, req.Id).Update(update); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		OldRecordOrAttr: oldData,
//		NewRecordOrAttr: req,
//	})
//
//	err = s.deleteCache(ctx, gconv.Uint(req.BelongSiteId))
//	//s.updateCache(ctx, []int64{gconv.Int64(req.Id)})
//	//s.deleteCache2(ctx, *req.BelongColId)
//
//	var news *entity.News
//	dao.News.Ctx(ctx).Where(dao.News.Columns().Id, req.Id).Scan(&news)
//	g.Redis().Set(ctx, "news_"+gconv.String(news.Id), news)
//
//	var newsBody *entity.NewsBody
//	dao.NewsBody.Ctx(ctx).Where(dao.NewsBody.Columns().NewsId, news.Id).Scan(&newsBody)
//	if news.Resource != "" {
//		g.Redis().Set(ctx, "newsBody_"+news.Resource, newsBody)
//	} else {
//		g.Redis().Set(ctx, "newsBody_"+gconv.String(news.Id), newsBody)
//	}
//	return
//}
//
//func (s *sNews) Delete(ctx context.Context, req *v1.NewsDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	update[dao.News.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	update[dao.News.Columns().DeleteTime] = gtime.Now().UnixMilli()
//
//	var ids []uint
//	if req.Id != 0 {
//		ids = append(ids, req.Id)
//	}
//	if len(req.Ids) > 0 {
//		ids = append(ids, req.Ids...)
//	}
//	if len(ids) < 1 {
//		err = gerror.New("id is empty")
//		return
//	}
//
//	// 取旧数据
//	oldData, _ := s.One(ctx, &v1.NewsOneReq{
//		Id: ids[0],
//	})
//	if _, err = dao.News.Ctx(ctx).Where(dao.News.Columns().Id, ids).Data(update).Update(); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		OldRecordOrAttr: oldData,
//	})
//
//	err = s.deleteCache(ctx, gconv.Uint(oldData.BelongSiteId))
//	s.updateCache(ctx, gconv.Int64s(ids))
//	//s.deleteCache2(ctx, oldData.BelongColId)
//	return
//}
//
//func (s *sNews) One(ctx context.Context, req *v1.NewsOneReq) (res *v1.NewsOneRes, err error) {
//	res = new(v1.NewsOneRes)
//
//	data, err := dao.News.Ctx(ctx).Fields(dao.News.Table()+".*", dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Name+" as belongGroupName",
//		dao.Site.Table()+"."+dao.Site.Columns().Name+" as belongSiteName").
//		LeftJoin(dao.SiteGroup.Table(), dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Id+" = "+dao.News.Table()+"."+dao.News.Columns().BelongGroupId).
//		LeftJoin(dao.Site.Table(), dao.Site.Table()+"."+dao.Site.Columns().Id+" = "+dao.News.Table()+"."+dao.News.Columns().BelongSiteId).
//		Where(dao.News.Columns().Id, req.Id).One()
//	if err != nil {
//		return
//	}
//
//	var item v1.NewsItem
//	err = gconv.Scan(data, &item)
//	if err != nil {
//		return
//	}
//
//	data2, err := dao.NewsBody.Ctx(ctx).Where(dao.NewsBody.Columns().NewsId, req.Id).One()
//	if err != nil {
//		return
//	}
//	var entityData *entity.NewsBody
//	if err = gconv.Scan(data2, &entityData); err != nil {
//		return
//	}
//	item.Content = entityData.Body
//
//	res = &v1.NewsOneRes{
//		NewsItem: item,
//	}
//	return
//}
//
//func (s *sNews) UpdateStatus(ctx context.Context, req *v1.NewsUpdateStatusReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	update[dao.News.Columns().Status] = req.Status
//	update[dao.News.Columns().UpdateTime] = gtime.Now().UnixMilli()
//
//	if _, err = dao.News.Ctx(ctx).Where(dao.News.Columns().Id, req.Ids).Data(update).Update(); err != nil {
//		return
//	}
//	return
//}
//
//func (s *sNews) ImportTxt(ctx context.Context, req *v1.NewsImportTxtReq) (res *v1.EmptyDataRes, err error) {
//	ids := make([]int64, 0, len(req.Files))
//
//	res = new(v1.EmptyDataRes)
//	var wg sync.WaitGroup
//	//SiteGroupId 站点id 参数校验。参考utility.GroupFilter 先不加
//
//	//req.SiteGroupId
//	//查询指定分组参数下的 所有站点。原来的SiteIds保留 append
//	mergeSiteIds := make([]uint, 0)
//	mergeSiteIds = append(mergeSiteIds, req.SiteIds...)
//
//	// 没有选择站点时，才取分组
//	if len(mergeSiteIds) == 0 {
//		if req.SiteGroupId > 0 {
//			selectOptionList, _ := service.Site().Options(ctx, req.SiteGroupId)
//			for _, item := range selectOptionList {
//				mergeSiteIds = append(mergeSiteIds, gconv.Uint(item.Value))
//			}
//		}
//	}
//
//	if len(mergeSiteIds) < 1 {
//		g.Log().Line().Error(ctx, "siteIds is empty")
//		return
//	}
//
//	for _, file := range req.Files {
//		for _, siteId := range slices.Unique(mergeSiteIds) {
//			sId := siteId
//			wg.Add(1)
//			go func(file *ghttp.UploadFile, siteId uint) {
//				defer wg.Done()
//
//				site, _ := service.Site().One(ctx, &v1.SiteOneReq{Id: siteId})
//				if site == nil {
//					g.Log().Line().Error(ctx, "site is not exist", siteId)
//					return
//				}
//				columns, _ := service.Columns().One(ctx, req.ColumnsId)
//				if columns == nil {
//					g.Log().Line().Error(ctx, "columns is not exist", req.ColumnsId)
//					return
//				}
//
//				fr, _ := file.Open()
//				defer fr.Close()
//
//				content, _ := io.ReadAll(fr)
//
//				//txt
//				ext := path.Ext(file.Filename)
//				if ext == ".txt" {
//					fr, _ := file.Open()
//					defer fr.Close()
//					res, _ := s.Add(ctx, &v1.NewsAddReq{
//						BelongSiteId:  siteId,
//						BelongGroupId: site.GroupId,
//						BelongColId:   req.ColumnsId,
//						BelongColName: columns.Name,
//						Title:         strings.ReplaceAll(file.Filename, ".txt", ""),
//						Status:        1,
//						Content:       gconv.String(content),
//						CreateTime:    time.Now().UnixMilli(),
//					}, true)
//					ids = append(ids, res.Id)
//				} else if ext == ".xlsx" || ext == ".xls" {
//					//excel
//					var dataList []model.AiNewsAddItem
//					if err = service.FileProcessor().Import(ctx, file, &dataList); err != nil {
//						return
//					}
//					if len(dataList) > 0 {
//						for _, data := range dataList {
//							_, _ = s.Add(ctx, &v1.NewsAddReq{
//								BelongSiteId:  siteId,
//								BelongGroupId: site.GroupId,
//								BelongColId:   req.ColumnsId,
//								BelongColName: columns.Name,
//								Title:         data.Title,
//								Status:        1,
//								Content:       data.Content,
//								Thumb:         s.getCover(data.Content),
//								CreateTime:    time.Now().UnixMilli(),
//							}, true)
//							//ids = append(ids, res.Id)
//						}
//					}
//				}
//			}(file, sId)
//		}
//	}
//	wg.Wait()
//	s.updateCache(ctx, ids)
//	return
//}
//
//// 自动发布新闻
//// 采集的文章只发布中文站点和未过期域名的站点
//// ai生成的文章可发布各种语言站点
//func (s *sNews) AutoPublish(ctx context.Context, newsList []*model.CrawlerNewsRaw, isAiGen bool, aiGenItem *v1.AiGenItem) (err error) {
//	//all, err := dao.SiteGroup.Ctx(ctx).Fields(dao.SiteGroup.Columns().Id, dao.SiteGroup.Columns().NewsUpdatesNum).
//	//	Where(dao.SiteGroup.Columns().DeleteTime, 0).
//	//	WhereGT(dao.SiteGroup.Columns().NewsUpdatesNum, 0).All()
//	//判断是否有分组id参数 有的话 就加一个过滤
//	queryModel := dao.SiteGroup.Ctx(ctx).Fields(dao.SiteGroup.Columns().Id, dao.SiteGroup.Columns().NewsUpdatesNum).
//		Where(dao.SiteGroup.Columns().DeleteTime, 0).
//		WhereGT(dao.SiteGroup.Columns().NewsUpdatesNum, 0)
//	if aiGenItem != nil && aiGenItem.SiteGroupId > 0 {
//		queryModel = queryModel.Where(dao.SiteGroup.Columns().Id, aiGenItem.SiteGroupId)
//	}
//	all, err := queryModel.All()
//
//	if err != nil {
//		return
//	}
//
//	if !isAiGen {
//		// 获取今天的新闻
//		startT, endT := service.Utility().GetTodayTimestamps()
//		err = dao.CrawlerNewsRaw.Ctx(ctx).
//			WhereGT(dao.CrawlerNewsRaw.Columns().CreateTime, startT).
//			WhereLT(dao.CrawlerNewsRaw.Columns().CreateTime, endT).
//			Where(dao.CrawlerNewsRaw.Columns().Title + " <> ''").
//			Where("is_ai = 1 or is_video = 1").Scan(&newsList)
//		if err != nil {
//			return
//		}
//		if len(newsList) < 1 {
//			return
//		}
//	}
//
//	var oldNewsIds []int64
//	// 所有分组
//	for _, v := range all {
//		groupId := gconv.Uint(v[dao.SiteGroup.Columns().Id])
//		newsUpdatesNum := gconv.Int(v[dao.SiteGroup.Columns().NewsUpdatesNum])
//		if newsUpdatesNum < 1 {
//			continue
//		}
//
//		// 要发布的新闻
//		publishNews := s.getRandomElementsFromSlice(newsList, 100)
//
//		var siteList []*model.SiteItem
//		m := dao.Site.Ctx(ctx)
//		m = m.Where(dao.Site.Columns().GroupId, groupId).
//			Where(dao.Site.Columns().Status, 1).
//			Where(dao.Site.Columns().DeleteTime, 0)
//		if !isAiGen {
//			m = m.Where(dao.Site.Columns().Language, "cn")
//		}
//		if err = m.Scan(&siteList); err != nil {
//			continue
//		}
//
//		newIds := make([]int64, 0)
//		// 同一分组下面的所有站点
//		for _, site := range siteList {
//			news := s.getRandomElementsFromSlice(publishNews, 1)
//			oldNewsIds = append(oldNewsIds, news[0].Id)
//
//			// 检查今天是否已经发布过且超过配置的发布新闻数
//			if service.Utility().IsToday(site.AutoPublishNewsTime) {
//				if site.HasAutoPublishNewsNum >= newsUpdatesNum {
//					continue
//				}
//			} else {
//				site.AutoPublishNewsTime = 0
//				site.HasAutoPublishNewsNum = 0
//			}
//
//			var newId int64
//			if newId, err = s.publishNewsToSite(ctx, site, news, isAiGen, aiGenItem); err != nil {
//				continue
//			}
//			if newId > 0 {
//				newIds = append(newIds, newId)
//			}
//		}
//
//		//go func(newIds []int64) {
//		//	// 延迟2秒钟
//		//	<-time.After(2 * time.Second)
//		//
//		//	idsStr := ""
//		//	for _, v := range newIds {
//		//		idsStr += gconv.String(v) + ","
//		//	}
//		//
//		//	if idsStr == "" {
//		//		return
//		//	}
//		//	service.DbCache().PublishUpdate(ctx, model.MemoryUpdateMessage{
//		//		ClassId: consts.NewsUpdate,
//		//		Key:     idsStr,
//		//	})
//		//}(append([]int64{}, newIds...))
//
//		if !isAiGen {
//			if _, err = dao.CrawlerNewsRaw.Ctx(ctx).Delete(dao.CrawlerNewsRaw.Columns().Id, oldNewsIds); err != nil {
//				return
//			}
//		}
//	}
//	return
//}
//
//// publishNewsToSite 发布新闻到指定站点
//func (s *sNews) publishNewsToSite(ctx context.Context, site *model.SiteItem, news []*model.CrawlerNewsRaw, isAiGen bool, aiGenItem *v1.AiGenItem) (id int64, err error) {
//	for _, v := range news {
//		if v == nil {
//			continue
//		}
//
//		var col *entity.Columns
//		if isAiGen {
//			// 如果是0，则随机选择一个栏目
//			if aiGenItem.ColumnId == 0 {
//				cols, _ := service.Columns().ColTreeOptions(ctx, &v1.ColumnsOptionsReq{
//					Belong:   2,
//					BelongId: &(site.Id),
//					Language: site.Language,
//					IsNews:   1,
//				})
//				idx := rand.Intn(len(cols))
//				if idx == 0 {
//					idx = 1
//				}
//				c := cols[idx]
//				err = gconv.Scan(c, &col)
//				if err != nil {
//					return
//				}
//			} else {
//				col, err = service.Columns().One(ctx, gconv.Uint(aiGenItem.ColumnId))
//				if err != nil {
//					return
//				}
//			}
//		} else {
//			col, err = service.Columns().GetNewsColumnsByModuleId(ctx, site, gconv.String(v.CompetitionId))
//			if err != nil {
//				return
//			}
//		}
//
//		res, err := s.Add(ctx, &v1.NewsAddReq{
//			BelongSiteId:  site.Id,
//			BelongGroupId: site.GroupId,
//			BelongColId:   col.Id,
//			BelongColName: strings.ReplaceAll(col.Name, "└", ""),
//			Title:         v.Title,
//			Status:        1,
//			Content:       v.Content,
//			CreateTime:    v.CreateTime,
//			Attr:          gconv.String(v.IsVideo),
//			Thumb:         s.getCover(v.Content),
//			Resource:      gconv.String(v.Id),
//			IsAuto:        1,
//			//IsAi:          v.IsAi,
//		}, true)
//		if err != nil {
//			continue
//		}
//		id = res.Id
//	}
//
//	site.AutoPublishNewsTime = time.Now().UnixMilli()
//	site.HasAutoPublishNewsNum += len(news)
//	_, _ = dao.Site.Ctx(ctx).Data(site).Where(dao.Site.Columns().Id, site.Id).Update()
//	return
//}
//
//func (s *sNews) getCover(content string) (cover string) {
//	// 正则表达式匹配 img 标签中的 src 属性
//	re := regexp.MustCompile(`<img\s+[^>]*src=["']([^"']+)["'][^>]*>`)
//	match := re.FindStringSubmatch(content)
//
//	if len(match) > 1 {
//		return match[1] // 返回第一个匹配的 URL
//	}
//	return "" // 如果没有匹配，返回空字符串
//}
//
//func (s *sNews) deleteCache(ctx context.Context, siteId uint) (err error) {
//	for _, v := range consts.ModNews {
//		for i := 0; i < 100; i++ {
//			key := consts.NewsListPage + gconv.String(siteId) + ":" + gconv.String(v) + ":" + gconv.String(i)
//			_, err = service.DbCache().DeleteDbCache(ctx, key)
//
//			key2 := consts.NewsListPageCount + gconv.String(siteId) + ":" + gconv.String(v)
//			_, err = service.DbCache().DeleteDbCache(ctx, key2)
//		}
//
//		key := consts.NewsListByMod + gconv.String(siteId) + ":" + gconv.String(v)
//		_, err = service.DbCache().DeleteDbCache(ctx, key)
//
//		key2 := consts.NewsListByModCount + gconv.String(siteId) + ":" + gconv.String(v)
//		_, err = service.DbCache().DeleteDbCache(ctx, key2)
//	}
//	return
//}
//
//func (s *sNews) generatePDFWithChromedp(html, outputPath string) error {
//	// 自定义 Chrome 启动参数
//	opts := append(chromedp.DefaultExecAllocatorOptions[:],
//		chromedp.ExecPath("/usr/local/bin/headless-chromium"),
//		chromedp.Flag("no-sandbox", true),
//		chromedp.Flag("user-data-dir", "/home/<USER>/chrome-user"),
//		chromedp.Headless,
//		chromedp.DisableGPU,
//	)
//
//	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
//	defer cancel()
//
//	ctx, cancel := chromedp.NewContext(allocCtx)
//	defer cancel()
//
//	var pdfBuf []byte
//	err := chromedp.Run(ctx,
//		chromedp.Navigate("data:text/html;charset=utf-8;base64,"+
//			base64.StdEncoding.EncodeToString([]byte(html))),
//		chromedp.ActionFunc(func(ctx context.Context) error {
//			var err error
//			pdfBuf, _, err = page.PrintToPDF().
//				WithPrintBackground(true).
//				Do(ctx)
//			return err
//		}),
//	)
//	if err != nil {
//		g.Log().Error(ctx, "chromedp 生成 PDF 失败:", err)
//		return fmt.Errorf("chromedp 生成 PDF 失败: %w", err)
//	}
//
//	// ✅ 注意使用 os.WriteFile 替代 ioutil
//	return os.WriteFile(outputPath, pdfBuf, 0644)
//}
//
//// 生成pdf文件，并上传到远程服务器
//func (s *sNews) generateAndUploadPDF(title, articleHTML, filename string) error {
//	// 拼接 HTML，加上标题
//	styledHTML := fmt.Sprintf(`
//	<html>
//	<head>
//	  <meta charset="UTF-8">
//	  <style>
//	    h1 {
//	      text-align: center;
//	      font-size: 28px;
//	      font-weight: bold;
//	      margin-bottom: 30px;
//	    }
//	    body {
//	      font-family: Arial, sans-serif;
//	      padding: 40px;
//	      font-size: 14px;
//	      line-height: 1.6;
//	    }
//	    img {
//	      max-width: 100%%;
//	      height: auto;
//	    }
//	  </style>
//	</head>
//	<body>
//	  <h1>%s</h1>
//	  %s
//	</body>
//	</html>
//	`, title, articleHTML)
//	tmpPath := "/tmp/" + filename
//	if err := s.generatePDFWithChromedp(styledHTML, tmpPath); err != nil {
//		g.Log().Error(ctx, "生成PDF失败: %v", err)
//	}
//
//	// 2. 打开生成的 PDF 文件
//	f, err := os.Open(tmpPath)
//	if err != nil {
//		g.Log().Error(ctx, "打开PDF失败: %v", err)
//	}
//	defer f.Close()
//
//	// 3. 上传 PDF 文件到远程服务器
//	err = file.UploadImageViaSFTPByRsa(ctx, f, filename, 3) // sportImg 是 false
//	if err != nil {
//		g.Log().Error(ctx, "上传PDF失败: %v", err)
//	}
//
//	// 4. （可选）删除本地临时文件
//	err = os.Remove(tmpPath)
//	if err != nil {
//		g.Log().Error(ctx, "remove file err:", err)
//	}
//
//	return nil
//}
