package faq

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/i18n/gi18n"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"

	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	model "gtcms/internal/model/islamic"
	"gtcms/internal/service"
)

type sFaq struct{}

func init() {
	service.RegisterFaq(New())
}
func New() *sFaq {
	return &sFaq{}
}

func (f *sFaq) CateAdd(ctx context.Context, req *v1.CateCreateReq) (res *v1.CateCreateRes, err error) {

	err = dao.FaqCate.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// cate表新增。
		lastId, err := tx.Model(dao.FaqCate.Table()).Ctx(ctx).Data(do.FaqCate{Sort: req.Sort, CateCount: 0}).InsertAndGetId()
		if err != nil {
			return err
		}
		// cate_languages表新增。
		var faqCateLanguages = make([]do.FaqCateLanguage, 0, len(req.Item))
		isZh := 0
		isEn := 0
		isId := 0
		for _, item := range req.Item {
			faqCateLanguage := do.FaqCateLanguage{
				FaqCateId:  lastId,
				Title:      item.Name,
				LanguageId: item.LanguageId,
			}
			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}

			faqCateLanguages = append(faqCateLanguages, faqCateLanguage)
		}

		_, err = tx.Model(dao.FaqCateLanguage.Table()).Ctx(ctx).Data(faqCateLanguages).Insert()
		if err != nil {
			return err
		}

		_, err = tx.Model(dao.FaqCate.Table()).Where(dao.FaqCate.Columns().Id, lastId).Update(do.FaqCate{
			IsZh: isZh,
			IsEn: isEn,
			IsId: isId,
		})

		return err
	})

	return
}

func (f *sFaq) CateEdit(ctx context.Context, req *v1.CateEditeReq) (res *v1.CateEditRes, err error) {
	err = dao.FaqCate.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 更新主表
		_, err := tx.Model(dao.FaqCate.Table()).
			Data(do.FaqCate{Sort: req.Sort}). // TODO 后续加上修改者
			Where(dao.FaqCate.Columns().Id, req.Id).
			Update()
		if err != nil {
			return err
		}
		isZh := 0
		isEn := 0
		isId := 0
		// 2. 处理子表：新增 or 更新
		for _, item := range req.Item {
			faqCateLanguage := do.FaqCateLanguage{
				FaqCateId:  req.Id,
				Title:      item.Name,
				LanguageId: item.LanguageId,
			}
			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}
			if item.Id > 0 {
				// 更新
				_, err = tx.Model(dao.FaqCateLanguage.Table()).
					Data(faqCateLanguage).
					Where(dao.FaqCateLanguage.Columns().Id, item.Id).
					Update()
			} else {
				// 新增
				_, err = tx.Model(dao.FaqCateLanguage.Table()).
					Data(faqCateLanguage).
					Insert()
			}
			if err != nil {
				return err
			}
		}

		_, err = tx.Model(dao.FaqCate.Table()).Where(dao.FaqCate.Columns().Id, req.Id).Update(do.FaqCate{
			IsZh: isZh,
			IsEn: isEn,
			IsId: isId,
		})

		return err
	})

	return
}

func (f *sFaq) CateDelete(ctx context.Context, req *v1.CateDeleteReq) (res *v1.CateDeleteRes, err error) {
	err = dao.FaqCate.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		count := 0
		count, err = tx.Model(dao.FaqQuestion.Table()).Where(dao.FaqQuestion.Columns().FaqCateId, req.Id).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			err = gerror.NewCode(gcode.CodeInvalidOperation, gi18n.T(ctx, "faqCate.delete.InvalidOperation"))
			return err
		}
		_, err = tx.Model(dao.FaqCate.Table()).Where(dao.FaqCate.Columns().Id, req.Id).Delete()

		if err != nil {
			return err
		}
		_, err = tx.Model(dao.FaqCateLanguage.Table()).Where(dao.FaqCateLanguage.Columns().FaqCateId, req.Id).Delete()
		if err != nil {
			return err
		}
		return nil
	})
	return
}

func (f *sFaq) CateOne(ctx context.Context, req *v1.CateOneReq) (res *v1.CateOneRes, err error) {
	var cate model.FaqCateWithLanguage
	err = dao.FaqCate.Ctx(ctx).
		Where(dao.FaqCate.Columns().Id, req.Id).
		With(model.FaqCateWithLanguage{}.Language). // 预加载一对多
		Scan(&cate)
	if err != nil {
		return
	}
	return &v1.CateOneRes{
		FaqCateWithLanguage: cate,
	}, nil
}

func (f *sFaq) CateList(ctx context.Context, req *v1.CateListReq) (res *v1.CateListRes, err error) {
	var cates []*model.FaqCateWithLanguage
	err = dao.FaqCate.Ctx(ctx).
		Page(req.Current, req.PageSize).
		With(model.FaqCateWithLanguage{}.Language). // 预加载一对多
		Order("id desc").
		Scan(&cates)
	if err != nil {
		return
	}
	res = &v1.CateListRes{
		List: cates,
	}
	return

}

func (f *sFaq) QuestionAdd(ctx context.Context, req *v1.QuestionCreateReq) (res *v1.QuestionCreateRes, err error) {

	err = dao.FaqQuestion.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// faq_question表新增。
		lastId, err := tx.Model(dao.FaqQuestion.Table()).Ctx(ctx).Data(
			do.FaqQuestion{Sort: req.Sort, FaqCateId: req.CateId}).InsertAndGetId()
		if err != nil {
			return err
		}
		// faq_question_language 表新增。
		var faqQuestionLanguages = make([]do.FaqQuestionLanguage, 0, len(req.Item))

		isZh := 0
		isEn := 0
		isId := 0

		for _, item := range req.Item {
			faqQuestionLanguage := do.FaqQuestionLanguage{
				FaqQuestionId: lastId,
				LanguageId:    item.LanguageId,
				Title:         item.Title,
				Desc:          item.Desc,
			}
			faqQuestionLanguages = append(faqQuestionLanguages, faqQuestionLanguage)

			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}
		}

		_, err = tx.Model(dao.FaqQuestionLanguage.Table()).Ctx(ctx).Data(faqQuestionLanguages).Insert()
		if err != nil {
			return err
		}
		// cate  count 加一
		_, err = tx.Model(dao.FaqCate.Table()).Ctx(ctx).Where("id = ?", req.CateId).
			Increment(dao.FaqCate.Columns().CateCount, 1)
		if err != nil {
			return err
		}
		// 更新主表语言。
		_, err = tx.Model(dao.FaqQuestion.Table()).Ctx(ctx).Where(dao.FaqQuestion.Columns().Id, lastId).
			Update(do.FaqQuestion{
				IsZh: isZh,
				IsEn: isEn,
				IsId: isId,
			})
		if err != nil {
			return err
		}

		// 位置
		faqShowPositions := make([]do.FaqShowPosition, 0, len(req.PositionId))
		for _, positionId := range req.PositionId {
			faqShowPosition := do.FaqShowPosition{
				FaqQuestionId: lastId,
				PositionId:    positionId,
			}
			faqShowPositions = append(faqShowPositions, faqShowPosition)
		}
		_, err = dao.FaqShowPosition.Ctx(ctx).Data(faqShowPositions).Insert()

		return err
	})

	return
}

func (f *sFaq) QuestionEdit(ctx context.Context, req *v1.QuestionEditReq) (res *v1.QuestionEditRes, err error) {
	err = dao.FaqQuestion.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 更新主表
		_, err := tx.Model(dao.FaqQuestion.Table()).
			Data(do.FaqQuestion{Sort: req.Sort, FaqCateId: req.CateId}).
			Where(dao.FaqQuestion.Columns().Id, req.Id).
			Update()
		if err != nil {
			return err
		}
		isZh := 0
		isEn := 0
		isId := 0
		// 2. 处理子表：新增 or 更新
		for _, item := range req.Item {
			faqQuestionLanguage := do.FaqQuestionLanguage{
				FaqQuestionId: req.Id,
				Title:         item.Title,
				Desc:          item.Desc,
				LanguageId:    item.LanguageId,
			}

			if item.Id > 0 {
				// 更新
				_, err = tx.Model(dao.FaqQuestionLanguage.Table()).
					Data(faqQuestionLanguage).
					Where(dao.FaqQuestionLanguage.Columns().Id, item.Id).
					Update()
			} else {
				// 新增
				_, err = tx.Model(dao.FaqQuestionLanguage.Table()).
					Data(faqQuestionLanguage).
					Insert()
			}
			if err != nil {
				return err
			}

			if item.LanguageId == consts.ArticleLanguageZh {
				isZh = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageEn {
				isEn = consts.One
			}

			if item.LanguageId == consts.ArticleLanguageId {
				isId = consts.One
			}
		}

		// 更新主表语言。
		_, err = tx.Model(dao.FaqQuestion.Table()).Ctx(ctx).Where(dao.FaqQuestion.Columns().Id, req.Id).
			Update(do.FaqQuestion{
				IsZh: isZh,
				IsEn: isEn,
				IsId: isId,
			})
		if err != nil {
			return err
		}

		// 位置先删除
		_, err = tx.Model(dao.FaqShowPosition).Where(dao.FaqShowPosition.Columns().FaqQuestionId, req.Id).Delete()
		if err != nil {
			return err
		}

		// 位置再更新
		faqShowPositions := make([]do.FaqShowPosition, 0, len(req.PositionId))
		for _, positionId := range req.PositionId {
			faqShowPosition := do.FaqShowPosition{
				FaqQuestionId: req.Id,
				PositionId:    positionId,
			}
			faqShowPositions = append(faqShowPositions, faqShowPosition)
		}
		_, err = dao.FaqShowPosition.Ctx(ctx).Data(faqShowPositions).Insert()

		return err
	})

	return
}

func (f *sFaq) QuestionDelete(ctx context.Context, req *v1.QuestionDeleteReq) (res *v1.QuestionDeleteRes, err error) {
	err = dao.FaqQuestion.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		record, err := tx.Model(dao.FaqQuestion.Table()).Where(dao.FaqQuestion.Columns().Id, req.Id).One()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.FaqQuestion.Table()).Where(dao.FaqQuestion.Columns().Id, req.Id).Delete()

		if err != nil {
			return err
		}
		_, err = tx.Model(dao.FaqQuestionLanguage.Table()).Where(dao.FaqQuestionLanguage.Columns().FaqQuestionId, req.Id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.FaqCate.Table()).Ctx(ctx).Where("id = ?", record["faq_cate_id"]).
			Decrement(dao.FaqCate.Columns().CateCount, 1)
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.FaqShowPosition).Where(dao.FaqShowPosition.Columns().FaqQuestionId, req.Id).Delete()

		return err
	})
	return

}

func (f *sFaq) QuestionOne(ctx context.Context, req *v1.QuestionOneReq) (res *v1.QuestionOneRes, err error) {
	var question *model.FaqQuestionWithLanguage
	err = dao.FaqQuestion.Ctx(ctx).Where(dao.FaqQuestion.Columns().Id, req.Id).
		With(model.FaqQuestionWithLanguage{}.Language).
		Scan(&question)
	if err != nil {
		return
	}

	return &v1.QuestionOneRes{
		question,
	}, nil
}

func (f *sFaq) QuestionList(ctx context.Context, req *v1.QuestionListReq) (res *v1.QuestionListRes, err error) {
	var questions []*model.FaqQuestionWithLanguage
	err = dao.FaqCate.Ctx(ctx).
		Page(req.Current, req.PageSize).
		With(model.FaqQuestionWithLanguage{}.Language, model.FaqQuestionWithLanguage{}.ShowPosition). // 预加载一对多
		Scan(&questions)
	if err != nil {
		return
	}
	res = &v1.QuestionListRes{
		List: questions,
	}
	return
}
