package faq

import (
	"context"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "gtcms/api/v1"

	dao "gtcms/internal/dao/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"

	"gtcms/internal/service"
)

type sQuran struct{}

func init() {
	service.RegisterQuran(New())
}
func New() *sQuran {
	return &sQuran{}
}

func (f *sQuran) SurahList(ctx context.Context, req *v1.SurahListReq) (res *v1.SurahListRes, err error) {
	res = new(v1.SurahListRes)
	res.Current = req.Current
	res.List = make([]*entity.SuratDaftar, 0)

	m := dao.SuratDaftar.Ctx(ctx)
	//m = m.Where(dao.SuratDaftar.Columns().DeleteTime, 0) //.<PERSON><PERSON>(utility.SiteFilter)
	total, err := m.Count()
	if err != nil {
		return
	}
	if total < 1 {
		return
	}
	if len(req.OrderBy) > 0 {
		m = m.Order(req.OrderBy)
	} else {
		m = m.OrderAsc(dao.SuratDaftar.Table() + "." + dao.SuratDaftar.Columns().Id) // 最新在前面
	}
	var resList []*entity.SuratDaftar
	if req.Current > 0 && req.PageSize > 0 {
		m = m.Page(req.Current, req.PageSize)
		resList = make([]*entity.SuratDaftar, 0, req.PageSize)
		res.List = make([]*entity.SuratDaftar, 0, req.PageSize)
	} else {
		resList = make([]*entity.SuratDaftar, 0, total)
		res.List = make([]*entity.SuratDaftar, 0, total)
	}

	err = m.Scan(&resList)
	if err != nil {
		return
	}
	for _, v := range resList {
		res.List = append(res.List, v)
	}
	res.Total = total
	return
}

func (f *sQuran) AyahList(ctx context.Context, req *v1.AyahListReq) (res *v1.AyahListRes, err error) {
	res = new(v1.AyahListRes)
	res.Current = req.Current
	res.List = make([]*entity.SuratAyat, 0)
	//pp := ctx.Value(consts.LanguageId)

	m := dao.SuratAyat.Ctx(ctx)
	m = m.Where(dao.SuratAyat.Columns().SurahId, req.Id) //.Handler(utility.SiteFilter)
	total, err := m.Count()
	if err != nil {
		return
	}
	if total < 1 {
		return
	}
	if len(req.OrderBy) > 0 {
		m = m.Order(req.OrderBy)
	} else {
		m = m.OrderAsc(dao.SuratAyat.Table() + "." + dao.SuratAyat.Columns().Id) // 最新在前面
	}
	var resList []*entity.SuratAyat
	if req.Current > 0 && req.PageSize > 0 {
		m = m.Page(req.Current, req.PageSize)
		resList = make([]*entity.SuratAyat, 0, req.PageSize)
		res.List = make([]*entity.SuratAyat, 0, req.PageSize)
	} else {
		resList = make([]*entity.SuratAyat, 0, total)
		res.List = make([]*entity.SuratAyat, 0, total)
	}

	err = m.Scan(&resList)
	if err != nil {
		return
	}
	for _, v := range resList {
		res.List = append(res.List, v)
	}
	res.Total = total
	return
}

func (f *sQuran) JuzList(ctx context.Context, req *v1.JuzListReq) (res *v1.JuzListRes, err error) {
	res = new(v1.JuzListRes)
	res.Current = req.Current
	res.List = make([]*v1.JuzItem, 0)

	m := dao.SuratAyat.Ctx(ctx)
	m = m.Where(dao.SuratAyat.Columns().Juz, req.Id) //.Handler(utility.SiteFilter)
	total, err := m.Count()
	if err != nil {
		return
	}
	if total < 1 {
		return
	}
	if len(req.OrderBy) > 0 {
		m = m.Order(req.OrderBy)
	} else {
		m = m.OrderAsc(dao.SuratAyat.Table() + "." + dao.SuratAyat.Columns().Id) // 最新在前面
	}
	var resList []*entity.SuratAyat
	if req.Current > 0 && req.PageSize > 0 {
		m = m.Page(req.Current, req.PageSize)
		resList = make([]*entity.SuratAyat, 0, req.PageSize)
		res.List = make([]*v1.JuzItem, 0, req.PageSize)
	} else {
		resList = make([]*entity.SuratAyat, 0, total)
		res.List = make([]*v1.JuzItem, 0, total)
	}

	err = m.Scan(&resList)
	if err != nil {
		return
	}

	var surahList []*entity.SuratDaftar
	querySurah := dao.SuratDaftar.Ctx(ctx)
	errSurah := querySurah.Scan(&surahList)
	if errSurah != nil || len(surahList) == 0 {
		return
	}
	// 初始化一个map 获取所有的SurahId和SurahName
	surahMap := make(map[int]*entity.SuratDaftar)
	for _, surah := range surahList {
		surahMap[surah.Nomor] = surah
	}

	for _, v := range resList {
		out := &v1.JuzItem{}
		gconv.Struct(v, out)
		out.SurahName = surahMap[v.SurahId].NamaLatin
		out.Place = surahMap[v.SurahId].TempatTurun
		out.Nomor = surahMap[v.SurahId].Nomor
		res.List = append(res.List, out)
	}
	res.Total = total
	return
}
