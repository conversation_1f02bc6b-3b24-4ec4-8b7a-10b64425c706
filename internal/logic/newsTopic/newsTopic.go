package newsTopic

import (
	"context"
	"database/sql"
	"errors"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"

	dao "gtcms/internal/dao/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"

	"gtcms/internal/service"
	"time"
)

type sNewsTopic struct{}

func init() {
	service.RegisterNewsTopic(New())
}

func New() *sNewsTopic {
	return &sNewsTopic{}
}

// 新增
func (s *sNewsTopic) Add(ctx context.Context, req *v1.NewsTopicAddReq) error {
	// 参数校验
	if 0 >= req.Sort {
		return gerror.New("排序值不能为空")
	}
	admin, _ := service.Utility().GetSelf(ctx)
	currentTime := time.Now().UnixMilli()
	topicData := entity.NewsTopic{
		TopicImgs:  req.TopicImgs,
		Creater:    admin.Id,
		CreateName: admin.Account,
		CreateTime: currentTime,
		UpdateTime: currentTime,
		Sort:       req.Sort,
		Status:     consts.One, // 启用
		IsAppShow:  req.IsAppShow,
		Counts:     gconv.Uint(len(req.ArticleArr)),
	}
	var topicLanguageData = make([]entity.NewsTopicLanguage, 0)
	var topicArticleData = make([]entity.NewsTopicArticle, 0)
	// 校验文章名称和内容
	for _, item := range req.NameArr {
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return gerror.New("语言类型错误")
		}
		one := entity.NewsTopicLanguage{
			Name:       item.TopicName,
			ShortName:  item.ShortName,
			CreateTime: currentTime,
			UpdateTime: currentTime,
			LanguageId: gconv.Uint(item.LanguageType),
		}
		if consts.Zero == item.LanguageType {
			topicData.IsZh = consts.One
		}
		if consts.One == item.LanguageType {
			topicData.IsEn = consts.One
		}
		if consts.Two == item.LanguageType {
			topicData.IsId = consts.One
		}
		topicLanguageData = append(topicLanguageData, one)
	}
	// 写入表
	err := dao.NewsTopic.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		id, err1 := dao.NewsTopic.Ctx(ctx).Data(topicData).InsertAndGetId()
		if err1 != nil {
			return err1
		}
		// 组装数据
		for key := range topicLanguageData {
			topicLanguageData[key].TopicId = gconv.Uint(id)
		}
		// 批量插入
		_, err1 = dao.NewsTopicLanguage.Ctx(ctx).Data(topicLanguageData).Insert()
		if err1 != nil {
			return err1
		}
		// 写入news_topic_article表
		for _, articleItem := range req.ArticleArr {
			topicArticleData = append(topicArticleData, entity.NewsTopicArticle{
				TopicId:    gconv.Uint(id),
				ArticleId:  articleItem.ArticleId,
				CreateTime: currentTime,
				UpdateTime: currentTime,
			})
		}
		_, err1 = dao.NewsTopicArticle.Ctx(ctx).Data(topicArticleData).Insert()
		return err1
	})
	return err
}

// 修改
func (s *sNewsTopic) Edit(ctx context.Context, req *v1.NewsTopicEditReq) error {
	// 参数校验
	if req.Id <= 0 {
		return gerror.New("id不能为空")
	}
	// 获取数据
	var topic entity.NewsTopic
	err := dao.NewsTopic.Ctx(ctx).Where(dao.NewsTopic.Columns().Id, req.Id).Scan(&topic)
	if err != nil {
		return err
	}
	if 0 >= topic.Id {
		return gerror.New("专题不存在")
	}
	if 0 >= req.Sort {
		return gerror.New("排序值不能为空")
	}
	admin, _ := service.Utility().GetSelf(ctx)
	currentTime := time.Now().UnixMilli()
	topicData := g.Map{
		dao.NewsTopic.Columns().TopicImgs:  req.TopicImgs,
		dao.NewsTopic.Columns().Creater:    admin.Id,
		dao.NewsTopic.Columns().CreateName: admin.Account,
		dao.NewsTopic.Columns().UpdateTime: currentTime,
		dao.NewsTopic.Columns().Sort:       req.Sort,
		dao.NewsTopic.Columns().IsAppShow:  req.IsAppShow,
		dao.NewsTopic.Columns().Counts:     gconv.Uint(len(req.ArticleArr)),
		dao.NewsTopic.Columns().IsZh:       consts.Zero,
		dao.NewsTopic.Columns().IsEn:       consts.Zero,
		dao.NewsTopic.Columns().IsId:       consts.Zero,
	}
	var topicLanguageData = make([]entity.NewsTopicLanguage, 0)
	var topicArticleData = make([]entity.NewsTopicArticle, 0)
	// 校验文章名称和内容
	for _, item := range req.NameArr {
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return gerror.New("语言类型错误")
		}
		one := entity.NewsTopicLanguage{
			Name:       item.TopicName,
			ShortName:  item.ShortName,
			CreateTime: currentTime,
			UpdateTime: currentTime,
			LanguageId: gconv.Uint(item.LanguageType),
		}
		if consts.Zero == item.LanguageType {
			topicData[dao.NewsTopic.Columns().IsZh] = consts.One
		}
		if consts.One == item.LanguageType {
			topicData[dao.NewsTopic.Columns().IsEn] = consts.One
		}
		if consts.Two == item.LanguageType {
			topicData[dao.NewsTopic.Columns().IsId] = consts.One
		}
		topicLanguageData = append(topicLanguageData, one)
	}
	// 写入表
	err = dao.NewsTopic.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		_, err1 = dao.NewsTopic.Ctx(ctx).Where(dao.NewsTopic.Columns().Id, req.Id).Data(topicData).Update()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.NewsTopicLanguage.Ctx(ctx).Where(dao.NewsTopicLanguage.Columns().TopicId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		// 组装数据
		for key := range topicLanguageData {
			topicLanguageData[key].TopicId = gconv.Uint(req.Id)
		}
		// 批量插入
		_, err1 = dao.NewsTopicLanguage.Ctx(ctx).Data(topicLanguageData).Insert()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.NewsTopicArticle.Ctx(ctx).Where(dao.NewsTopicArticle.Columns().TopicId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		// 写入news_topic_article表
		for _, articleItem := range req.ArticleArr {
			topicArticleData = append(topicArticleData, entity.NewsTopicArticle{
				TopicId:    gconv.Uint(req.Id),
				ArticleId:  articleItem.ArticleId,
				CreateTime: currentTime,
				UpdateTime: currentTime,
			})
		}
		_, err1 = dao.NewsTopicArticle.Ctx(ctx).Data(topicArticleData).Insert()
		return err1
	})
	return err
}

// 详情
func (s *sNewsTopic) Info(ctx context.Context, req *v1.NewsTopicInfoReq) (out *v1.NewsTopicInfoRes, err error) {
	out = &v1.NewsTopicInfoRes{}
	// 获取语言
	currentLang := gconv.Uint(ctx.Value(consts.LanguageId))
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("参数错误")
	}
	var topicData entity.NewsTopic
	err = dao.NewsTopic.Ctx(ctx).Where(dao.NewsTopic.Columns().Id, req.Id).Scan(&topicData)
	if err != nil {
		return out, err
	}
	// 获取专题下的文章
	var topicArticleData []entity.NewsTopicArticle
	err = dao.NewsTopicArticle.Ctx(ctx).Where(dao.NewsTopicArticle.Columns().TopicId, req.Id).Scan(&topicArticleData)
	if err != nil {
		return out, err
	}
	// 获取专题语言列表
	var topicLanguageList []entity.NewsTopicLanguage
	err = dao.NewsTopicLanguage.Ctx(ctx).Where(dao.NewsTopicLanguage.Columns().TopicId, req.Id).Scan(&topicLanguageList)
	if err != nil {
		return out, err
	}
	var nameArr = make([]v1.NewsTopicNameArrItem, 0)
	var articleArr = make([]v1.NewsTopicArticleArrItem, 0)
	// 提取所有的文章id
	articleIds := gutil.ListItemValuesUnique(topicArticleData, "ArticleId")
	// 根据文章id查询文章名称（当前语言）
	type articleName struct {
		ArticleId int    `json:"article_id"`
		Name      string `json:"name"`
	}
	var articleNameList []articleName
	err = dao.NewsArticleLanguage.Ctx(ctx).
		WhereIn(dao.NewsArticleLanguage.Columns().ArticleId, articleIds).
		Where(dao.NewsArticleLanguage.Columns().LanguageId, currentLang).
		Scan(&articleNameList)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return out, err
	}
	// 转成map
	articleNameMap := make(map[int]string)
	for _, v := range articleNameList {
		articleNameMap[v.ArticleId] = v.Name
	}
	for _, topicLanguage := range topicLanguageList {
		nameArr = append(nameArr, v1.NewsTopicNameArrItem{
			LanguageType: gconv.Int(topicLanguage.LanguageId),
			ShortName:    topicLanguage.ShortName,
			TopicName:    topicLanguage.Name,
		})
	}
	for _, topicArticle := range topicArticleData {
		articleArr = append(articleArr, v1.NewsTopicArticleArrItem{
			ArticleId:   topicArticle.ArticleId,
			ArticleName: articleNameMap[gconv.Int(topicArticle.ArticleId)],
		})
	}
	// 组装返回值
	out = &v1.NewsTopicInfoRes{
		Id: req.Id,
		NewsTopic: v1.NewsTopic{
			NameArr:    nameArr,
			TopicImgs:  topicData.TopicImgs,
			IsAppShow:  topicData.IsAppShow,
			Sort:       topicData.Sort,
			ArticleArr: articleArr,
		},
	}
	return out, nil
}

// 列表
func (s *sNewsTopic) List(ctx context.Context, req *v1.NewsTopicListReq) (out *v1.NewsTopicListRes, err error) {
	out = new(v1.NewsTopicListRes)
	// 获取语言
	currentLang := gconv.Uint(ctx.Value(consts.LanguageId))
	orm := dao.NewsTopic.Ctx(ctx).Where(dao.NewsTopic.Columns().DeleteTime, consts.Zero)
	if "" != req.TopicName {
		type topicLanguage struct {
			TopicId int `json:"topic_id"`
		}
		var topicLanguageList []*topicLanguage
		err = dao.NewsTopicLanguage.Ctx(ctx).
			WhereLike(dao.NewsTopicLanguage.Columns().Name, "%"+req.TopicName+"%").
			Scan(&topicLanguageList)
		if err != nil {
			return
		}
		topicIds := gutil.ListItemValuesUnique(topicLanguageList, "TopicId")
		if 0 < len(topicIds) {
			orm = orm.WhereIn(dao.NewsTopic.Columns().Id, topicIds)
		}
	}
	// 获取总数
	out.Total, err = orm.Count()
	if err != nil {
		out.List = []v1.NewsTopicListItem{}
		return out, err
	}
	if out.Total <= 0 {
		out.List = []v1.NewsTopicListItem{}
		return out, err
	}
	// 按照创建时间倒序
	orm = orm.OrderDesc(dao.NewsTopic.Columns().CreateTime)
	// 分页
	orm = orm.Page(req.Current, req.PageSize)
	var topicList []*entity.NewsTopic
	err = orm.Scan(&topicList)
	if err != nil {
		out.List = []v1.NewsTopicListItem{}
		return out, err
	}
	// 提取专题 id
	topicIds := gutil.ListItemValuesUnique(topicList, "Id")
	var topicLanguageList []*entity.NewsTopicLanguage
	err = dao.NewsTopicLanguage.Ctx(ctx).Where(dao.NewsTopicLanguage.Columns().TopicId, topicIds).Scan(&topicLanguageList)
	if err != nil {
		out.List = []v1.NewsTopicListItem{}
		return out, err
	}
	var topicLanguageListMap = make(map[uint]map[uint]*entity.NewsTopicLanguage)
	for _, topicLanguage := range topicLanguageList {
		if _, ok := topicLanguageListMap[topicLanguage.TopicId]; !ok {
			topicLanguageListMap[topicLanguage.TopicId] = make(map[uint]*entity.NewsTopicLanguage)
		}
		topicLanguageListMap[topicLanguage.TopicId][topicLanguage.LanguageId] = topicLanguage
	}
	for _, topic := range topicList {
		// 时间戳转成时间
		var createTime, topicName, shortName string
		if 0 < topic.CreateTime {
			createTime = time.Unix(topic.CreateTime/1000, 0).Format("2006-01-02 15:04:05")
		}
		// 展示当前语言的专题名称
		topicName = topicLanguageListMap[topic.Id][currentLang].Name
		shortName = topicLanguageListMap[topic.Id][currentLang].ShortName
		one := v1.NewsTopicListItem{
			Id:         topic.Id,
			TopicName:  topicName,
			ShortName:  shortName,
			Sort:       topic.Sort,
			ArticleNum: topic.Counts,
			CreateTime: createTime,
			IsAppShow:  gconv.Uint(topic.IsAppShow),
		}
		// 获取专题文章id
		type topicArticleListItem struct {
			ArticleId uint `v:"required" json:"article_id" dc:"文章id"`
		}
		var topicArticleList []topicArticleListItem
		err = dao.NewsTopicArticle.Ctx(ctx).Where(dao.NewsTopicArticle.Columns().TopicId, topic.Id).Scan(&topicArticleList)
		if err != nil {
			return
		}
		// 提取文章id
		articleIds := gutil.ListItemValuesUnique(topicArticleList, "ArticleId")
		// 获取文章收藏/分享/浏览数量
		one.CollectNum, err = dao.NewsArticleCollect.Ctx(ctx).Where(dao.NewsArticleCollect.Columns().ArticleId, articleIds).Count()
		if err != nil {
			return out, err
		}
		one.ShareNum, err = dao.NewsArticleShare.Ctx(ctx).Where(dao.NewsArticleShare.Columns().ArticleId, articleIds).Count()
		if err != nil {
			return out, err
		}
		one.ViewNum, err = dao.NewsArticleView.Ctx(ctx).Where(dao.NewsArticleView.Columns().ArticleId, articleIds).Count()
		if err != nil {
			return out, err
		}
		// 支持的语言
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Zero,
			LanguageTypeText: "ZH",
			IsSupport:        gconv.Int(topic.IsZh),
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.One,
			LanguageTypeText: "EN",
			IsSupport:        gconv.Int(topic.IsEn),
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Two,
			LanguageTypeText: "ID",
			IsSupport:        gconv.Int(topic.IsId),
		})
		out.List = append(out.List, one)
	}
	return out, nil
}

// 删除
func (s *sNewsTopic) Delete(ctx context.Context, req *v1.NewsTopicDeleteReq) error {
	// 参数校验
	if 1 > len(req.Ids) {
		return gerror.New("请选择要删除的专题")
	}
	// 把删除时间改成当前时间
	_, err := dao.NewsTopic.Ctx(ctx).
		WhereIn(dao.NewsTopic.Columns().Id, req.Ids).
		Data(g.Map{
			dao.NewsTopic.Columns().DeleteTime: time.Now().UnixMilli(),
		}).
		Update()
	return err
}
