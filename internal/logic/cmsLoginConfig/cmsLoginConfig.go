package cmsLoginConfig

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "gtcms/api/v1"
	dao "gtcms/internal/dao/admin"
	islamic_dao "gtcms/internal/dao/islamic_content_svc"
	islamic_entity "gtcms/internal/model/entity/islamic_content_svc"

	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"
	"os"
	"strconv"

	"gtcms/internal/service"
	"io"
	"log"
	"net/http"
	"time"
)

type (
	sCmsLoginConfig struct{}
)

func init() {
	service.RegisterCmsLoginConfig(New())
}

var cl = dao.CmsLoginConfig.Columns()

func New() service.ICmsLoginConfig {
	return &sCmsLoginConfig{}
}

func (s *sCmsLoginConfig) Edit(ctx context.Context, id uint, in *entity.CmsLoginConfig) (err error) {
	var oldData entity.CmsLoginConfig
	_ = dao.CmsLoginConfig.Ctx(ctx).WherePri(id).Scan(&oldData)

	admin, _ := service.Utility().GetSelf(ctx)
	in.UpdateAccount = admin.Account
	in.UpdateTime = time.Now().UnixMilli()

	_, err = dao.CmsLoginConfig.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Data(in).Update()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
			NewRecordOrAttr: in,
		})
	}

	return
}

// Detail 查询登录配置详情
func (s *sCmsLoginConfig) Detail(ctx context.Context) (out *v1.CmsLoginConfigDetailRes, err error) {
	err = dao.CmsLoginConfig.Ctx(ctx).Limit(1).Scan(&out)
	if out == nil {
		return nil, gerror.New("None config")
	}
	return
}

// DetailCache 查询登录配置详情(有缓存)
func (s *sCmsLoginConfig) DetailCache(ctx context.Context) (out *v1.CmsLoginConfigDetailRes, err error) {
	err = dao.CmsLoginConfig.Ctx(ctx).Limit(1).Scan(&out)
	if out == nil {
		return nil, gerror.New("None config")
	}
	return
}
func (s *sCmsLoginConfig) SynQuranJuz(ctx context.Context, id int) (out *v1.CmsLoginConfigDetailRes, err error) {

	//return
	// ------------- 主函数 -------------
	url := "http://api.alquran.cloud/v1/juz/" + gconv.String(id) + "/en.asad" // 替换成真实地址
	// 1. 构造客户端（带 10 秒超时）
	client := http.Client{Timeout: 50 * time.Second}

	// 2. 发起 GET 请求
	resp, err := client.Get(url)
	if err != nil {
		log.Fatalf("request failed: %v", err)
	}
	defer resp.Body.Close()

	// 3. 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("read body failed: %v", err)
	}

	// 4. 反序列化
	var data v1.Response
	if err := json.Unmarshal(body, &data); err != nil {
		log.Fatalf("unmarshal failed: %v", err)
	}
	//print(data.Data)
	for _, ayah := range data.Data.Ayahs {

		// 1. 取出所有键
		//keys := make([]int, 0, len(data.Data.Surahs))
		//for k := range data.Data.Surahs {
		//	keys = append(keys, k)
		//}
		//
		//// 2. 排序
		//sort.Ints(keys)

		// 3. 取最小键（第一个）
		//firstKey := keys[0]
		//// 4. 取最大键（最后一个）
		//lastKey := keys[len(keys)-1]
		//dataUpdate := &islamic_entity.SuratAyat{
		//	Juz:  ayah.Juz,
		//	Page: ayah.Page,
		//}
		_, err = islamic_dao.SuratAyat.Ctx(ctx).Where(islamic_dao.SuratAyat.Columns().SurahId, ayah.Surah.Number).Where(islamic_dao.SuratAyat.Columns().Nomor, ayah.NumberInSurah).Update(g.Map{
			"juz":  ayah.Juz,
			"page": ayah.Page,
		})
		if err != nil {
			return nil, gerror.New("Error")
		}
		time.Sleep(10 * time.Millisecond) // 避免请求过快
	}

	return
}

func (s *sCmsLoginConfig) SynTahlil(ctx context.Context) (out *v1.CmsLoginConfigDetailRes, err error) {

	// 打开HTML文件
	f, err := os.Open("/Users/<USER>/code/tahlil.html")
	if err != nil {
		log.Fatal(err)
	}
	defer f.Close()

	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(f)
	if err != nil {
		log.Fatal(err)
	}

	//text := doc.Find("div.flex.flex-col.space-y-3 span").First().Text()
	//fmt.Println(text)

	// 获取指定div内容，例如class="mydiv"
	var result1 []string
	var result2 []string
	var result3 []string
	doc.Find("div.flex.flex-col.space-y-3").Each(func(i int, s *goquery.Selection) {
		spans := s.Find("span")
		result1 = append(result1, spans.Eq(0).Text())
		result2 = append(result2, spans.Eq(1).Text())
		result3 = append(result2, spans.Eq(2).Text())
	})
	// 输出结果
	fmt.Println(result1)
	fmt.Println(result2)
	fmt.Println(result3)
	// 将结果存储到数据库
	for k, text := range result1 {
		tahlil := &islamic_entity.NewsTahlil{
			Name:     "Tahlil",
			Content1: text,
			Content2: result2[k],
			Content3: result3[k],
		}
		_, err = islamic_dao.NewsTahlil.Ctx(ctx).Data(tahlil).Insert()
		if err != nil {
			return nil, gerror.New("Error inserting Tahlil content")
		}
	}
	return
}

func (s *sCmsLoginConfig) SynDoa(ctx context.Context, id int) (out *v1.CmsLoginConfigDetailRes, err error) {

	// 打开HTML文件
	f, err := os.Open("/home/<USER>/下载/backup/doa/" + strconv.Itoa(id) + ".html")
	if err != nil {
		log.Fatal(err)
	}
	defer f.Close()

	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(f)
	if err != nil {
		log.Fatal(err)
	}
	//标题查询 没有的就新建数据

	title := doc.Find("div.text-center.space-y-2.mb-5 h1").Text()
	numbers := doc.Find("div.text-center.space-y-2.mb-5 span").Text()
	title = gstr.Replace(title, "Kumpulan ", "")
	parts := gstr.Split(numbers, " ")
	if len(parts) > 0 {
		numbers = parts[0]
	}
	doaId := 0
	newsDoa := &islamic_entity.NewsDoa{}
	islamic_dao.NewsDoa.Ctx(ctx).Where(islamic_dao.NewsDoa.Columns().Name, title).Scan(&newsDoa)
	if newsDoa.Id == 0 {
		newsDoa = &islamic_entity.NewsDoa{
			Name:    title,
			Bacaans: uint(gconv.Int(numbers)),
		}
		id, err := islamic_dao.NewsDoa.Ctx(ctx).Data(newsDoa).InsertAndGetId()
		if err != nil {
			return nil, gerror.New("Error inserting NewsDoa content")
		}
		doaId = int(id)
	} else {
		doaId = int(newsDoa.Id)
	}

	doc.Find("div.text-center.mt-10.mb-6").Each(func(i int, s *goquery.Selection) {
		spans := s.Find("span").Text()
		h1 := s.Find("h1").Text()
		tahlil := &islamic_entity.NewsDoaBacaan{
			DoaId:    uint(doaId),
			Name:     h1,
			Content1: spans,
			Content2: "",
			Content3: "",
			Pid:      0,
		}
		//print(tahlil)
		pid, _ := islamic_dao.NewsDoaBacaan.Ctx(ctx).Data(tahlil).InsertAndGetId()

		//next level
		siblings := s.NextAll()
		siblings.EachWithBreak(func(i int, s *goquery.Selection) bool {
			if s.Is("div.text-center.mt-10.mb-6") {
				return false // 结束循环
			}
			tahlil1 := &islamic_entity.NewsDoaBacaan{
				DoaId:    uint(doaId),
				Pid:      int(pid),
				Name:     "",
				Content1: "",
				Content2: "",
				Content3: "",
			}

			//如果是红色的span  单独存一条
			//text-center __className_d15661
			contentRedspan := s.Find("span.__className_d15661").Text()
			if contentRedspan != "" {
				tahlil1.Name = contentRedspan
			} else {
				// 获取下一个兄弟元素的内容
				content1 := s.Find("span.__className_6952f9").Text()
				if content1 != "" {
					tahlil1.Content1 = content1
				}

				content2 := s.Find("span.block.text-md").Eq(0).Text()
				if content2 != "" {
					tahlil1.Content2 = content2
				}

				content3 := s.Find("span.block.text-md").Eq(1).Text()
				if content3 != "" {
					tahlil1.Content3 = content3
				}

			}
			//print(tahlil1)
			_, err = islamic_dao.NewsDoaBacaan.Ctx(ctx).Data(tahlil1).Insert()
			return true // 继续循环
		})

	})

	return
}

func (s *sCmsLoginConfig) SynWirid(ctx context.Context, id int) (out *v1.CmsLoginConfigDetailRes, err error) {

	// 打开HTML文件
	f, err := os.Open("/home/<USER>/下载/backup/wirid/" + strconv.Itoa(id) + ".html")
	if err != nil {
		log.Fatal(err)
	}
	defer f.Close()

	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(f)
	if err != nil {
		log.Fatal(err)
	}
	//标题查询 没有的就新建数据

	title := doc.Find("div.text-center.space-y-2.mb-5 h1").Text()
	numbers := doc.Find("div.text-center.space-y-2.mb-5 span").Text()
	title = gstr.Replace(title, "Kumpulan ", "")
	parts := gstr.Split(numbers, " ")
	if len(parts) > 0 {
		numbers = parts[0]
	}
	doaId := 0
	newsDoa := &islamic_entity.NewsWirid{}
	islamic_dao.NewsWirid.Ctx(ctx).Where(islamic_dao.NewsWirid.Columns().Name, title).Scan(&newsDoa)
	if newsDoa.Id == 0 {
		newsDoa = &islamic_entity.NewsWirid{
			Name:    title,
			Bacaans: uint(gconv.Int(numbers)),
		}
		id, err := islamic_dao.NewsWirid.Ctx(ctx).Data(newsDoa).InsertAndGetId()
		if err != nil {
			return nil, gerror.New("Error inserting NewsDoa content")
		}
		doaId = int(id)
	} else {
		doaId = int(newsDoa.Id)
	}

	doc.Find("div.text-center.mt-10.mb-6").Each(func(i int, s *goquery.Selection) {
		spans := s.Find("span").Text()
		h1 := s.Find("h1").Text()
		tahlil := &islamic_entity.NewsWiridBacaan{
			WiridId:  uint(doaId),
			Name:     h1,
			Content1: spans,
			Content2: "",
			Content3: "",
		}
		//print(tahlil)
		pid, _ := islamic_dao.NewsWiridBacaan.Ctx(ctx).Data(tahlil).InsertAndGetId()

		//next level
		siblings := s.NextAll()
		siblings.EachWithBreak(func(i int, s *goquery.Selection) bool {
			if s.Is("div.text-center.mt-10.mb-6") {
				return false // 结束循环
			}
			tahlil1 := &islamic_entity.NewsWiridBacaan{
				WiridId:  uint(doaId),
				Pid:      int(pid),
				Name:     "",
				Content1: "",
				Content2: "",
				Content3: "",
			}

			//如果是红色的span  单独存一条
			//text-center __className_d15661
			contentRedspan := s.Find("span.__className_d15661").Text()
			if contentRedspan != "" {
				tahlil1.Name = contentRedspan
			} else {
				// 获取下一个兄弟元素的内容
				content1 := s.Find("span.__className_6952f9").Text()
				if content1 != "" {
					tahlil1.Content1 = content1
				}

				content2 := s.Find("span.block.text-md").Eq(0).Text()
				if content2 != "" {
					tahlil1.Content2 = content2
				}

				content3 := s.Find("span.block.text-md").Eq(1).Text()
				if content3 != "" {
					tahlil1.Content3 = content3
				}
			}
			//print(tahlil1)
			_, err = islamic_dao.NewsWiridBacaan.Ctx(ctx).Data(tahlil1).Insert()
			return true // 继续循环
		})

	})

	return
}
