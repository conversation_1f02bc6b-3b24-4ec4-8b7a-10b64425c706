package Module

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	_ "gtcms/internal/logic/utility"

	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
)

var ctx = context.Background()

type sModule struct{}

func init() {
	service.RegisterModule(New())
}

func New() *sModule {
	s := &sModule{}
	//g.Go(ctx, func(ctx context.Context) {
	//	s.initData()
	//}, nil)
	return s
}

// 初始化后，作为栏目的数据源
func (s *sModule) initData() {
	rs, err := g.Redis().Do(ctx, "GET", consts.InitModule)
	if err != nil {
		g.Log().Error(ctx, err)
		return
	}
	if !rs.IsNil() {
		//return
	}

	// 初始预设模块
	//var moduleList []entity.Module
	//
	//// 读取所有足球联赛，将联赛名称作为模块名称
	//var fbList []entity.CollectFootballCompetition
	//err = dao.CollectFootballCompetition.Ctx(ctx).Scan(&fbList)
	//if err != nil {
	//	g.Log().Error(ctx, err)
	//	return
	//}
	//
	//// 读取所有篮球联赛，将联赛名称作为模块名称
	//var bkList []entity.CollectBasketballCompetition
	//err = dao.CollectBasketballCompetition.Ctx(ctx).Scan(&bkList)
	//if err != nil {
	//	g.Log().Error(ctx, err)
	//	return
	//}
	//
	//for _, v := range fbList {
	//	module := entity.Module{
	//		Id:   consts.SportTypeFootball + gconv.String(v.Id),
	//		Name: utility.If(v.ShortNameZh == "", v.NameZh, v.ShortNameZh),
	//	}
	//	moduleList = append(moduleList, module)
	//}
	//for _, v := range bkList {
	//	module := entity.Module{
	//		Id:   consts.SportTypeBasketball + gconv.String(v.Id),
	//		Name: utility.If(v.ShortNameZh == "", v.NameZh, v.ShortNameZh),
	//	}
	//	moduleList = append(moduleList, module)
	//}
	//
	//if len(moduleList) == 0 {
	//	return
	//}
	//_, err = dao.Module.Ctx(ctx).Cache(gdb.CacheOption{
	//	Duration: -1,
	//	Name:     consts.ModuleOptions,
	//	Force:    false, // nil也缓存
	//}).Replace(moduleList)
	//if err != nil {
	//	g.Log().Error(ctx, err)
	//	return
	//}
	//
	//_, err = g.Redis().Do(ctx, "SET", consts.InitModule, 1)
	//if err != nil {
	//	g.Log().Error(ctx, err)
	//	return
	//}
}

func (s *sModule) List(ctx context.Context, req *v1.ModuleListReq) (res *v1.ModuleListRes, err error) {
	res = new(v1.ModuleListRes)
	res.Current = req.Current
	res.List = make([]entity.Module, 0)

	m := dao.Module.Ctx(ctx)
	if req.IdLess != nil {
		m = m.WhereLT(dao.Module.Columns().Id, req.IdLess)
	}

	total, err := m.Count()
	if err != nil {
		return
	}
	if total < 1 {
		return
	}
	if len(req.OrderBy) > 0 {
		m = m.Order(req.OrderBy)
	} else {
		m = m.OrderDesc(dao.Module.Table() + "." + dao.Module.Columns().Id) // 最新在前面
	}

	if req.Current > 0 && req.PageSize > 0 {
		m = m.Page(req.Current, req.PageSize)
		res.List = make([]entity.Module, 0, req.PageSize)
	} else {
		res.List = make([]entity.Module, 0, total)
	}

	err = m.Scan(&res.List)
	if err != nil {
		return
	}

	res.Total = total
	return
}

func (s *sModule) Add(ctx context.Context, req *v1.ModuleAddReq) (res *v1.ModuleAddRes, err error) {
	res = new(v1.ModuleAddRes)

	var entityData *entity.Module
	if err = gconv.Scan(req, &entityData); err != nil {
		return
	}

	id, err := dao.Module.Ctx(ctx).InsertAndGetId(entityData)
	if err != nil {
		return
	}

	res = &v1.ModuleAddRes{
		Id: uint(id),
	}
	return
}

func (s *sModule) Edit(ctx context.Context, req *v1.ModuleEditReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)

	update := g.Map{}
	if req.Name != nil {
		update[dao.Module.Columns().Name] = req.Name
	}

	if _, err = dao.Module.Ctx(ctx).Where(dao.Module.Columns().Id, req.Id).Update(update); err != nil {
		return
	}

	return
}

func (s *sModule) Delete(ctx context.Context, req *v1.ModuleDeleteReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)

	if _, err = dao.Module.Ctx(ctx).Where(dao.Module.Columns().Id, req.Ids).Delete(); err != nil {
		return
	}
	return
}

func (s *sModule) Options(ctx context.Context, req *v1.ModuleOptionsReq) (options []model.SelectOption, err error) {
	options = make([]model.SelectOption, 0)
	if err = dao.Module.Ctx(ctx).Fields("name as label,id as value").Scan(&options); err != nil {
		return
	}
	return
}

func (s *sModule) One(ctx context.Context, id string) (item *entity.Module, err error) {
	sg, err := dao.Module.Ctx(ctx).Where(dao.Module.Columns().Id, id).One()
	if err != nil {
		return
	}

	if err = gconv.Scan(sg, &item); err != nil {
		return
	}
	return
}
