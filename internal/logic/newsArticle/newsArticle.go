package newsArticle

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"

	dao "gtcms/internal/dao/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"

	"gtcms/internal/service"
	"time"
)

type sNewsArticle struct{}

func init() {
	service.RegisterNewsArticle(New())
}

func New() *sNewsArticle {
	return &sNewsArticle{}
}

// 新增
func (s *sNewsArticle) Add(ctx context.Context, req *v1.NewsArticleAddReq) error {
	// 参数校验
	if 0 >= req.CategoryId {
		return gerror.New("分类id不能为空")
	}
	if "" == req.CoverImgs {
		return gerror.New("封面图不能为空")
	}
	// 获取分类信息
	var category entity.NewsCategory
	err := dao.NewsCategory.Ctx(ctx).Where(dao.NewsCategory.Columns().Id, req.CategoryId).Scan(&category)
	if err != nil {
		return err
	}
	if 0 >= category.Id {
		return gerror.New("分类不存在")
	}
	// 判断状态
	if consts.One != category.Status {
		return gerror.New("所选分类已停用，请重新选择")
	}
	admin, _ := service.Utility().GetSelf(ctx)
	currentTime := time.Now().UnixMilli()
	articleData := entity.NewsArticle{
		CategoryId:  req.CategoryId,
		AdminId:     category.AdminId, // 分类负责人id
		CoverImgs:   req.CoverImgs,
		Creater:     admin.Id,
		CreateName:  admin.Account,
		Author:      req.Author,
		IsTop:       req.IsTop,
		IsRecommend: req.IsRecommend,
		IsPublish:   req.IsPublish,
		IsDraft:     req.IsDraft,
		CreateTime:  currentTime,
		UpdateTime:  currentTime,
	}
	// 如果有发布时间，则设置发布时间，没有就设置当前时间
	if "" != req.PublishTime {
		// 转换为时间戳
		publishTime, _ := time.ParseInLocation("2006-01-02 15:04:05", req.PublishTime, time.Local)
		articleData.PublishTime = publishTime.UnixMilli()
	}
	var articleLanguageData = make([]entity.NewsArticleLanguage, 0)
	// 校验文章名称和内容
	for _, item := range req.ContentArr {
		if "" == item.Content {
			return gerror.New("内容不能为空")
		}
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return gerror.New("语言类型错误")
		}
		one := entity.NewsArticleLanguage{
			Name:       item.ArticleName,
			Content:    item.Content,
			CreateTime: currentTime,
			LanguageId: gconv.Uint(item.LanguageType),
		}
		if consts.Zero == item.LanguageType {
			articleData.IsZh = consts.One
		}
		if consts.One == item.LanguageType {
			articleData.IsEn = consts.One
		}
		if consts.Two == item.LanguageType {
			articleData.IsId = consts.One
		}
		articleLanguageData = append(articleLanguageData, one)
	}
	// 写入表
	err = dao.NewsArticle.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		id, err1 := dao.NewsArticle.Ctx(ctx).Data(articleData).InsertAndGetId()
		if err1 != nil {
			return err1
		}
		// 组装数据
		for key := range articleLanguageData {
			articleLanguageData[key].ArticleId = gconv.Uint(id)
		}
		// 批量插入
		_, err1 = dao.NewsArticleLanguage.Ctx(ctx).Data(articleLanguageData).Insert()
		return err1
	})
	return nil
}

// 编辑
func (s *sNewsArticle) Edit(ctx context.Context, req *v1.NewsArticleEditReq) error {
	// 校验参数
	if 0 >= req.Id {
		return gerror.New("文章id不能为空")
	}
	// 获取分类信息
	var category entity.NewsCategory
	err := dao.NewsCategory.Ctx(ctx).Where(dao.NewsCategory.Columns().Id, req.CategoryId).Scan(&category)
	if err != nil {
		return err
	}
	if 0 >= category.Id {
		return gerror.New("分类不存在")
	}
	// 判断状态
	if consts.One != category.Status {
		return gerror.New("所选分类已停用，请重新选择")
	}
	// 获取文章信息
	var article entity.NewsArticle
	err = dao.NewsArticle.Ctx(ctx).Where(dao.NewsArticle.Columns().Id, req.Id).Scan(&article)
	if err != nil {
		return err
	}
	if 0 >= article.Id {
		return gerror.New("文章不存在")
	}
	admin, _ := service.Utility().GetSelf(ctx)
	currentTime := time.Now().UnixMilli()
	articleData := g.Map{
		dao.NewsArticle.Columns().CategoryId:  req.CategoryId,
		dao.NewsArticle.Columns().AdminId:     category.AdminId, // 分类负责人id
		dao.NewsArticle.Columns().CoverImgs:   req.CoverImgs,
		dao.NewsArticle.Columns().Creater:     admin.Id,
		dao.NewsArticle.Columns().CreateName:  admin.Account,
		dao.NewsArticle.Columns().Author:      req.Author,
		dao.NewsArticle.Columns().IsTop:       req.IsTop,
		dao.NewsArticle.Columns().IsRecommend: req.IsRecommend,
		dao.NewsArticle.Columns().IsPublish:   req.IsPublish,
		dao.NewsArticle.Columns().IsDraft:     req.IsDraft,
		dao.NewsArticle.Columns().CreateTime:  article.CreateTime,
		dao.NewsArticle.Columns().UpdateTime:  currentTime,
		dao.NewsArticle.Columns().IsZh:        consts.Zero,
		dao.NewsArticle.Columns().IsEn:        consts.Zero,
		dao.NewsArticle.Columns().IsId:        consts.Zero,
	}
	var articleLanguageData = make([]entity.NewsArticleLanguage, 0)
	// 校验文章名称和内容
	for _, item := range req.ContentArr {
		if "" == item.Content {
			return gerror.New("内容不能为空")
		}
		if consts.Zero > item.LanguageType || consts.Two < item.LanguageType {
			return gerror.New("语言类型错误")
		}
		one := entity.NewsArticleLanguage{
			Name:       item.ArticleName,
			Content:    item.Content,
			CreateTime: article.CreateTime,
			UpdateTime: currentTime,
			LanguageId: gconv.Uint(item.LanguageType),
		}
		if consts.Zero == item.LanguageType {
			articleData[dao.NewsArticle.Columns().IsZh] = consts.One
		}
		if consts.One == item.LanguageType {
			articleData[dao.NewsArticle.Columns().IsEn] = consts.One
		}
		if consts.Two == item.LanguageType {
			articleData[dao.NewsArticle.Columns().IsId] = consts.One
		}
		articleLanguageData = append(articleLanguageData, one)
	}
	// 写入表
	err = dao.NewsArticle.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		_, err1 = dao.NewsArticle.Ctx(ctx).Where(dao.NewsArticle.Columns().Id, req.Id).Data(articleData).Update()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.NewsArticleLanguage.Ctx(ctx).Where(dao.NewsArticleLanguage.Columns().ArticleId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		// 组装数据
		for key := range articleLanguageData {
			articleLanguageData[key].ArticleId = gconv.Uint(req.Id)
		}
		// 批量插入
		_, err1 = dao.NewsArticleLanguage.Ctx(ctx).Data(articleLanguageData).Insert()
		return err1
	})
	return err
}

// 列表
func (s *sNewsArticle) List(ctx context.Context, req *v1.NewsArticleListReq) (out *v1.NewsArticleListRes, err error) {
	out = new(v1.NewsArticleListRes)
	out.Current = req.Current
	out.Offset = req.Offset
	// 获取orm
	orm, err := s.getListCond(ctx, req)
	if err != nil {
		return out, err
	}
	// 总数
	out.Total, err = orm.Count()
	if err != nil {
		return out, err
	}
	// 为空直接返回
	if out.Total == 0 {
		out.List = []v1.NewsArticleListItem{}
		return out, nil
	}
	// 分页
	orm = orm.Page(req.Current, req.PageSize)
	// 排序，按照发布时间倒序
	orm = orm.OrderDesc(dao.NewsArticle.Columns().PublishTime)
	// 查询数据
	var articleList []*entity.NewsArticle
	err = orm.Scan(&articleList)
	if err != nil {
		return out, err
	}
	// 提取文章id
	articleIds := gutil.ListItemValuesUnique(articleList, "Id")
	// 提取文章分类id
	categoryIds := gutil.ListItemValuesUnique(articleList, "CategoryId")
	// 查询文章分类
	type category struct {
		CategoryId int    `json:"category_id"`
		LanguageId int    `json:"language_id"`
		Name       string `json:"name"`
	}
	var categoryList []*category
	err = dao.NewsCategoryLanguage.Ctx(ctx).WhereIn(dao.NewsCategoryLanguage.Columns().CategoryId, categoryIds).Scan(&categoryList)
	if err != nil {
		return out, err
	}
	var categoryMap = make(map[int]map[int]string)
	for _, v := range categoryList {
		if _, ok := categoryMap[v.CategoryId]; !ok {
			categoryMap[v.CategoryId] = make(map[int]string)
		}
		categoryMap[v.CategoryId][v.LanguageId] = v.Name
	}
	// 查询文章名称
	type articleLanguage struct {
		ArticleId  int    `json:"article_id"`
		LanguageId int    `json:"language_id"`
		Name       string `json:"name"`
	}
	var articleLanguageList []*articleLanguage
	err = dao.NewsArticleLanguage.Ctx(ctx).WhereIn(dao.NewsArticleLanguage.Columns().ArticleId, articleIds).Scan(&articleLanguageList)
	if err != nil {
		return
	}
	articleLanguageMap := make(map[int]map[int]string)
	for _, v := range articleLanguageList {
		if _, ok := articleLanguageMap[v.ArticleId]; !ok {
			articleLanguageMap[v.ArticleId] = make(map[int]string)
		}
		articleLanguageMap[v.ArticleId][v.LanguageId] = v.Name
	}
	// 获取语言
	currentLang := gconv.Int(ctx.Value(consts.LanguageId))
	// 计算序号开始值
	serialNumber := (req.Current-1)*req.PageSize + 1
	// 组装数据
	for _, v := range articleList {
		// 将int转成日期格式，时间戳是13位，需要除1000
		var createTime, publishTime string
		if 0 < v.PublishTime {
			publishTime = time.Unix(v.PublishTime/1000, 0).Format("2006-01-02 15:04:05")
		}
		if 0 < v.CreateTime {
			createTime = time.Unix(v.CreateTime/1000, 0).Format("2006-01-02 15:04:05")
		}
		article := v1.NewsArticleListItem{
			Id:          v.Id,
			CategoryId:  v.CategoryId,
			IsPublish:   gconv.Int(v.IsPublish),
			IsTop:       gconv.Int(v.IsTop),
			IsRecommend: gconv.Int(v.IsRecommend),
			CreateTime:  createTime,
			PublishTime: publishTime,
		}
		// 获取收藏数量/浏览数量/分享数量
		article.CollectNum, err = dao.NewsArticleCollect.Ctx(ctx).Where(dao.NewsArticleCollect.Columns().ArticleId, v.Id).Count()
		if err != nil {
			return out, err
		}
		article.ShareNum, err = dao.NewsArticleShare.Ctx(ctx).Where(dao.NewsArticleShare.Columns().ArticleId, v.Id).Count()
		if err != nil {
			return out, err
		}
		article.ViewNum, err = dao.NewsArticleView.Ctx(ctx).Where(dao.NewsArticleView.Columns().ArticleId, v.Id).Count()
		if err != nil {
			return out, err
		}
		// 支持的语言
		article.LanguageArr = append(article.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Zero,
			LanguageTypeText: "ZH",
			IsSupport:        gconv.Int(v.IsZh),
		})
		article.LanguageArr = append(article.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.One,
			LanguageTypeText: "EN",
			IsSupport:        gconv.Int(v.IsEn),
		})
		article.LanguageArr = append(article.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Two,
			LanguageTypeText: "ID",
			IsSupport:        gconv.Int(v.IsId),
		})
		// 展示当前语言对应的文章名称
		article.ArticleName = articleLanguageMap[gconv.Int(v.Id)][currentLang]
		article.CategoryName = categoryMap[gconv.Int(v.CategoryId)][currentLang]
		article.ArticleStatus = gconv.Int(v.IsPublish)
		article.ArticleStatusText = consts.GetArticleStatusText(gconv.Int(v.IsPublish))
		article.IsTopText = consts.GetArticleIsTopText(gconv.Int(v.IsTop))
		article.IsRecommendText = consts.GetArticleIsRecommendText(gconv.Int(v.IsRecommend))
		article.SerialNum = serialNumber
		out.List = append(out.List, article)
		serialNumber++
	}
	return out, nil
}

// 获取列表查询orm
func (s sNewsArticle) getListCond(ctx context.Context, req *v1.NewsArticleListReq) (*gdb.Model, error) {
	orm := dao.NewsArticle.Ctx(ctx).
		Where(dao.NewsArticle.Columns().DeleteTime, consts.Zero).
		Where(dao.NewsArticle.Columns().IsDraft, consts.Zero)
	// 文章名称模糊查询
	if "" != req.ArticleName {
		type articleLanguage struct {
			ArticleId int `json:"article_id"`
		}
		var articleLanguageList []articleLanguage
		err := dao.NewsArticleLanguage.Ctx(ctx).
			Distinct().
			Fields(dao.NewsArticleLanguage.Columns().ArticleId).
			WhereLike(dao.NewsArticleLanguage.Columns().Name, "%"+req.ArticleName+"%").
			Scan(&articleLanguageList)
		if err != nil {
			return orm, err
		}
		articleIds := gutil.ListItemValuesUnique(articleLanguageList, "ArticleId")
		if 0 < len(articleIds) {
			orm = orm.WhereIn(dao.NewsArticle.Columns().Id, articleIds)
		}
	}
	// 分类id
	if 0 < req.CategoryId {
		orm = orm.Where(dao.NewsArticle.Columns().CategoryId, req.CategoryId)
	}
	// 创建时间
	if "" != req.CreateTimeBegin && "" != req.CreateTimeEnd {
		// 转成时间戳
		createTimeBegin := gtime.NewFromStr(req.CreateTimeBegin).TimestampStr()
		createTimeEnd := gtime.NewFromStr(req.CreateTimeEnd).TimestampStr()
		// 判断时间范围是否合法
		if createTimeBegin > createTimeEnd {
			return nil, gerror.New("开始时间不能大于结束时间")
		}
		orm = orm.WhereBetween(dao.NewsArticle.Columns().CreateTime, createTimeBegin, createTimeEnd)
	}
	// 加入头条时间
	if "" != req.TopTimeBegin && "" != req.TopTimeEnd {
		// 转成时间戳
		topTimeBegin := gtime.NewFromStr(req.TopTimeBegin).TimestampStr()
		topTimeEnd := gtime.NewFromStr(req.TopTimeEnd).TimestampStr()
		// 判断时间范围是否合法
		if topTimeBegin > topTimeEnd {
			return nil, gerror.New("开始时间不能大于结束时间")
		}
		orm = orm.WhereBetween(dao.NewsArticle.Columns().CreateTime, topTimeBegin, topTimeEnd)
	}
	// 加入推荐时间
	if "" != req.RecommendTimeBegin && "" != req.RecommendTimeEnd {
		// 转成时间戳
		recommendTimeBegin := gtime.NewFromStr(req.RecommendTimeBegin).TimestampStr()
		recommendTimeEnd := gtime.NewFromStr(req.RecommendTimeEnd).TimestampStr()
		// 判断时间范围是否合法
		if recommendTimeBegin > recommendTimeEnd {
			return nil, gerror.New("开始时间不能大于结束时间")
		}
		orm = orm.WhereBetween(dao.NewsArticle.Columns().CreateTime, recommendTimeBegin, recommendTimeEnd)
	}
	// 发布状态
	if 0 <= req.IsPublish {
		orm = orm.Where(dao.NewsArticle.Columns().IsPublish, req.IsPublish)
	}
	// 推荐
	if 0 <= req.IsRecommend {
		orm = orm.Where(dao.NewsArticle.Columns().IsRecommend, req.IsRecommend)
	}
	// 头条
	if 0 <= req.IsTop {
		orm = orm.Where(dao.NewsArticle.Columns().IsTop, req.IsTop)
	}
	return orm, nil
}

// 详情
func (s *sNewsArticle) Info(ctx context.Context, req *v1.NewsArticleInfoReq) (out *v1.NewsArticleInfoRes, err error) {
	out = &v1.NewsArticleInfoRes{}
	// 参数校验
	if 0 >= req.Id {
		return out, gerror.New("参数错误")
	}
	var article *entity.NewsArticle
	err = dao.NewsArticle.Ctx(ctx).Where(dao.NewsArticle.Columns().Id, req.Id).Scan(&article)
	if err != nil {
		return
	}
	var articleLanguage []*entity.NewsArticleLanguage
	err = dao.NewsArticleLanguage.Ctx(ctx).Where(dao.NewsArticleLanguage.Columns().ArticleId, req.Id).Scan(&articleLanguage)
	if err != nil {
		return
	}
	var contentArr = make([]v1.ContentItem, 0)
	for _, v := range articleLanguage {
		contentArr = append(contentArr, v1.ContentItem{
			LanguageType: gconv.Int(v.LanguageId),
			ArticleName:  v.Name,
			Content:      v.Content,
		})
	}
	// 将int转成日期格式
	var publishTime string
	if 0 < article.PublishTime {
		publishTime = time.Unix(article.PublishTime, 0).Format("2006-01-02 15:04:05")
	}
	// 组装返回值
	out = &v1.NewsArticleInfoRes{
		NewArticle: v1.NewArticle{
			Author:      article.Author,
			CategoryId:  article.CategoryId,
			ContentArr:  contentArr,
			CoverImgs:   article.CoverImgs,
			IsDraft:     article.IsDraft,
			IsPublish:   article.IsPublish,
			IsRecommend: article.IsRecommend,
			IsTop:       article.IsTop,
			PublishTime: publishTime,
		},
	}
	return out, err
}

// 删除
func (s *sNewsArticle) Delete(ctx context.Context, req *v1.NewsArticleDeleteReq) error {
	// 校验参数
	if 1 > len(req.Ids) {
		return gerror.New("请选择要删除的文章")
	}
	// 删除
	err := dao.NewsArticle.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := dao.NewsArticle.Ctx(ctx).WhereIn(dao.NewsArticle.Columns().Id, req.Ids).Data(g.Map{
			dao.NewsArticle.Columns().DeleteTime: time.Now().UnixMilli(),
			dao.NewsArticle.Columns().UpdateTime: time.Now().UnixMilli(),
		}).Update()
		if err != nil {
			return err
		}
		_, err = dao.NewsArticleLanguage.Ctx(ctx).
			WhereIn(dao.NewsArticleLanguage.Columns().ArticleId, req.Ids).
			Data(g.Map{
				dao.NewsArticleLanguage.Columns().DeleteTime: time.Now().UnixMilli(),
				dao.NewsArticleLanguage.Columns().UpdateTime: time.Now().UnixMilli(),
			}).Update()
		return err
	})
	return err
}

// 批量下线
func (s *sNewsArticle) SetOffline(ctx context.Context, req *v1.NewsArticleSetOfflineReq) error {
	// 校验参数
	if 1 > len(req.Ids) {
		return gerror.New("请选择要下线的文章")
	}
	// 下线
	err := dao.NewsArticle.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := dao.NewsArticle.Ctx(ctx).WhereIn(dao.NewsArticle.Columns().Id, req.Ids).Data(g.Map{
			dao.NewsArticle.Columns().IsPublish:  consts.Two,
			dao.NewsArticle.Columns().IsDraft:    consts.Zero,
			dao.NewsArticle.Columns().UpdateTime: time.Now().UnixMilli(),
		}).Update()
		return err
	})
	return err
}

// 批量上线
func (s *sNewsArticle) SetOnline(ctx context.Context, req *v1.NewsArticleSetOnlineReq) error {
	// 校验参数
	if 1 > len(req.Ids) {
		return gerror.New("请选择要上线的文章")
	}
	// 下线
	err := dao.NewsArticle.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := dao.NewsArticle.Ctx(ctx).WhereIn(dao.NewsArticle.Columns().Id, req.Ids).Data(g.Map{
			dao.NewsArticle.Columns().IsPublish:   consts.One,
			dao.NewsArticle.Columns().IsDraft:     consts.Zero,
			dao.NewsArticle.Columns().UpdateTime:  time.Now().UnixMilli(),
			dao.NewsArticle.Columns().PublishTime: time.Now().UnixMilli(),
		}).Update()
		return err
	})
	return err
}

// 批量加入头条
func (s *sNewsArticle) SetTop(ctx context.Context, req *v1.NewsArticleSetTopReq) error {
	// 校验参数
	if 1 > len(req.Ids) {
		return gerror.New("请选择要加入头条的文章")
	}
	// 下线
	err := dao.NewsArticle.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := dao.NewsArticle.Ctx(ctx).WhereIn(dao.NewsArticle.Columns().Id, req.Ids).Data(g.Map{
			dao.NewsArticle.Columns().IsTop:      consts.One,
			dao.NewsArticle.Columns().UpdateTime: time.Now().UnixMilli(),
			dao.NewsArticle.Columns().TopTime:    time.Now().UnixMilli(),
		}).Update()
		return err
	})
	return err
}

// 批量取消加入头条
func (s *sNewsArticle) SetNotTop(ctx context.Context, req *v1.NewsArticleSetNotTopReq) error {
	// 校验参数
	if 1 > len(req.Ids) {
		return gerror.New("请选择要取消加入头条的文章")
	}
	// 下线
	err := dao.NewsArticle.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := dao.NewsArticle.Ctx(ctx).WhereIn(dao.NewsArticle.Columns().Id, req.Ids).Data(g.Map{
			dao.NewsArticle.Columns().IsTop:      consts.Zero,
			dao.NewsArticle.Columns().UpdateTime: time.Now().UnixMilli(),
			dao.NewsArticle.Columns().TopTime:    consts.Zero,
		}).Update()
		return err
	})
	return err
}

// 批量推荐
func (s *sNewsArticle) SetRecommend(ctx context.Context, req *v1.NewsArticleSetRecommendReq) error {
	// 校验参数
	if 1 > len(req.Ids) {
		return gerror.New("请选择要加入推荐的文章")
	}
	// 下线
	err := dao.NewsArticle.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := dao.NewsArticle.Ctx(ctx).WhereIn(dao.NewsArticle.Columns().Id, req.Ids).Data(g.Map{
			dao.NewsArticle.Columns().IsRecommend:   consts.One,
			dao.NewsArticle.Columns().UpdateTime:    time.Now().UnixMilli(),
			dao.NewsArticle.Columns().RecommendTime: time.Now().UnixMilli(),
		}).Update()
		return err
	})
	return err
}

// 批量不推荐
func (s *sNewsArticle) SetNotRecommend(ctx context.Context, req *v1.NewsArticleSetNotRecommendReq) error {
	// 校验参数
	if 1 > len(req.Ids) {
		return gerror.New("请选择要取消推荐的文章")
	}
	// 下线
	err := dao.NewsArticle.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err := dao.NewsArticle.Ctx(ctx).WhereIn(dao.NewsArticle.Columns().Id, req.Ids).Data(g.Map{
			dao.NewsArticle.Columns().IsRecommend:   consts.Zero,
			dao.NewsArticle.Columns().UpdateTime:    time.Now().UnixMilli(),
			dao.NewsArticle.Columns().RecommendTime: consts.Zero,
		}).Update()
		return err
	})
	return err
}

// 草稿箱
func (s *sNewsArticle) Draft(ctx context.Context, req *v1.NewsArticleDraftReq) (out *v1.NewsArticleDraftRes, err error) {
	out = new(v1.NewsArticleDraftRes)
	out.Current = req.Current
	out.Offset = req.Offset
	orm := dao.NewsArticle.Ctx(ctx).
		Where(dao.NewsArticle.Columns().IsDraft, consts.One).
		Where(dao.NewsArticle.Columns().DeleteTime, consts.Zero)
	// 文章名称模糊查询
	if "" != req.ArticleName {
		type articleLanguage struct {
			ArticleId int `json:"article_id"`
		}
		var articleLanguageList []articleLanguage
		err = dao.NewsArticleLanguage.Ctx(ctx).
			Distinct().
			Fields(dao.NewsArticleLanguage.Columns().ArticleId).
			WhereLike(dao.NewsArticleLanguage.Columns().Name, "%"+req.ArticleName+"%").
			Scan(&articleLanguageList)
		if err != nil {
			return out, err
		}
		articleIds := gutil.ListItemValuesUnique(articleLanguageList, "ArticleId")
		if 0 < len(articleIds) {
			orm = orm.WhereIn(dao.NewsArticle.Columns().Id, articleIds)
		}
	}
	// 创建时间
	if "" != req.CreateTimeBegin && "" != req.CreateTimeEnd {
		// 转成时间戳
		createTimeBegin := gtime.NewFromStr(req.CreateTimeBegin).TimestampStr()
		createTimeEnd := gtime.NewFromStr(req.CreateTimeEnd).TimestampStr()
		// 判断时间范围是否合法
		if createTimeBegin > createTimeEnd {
			return nil, gerror.New("开始时间不能大于结束时间")
		}
		orm = orm.WhereBetween(dao.NewsArticle.Columns().CreateTime, createTimeBegin, createTimeEnd)
	}
	// 修改时间
	if "" != req.UpdateTimeBegin && "" != req.UpdateTimeEnd {
		// 转成时间戳
		updateTimeBegin := gtime.NewFromStr(req.UpdateTimeBegin).TimestampStr()
		updateTimeEnd := gtime.NewFromStr(req.UpdateTimeEnd).TimestampStr()
		// 判断时间范围是否合法
		if updateTimeBegin > updateTimeEnd {
			return nil, gerror.New("开始时间不能大于结束时间")
		}
		orm = orm.WhereBetween(dao.NewsArticle.Columns().CreateTime, updateTimeBegin, updateTimeEnd)
	}
	// 总数
	total, err := orm.Count()
	if err != nil {
		return out, gerror.Wrap(err, "获取草稿箱总数失败")
	}
	if total == 0 {
		out.List = []v1.NewsArticleDraftItem{}
		return out, nil
	}
	out.Total = total
	// 排序，按照发布时间倒序
	orm = orm.OrderDesc(dao.NewsArticle.Columns().PublishTime)
	// 查询数据
	var articleList []*entity.NewsArticle
	err = orm.Scan(&articleList)
	if err != nil {
		return out, err
	}
	// 提取文章id
	articleIds := gutil.ListItemValuesUnique(articleList, "Id")
	// 查询文章名称
	type articleLanguage struct {
		ArticleId  int    `json:"article_id"`
		LanguageId int    `json:"language_id"`
		Name       string `json:"name"`
	}
	var articleLanguageList []*articleLanguage
	err = dao.NewsArticleLanguage.Ctx(ctx).WhereIn(dao.NewsArticleLanguage.Columns().ArticleId, articleIds).Scan(&articleLanguageList)
	if err != nil {
		return
	}
	articleLanguageMap := make(map[int]map[int]string)
	for _, v := range articleLanguageList {
		if _, ok := articleLanguageMap[v.ArticleId]; !ok {
			articleLanguageMap[v.ArticleId] = make(map[int]string)
		}
		articleLanguageMap[v.ArticleId][v.LanguageId] = v.Name
	}
	// 组装数据
	for _, v := range articleList {
		// 将int转成日期格式，时间戳是13位，需要除1000
		var createTime, updateTime string
		if 0 < v.UpdateTime {
			updateTime = time.Unix(v.UpdateTime/1000, 0).Format("2006-01-02 15:04:05")
		}
		if 0 < v.CreateTime {
			createTime = time.Unix(v.CreateTime/1000, 0).Format("2006-01-02 15:04:05")
		}
		article := v1.NewsArticleDraftItem{
			Id:         v.Id,
			CreateTime: createTime,
			UpdateTime: updateTime,
		}
		// 支持的语言
		article.LanguageArr = append(article.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Zero,
			LanguageTypeText: "ZH",
			IsSupport:        gconv.Int(v.IsZh),
		})
		article.LanguageArr = append(article.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.One,
			LanguageTypeText: "EN",
			IsSupport:        gconv.Int(v.IsEn),
		})
		article.LanguageArr = append(article.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Two,
			LanguageTypeText: "ID",
			IsSupport:        gconv.Int(v.IsId),
		})
		article.ArticleName = articleLanguageMap[gconv.Int(v.Id)][1]
		article.ArticleStatus = gconv.Int(v.IsPublish)
		article.ArticleStatusText = consts.GetArticleStatusText(gconv.Int(v.IsPublish))
		out.List = append(out.List, article)
	}
	return out, nil
}
