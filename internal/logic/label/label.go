package label

//
//import (
//	"context"
//	"database/sql"
//	"github.com/gogf/gf/v2/container/gmap"
//	"github.com/gogf/gf/v2/database/gdb"
//	"github.com/gogf/gf/v2/errors/gerror"
//	"github.com/gogf/gf/v2/frame/g"
//	"github.com/gogf/gf/v2/os/gtime"
//	"github.com/gogf/gf/v2/util/gconv"
//	v1 "gtcms/api/v1"
//	"gtcms/internal/consts"
//	"gtcms/internal/dao"
//	"gtcms/internal/logic/basicLib"
//	"gtcms/internal/model"
//	"gtcms/internal/model/entity"
//	"gtcms/internal/service"
//	"gtcms/utility"
//	"time"
//)
//
//var (
//	ctx = context.Background()
//	//UrlPrefix = "/label/"
//)
//
//type sLabel struct {
//	NameList *gmap.Map
//}
//
//func init() {
//	service.RegisterLabel(New())
//}
//
//func New() *sLabel {
//	s := &sLabel{
//		NameList: gmap.New(),
//	}
//	s.initData()
//	return s
//}
//
//func (s *sLabel) initData() {
//	all, err := dao.Label.Ctx(ctx).Where(dao.Label.Columns().DeleteTime, 0).All()
//	if err != nil {
//		return
//	}
//	for _, v := range all {
//		s.NameList.Set(gconv.Uint(v[dao.Label.Columns().Id]), v[dao.Label.Columns().Name])
//	}
//}
//
//func (s *sLabel) List(ctx context.Context, req *v1.LabelListReq) (res *v1.LabelListRes, err error) {
//	res = new(v1.LabelListRes)
//	res.Current = req.Current
//	res.List = make([]v1.LabelItem, 0)
//
//	m := dao.Label.Ctx(ctx).Where(dao.Label.Columns().Belong, req.Belong).Handler(utility.CreatorFilter)
//	if req.Name != nil {
//		m = m.WhereLike(dao.Label.Columns().Name, utility.WhereLike(*req.Name))
//	}
//	if req.Status != nil {
//		m = m.Where(dao.Label.Columns().Status, req.Status)
//	}
//	if req.BelongId != nil {
//		m = m.Where(dao.Label.Columns().BelongId, *req.BelongId)
//	}
//	if req.IsRecommend != nil {
//		m = m.Where(dao.Label.Columns().IsRecommend, req.IsRecommend)
//	}
//
//	m = m.Where(dao.Label.Columns().DeleteTime, 0)
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.Label.Table() + "." + dao.Label.Columns().Id) // 最新在前面
//	}
//
//	// 查询列表
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.LabelItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.LabelItem, 0, total)
//	}
//
//	err = m.Scan(&res.List)
//	if err != nil {
//		return
//	}
//	res.Total = total
//	return
//}
//
//func (s *sLabel) checkNameExist(name interface{}) error {
//	//for _, v := range s.NameList.Values() {
//	//	if gconv.String(v) == gconv.String(name) {
//	//		return gerror.New("该名称已存在")
//	//	}
//	//}
//	return nil
//}
//
//func (s *sLabel) updateModel(ctx context.Context, belong int, belongId *uint) (m *gdb.Model) {
//	m = dao.Label.Ctx(ctx)
//	name := ""
//	switch belong {
//	case 0:
//		name = consts.LabelList
//	case 1:
//		name = consts.LabelGroupList + gconv.String(belongId)
//	case 2:
//		name = consts.LabelSiteList + gconv.String(belongId)
//	}
//	m = m.Cache(gdb.CacheOption{
//		Duration: -1,
//		Name:     name,
//		Force:    false, // nil也缓存
//	})
//	return
//}
//
//func (s *sLabel) Add(ctx context.Context, req *v1.LabelAddReq) (res *v1.LabelAddRes, err error) {
//	res = new(v1.LabelAddRes)
//
//	if err = s.checkNameExist(req.Name); err != nil {
//		return
//	}
//
//	// 校验url
//	if req.Url != "" {
//		if !service.Utility().IsAlphaNumeric(req.Url) {
//			return res, gerror.New("url只能包含字母和数字")
//		}
//	}
//
//	m := s.updateModel(ctx, req.Belong, req.BelongId)
//	m = m.Where(dao.Label.Columns().Belong, req.Belong)
//	var entityData *entity.Label
//	if err = gconv.Scan(req, &entityData); err != nil {
//		return
//	}
//	entityData.CreateTime = time.Now().UnixMilli()
//	if req.BelongId != nil {
//		entityData.BelongId = *req.BelongId
//	}
//	self, err := service.Utility().GetSelf(ctx)
//	if err != nil {
//		return
//	}
//	if self != nil {
//		entityData.Creater = self.Id
//	}
//	entityData.Url = req.Url
//
//	id, err := m.InsertAndGetId(entityData)
//	if err != nil {
//		return
//	}
//
//	s.NameList.Set(gconv.Uint(id), req.Name)
//	res = &v1.LabelAddRes{
//		Id: uint(id),
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: entityData,
//	})
//	return
//}
//
//func (s *sLabel) Edit(ctx context.Context, req *v1.LabelEditReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	if err = s.checkNameExist(req.Name); err != nil {
//		//return
//	}
//
//	update := g.Map{}
//	if req.Name != nil {
//		update[dao.Label.Columns().Name] = req.Name
//	}
//	if req.Sort != nil {
//		update[dao.Label.Columns().Sort] = req.Sort
//	}
//	if req.IsUsed != nil {
//		update[dao.Label.Columns().IsUsed] = req.IsUsed
//	}
//	if req.Thumb != nil {
//		update[dao.Label.Columns().Thumb] = req.Thumb
//	}
//	if req.SeoTitle != nil {
//		update[dao.Label.Columns().SeoTitle] = req.SeoTitle
//	}
//	if req.SeoKeyword != nil {
//		update[dao.Label.Columns().SeoKeyword] = req.SeoKeyword
//	}
//	if req.SeoDesc != nil {
//		update[dao.Label.Columns().SeoDesc] = req.SeoDesc
//	}
//	if req.Status != nil {
//		update[dao.Label.Columns().Status] = req.Status
//	}
//	if req.IsRecommend != nil {
//		update[dao.Label.Columns().IsRecommend] = req.IsRecommend
//	}
//	if req.Url != nil {
//		if !service.Utility().IsAlphaNumeric(*req.Url) {
//			return res, gerror.New("url只能包含字母和数字")
//		}
//		update[dao.Label.Columns().Url] = *req.Url
//	}
//
//	// 取旧数据
//	oldData, _ := s.One(ctx, &v1.LabelOneReq{
//		Id: req.Id,
//	})
//	m := s.updateModel(ctx, oldData.Belong, &oldData.BelongId)
//	update[dao.Label.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	var rs sql.Result
//	if rs, err = m.Where(dao.Label.Columns().Id, req.Id).Update(update); err != nil {
//		return
//	}
//	affected, _ := rs.RowsAffected()
//	if affected > 0 {
//		s.NameList.Set(gconv.Uint(req.Id), req.Name)
//	}
//
//	if _, err = service.DbCache().DeleteDbCache(ctx, consts.LabelOne+gconv.String(req.Id)); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		OldRecordOrAttr: oldData,
//		NewRecordOrAttr: req,
//	})
//	return
//}
//
//func (s *sLabel) Delete(ctx context.Context, req *v1.LabelDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	update[dao.Label.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	update[dao.Label.Columns().DeleteTime] = gtime.Now().UnixMilli()
//
//	var ids []uint
//	if req.Id != 0 {
//		ids = append(ids, req.Id)
//	}
//	if len(req.Ids) > 0 {
//		ids = append(ids, req.Ids...)
//	}
//	if len(ids) < 1 {
//		err = gerror.New("id is empty")
//		return
//	}
//
//	// 取旧数据
//	oldData, _ := s.One(ctx, &v1.LabelOneReq{
//		Id: ids[0],
//	})
//	m := s.updateModel(ctx, oldData.Belong, &oldData.BelongId)
//	var rs sql.Result
//	if rs, err = m.Where(dao.Label.Columns().Id, ids).Data(update).Update(); err != nil {
//		return
//	}
//	affected, _ := rs.RowsAffected()
//	if affected > 0 {
//		s.NameList.Remove(gconv.Uint(ids))
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: oldData,
//	})
//
//	if _, err = service.DbCache().DeleteDbCache(ctx, consts.LabelOne+gconv.String(req.Id)); err != nil {
//		return
//	}
//	return
//}
//
//func (s *sLabel) One(ctx context.Context, req *v1.LabelOneReq) (res *v1.LabelOneRes, err error) {
//	res = new(v1.LabelOneRes)
//
//	sg, err := dao.Label.Ctx(ctx).Where(dao.Label.Columns().Id, req.Id).One()
//	if err != nil {
//		return
//	}
//
//	var item v1.LabelItem
//	err = gconv.Scan(sg, &item)
//	if err != nil {
//		return
//	}
//	res = &v1.LabelOneRes{
//		LabelItem: item,
//	}
//	return
//}
//
//func (s *sLabel) Options(ctx context.Context) (options []model.SelectOption, err error) {
//	options = make([]model.SelectOption, 0)
//	err = dao.Label.Ctx(ctx).Fields("name as label,id as value").
//		Where(dao.Label.Columns().DeleteTime, 0).Scan(&options)
//	return
//}
//
//func (s *sLabel) Related(ctx context.Context, req *v1.LabelRelatedReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	self, err := service.Utility().GetSelf(ctx)
//	if err != nil {
//		return
//	}
//	var selfId uint
//	if self != nil {
//		selfId = self.Id
//	}
//
//	insertList := g.List{}
//	var deleteIds []uint
//	for _, v := range req.RelatedList {
//		if v.Mode == 1 {
//			insertList = append(insertList, g.Map{
//				dao.LabelOtherRelation.Columns().LabelId:  req.LabelId,
//				dao.LabelOtherRelation.Columns().RelId:    v.RelId,
//				dao.LabelOtherRelation.Columns().RelType:  v.RelType,
//				dao.LabelOtherRelation.Columns().Belong:   req.Belong,
//				dao.LabelOtherRelation.Columns().BelongId: utility.If(req.Belong == 0, gconv.Uint(req.Belong), gconv.Uint(req.BelongId)),
//				dao.LabelOtherRelation.Columns().Creater:  selfId,
//			})
//		} else {
//			deleteIds = append(deleteIds, v.RelId)
//		}
//	}
//
//	m := dao.LabelOtherRelation.Ctx(ctx).Where(dao.LabelOtherRelation.Columns().Belong, req.Belong)
//	if req.BelongId != nil {
//		m = m.Where(dao.LabelOtherRelation.Columns().BelongId, req.BelongId)
//	}
//	if len(insertList) > 0 {
//		if _, err = m.Data(insertList).Replace(); err != nil {
//			return
//		}
//	}
//	if len(deleteIds) > 0 {
//		if _, err = m.Where(dao.LabelOtherRelation.Columns().LabelId, req.LabelId).
//			Where(dao.LabelOtherRelation.Columns().RelId, deleteIds).Delete(); err != nil {
//			return
//		}
//	}
//	return
//}
//
//func (s *sLabel) NewsList(ctx context.Context, req *v1.LabelNewsListReq) (res *v1.LabelNewsListRes, err error) {
//	res = &v1.LabelNewsListRes{
//		List: make([]v1.LabelNewsItem, 0),
//	}
//	res.Current = req.Current
//
//	m := dao.News.Ctx(ctx)
//	m = m.Where(dao.News.Columns().DeleteTime, 0).Handler(utility.CreatorFilter)
//	if req.GroupId != nil {
//		m = m.Where(dao.News.Columns().BelongGroupId, req.GroupId)
//	}
//	if req.ColumnName != nil {
//		m = m.WhereLike(dao.News.Columns().BelongColName, utility.WhereLike(*req.ColumnName))
//	}
//
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.News.Table() + "." + dao.News.Columns().Id) // 最新在前面
//	}
//
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.LabelNewsItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.LabelNewsItem, 0, total)
//	}
//
//	m = m.Fields(dao.News.Table()+"."+dao.News.Columns().Id,
//		dao.News.Table()+"."+dao.News.Columns().Views,
//		dao.News.Table()+"."+dao.News.Columns().CreateTime,
//		dao.News.Table()+"."+dao.News.Columns().Title,
//		dao.News.Table()+"."+dao.News.Columns().BelongColName,
//		dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Name+" as belongGroupName",
//		dao.Site.Table()+"."+dao.Site.Columns().Name+" as belongSiteName",
//		"CASE WHEN "+dao.LabelOtherRelation.Table()+"."+dao.LabelOtherRelation.Columns().LabelId+" IS NOT NULL THEN 1 ELSE 2 END AS status").
//		LeftJoin(dao.SiteGroup.Table(), dao.SiteGroup.Table()+"."+dao.SiteGroup.Columns().Id+" = "+dao.News.Table()+"."+dao.News.Columns().BelongGroupId).
//		LeftJoin(dao.Site.Table(), dao.Site.Table()+"."+dao.Site.Columns().Id+" = "+dao.News.Table()+"."+dao.News.Columns().BelongSiteId).
//		LeftJoin(dao.LabelOtherRelation.Table(), dao.LabelOtherRelation.Table()+"."+dao.LabelOtherRelation.Columns().RelId+" = "+dao.News.Table()+"."+
//			dao.News.Columns().Id+" AND "+dao.LabelOtherRelation.Table()+"."+dao.LabelOtherRelation.Columns().LabelId+" = "+gconv.String(req.LabelId))
//	if req.SiteName != nil {
//		m = m.Where(dao.Site.Table()+"."+dao.Site.Columns().Name, req.SiteName)
//	}
//	if req.Content != nil {
//		m = m.LeftJoin(dao.NewsBody.Table(), dao.NewsBody.Table()+"."+dao.NewsBody.Columns().NewsId+" = "+dao.News.Table()+"."+dao.News.Columns().Id)
//		m = m.WhereLike(dao.NewsBody.Table()+"."+dao.NewsBody.Columns().Body, utility.WhereLike(*req.Content))
//	}
//
//	err = m.Scan(&res.List)
//	if err != nil {
//		return
//	}
//
//	res.Total = total
//	return
//}
//
//func (s *sLabel) GroupSiteAllList(ctx context.Context, req *v1.LabelGroupSiteAllListReq) (res *v1.LabelGroupSiteAllListRes, err error) {
//	list, err := s.List(ctx, &v1.LabelListReq{
//		Belong:   req.Belong,
//		BelongId: req.BelongId,
//	})
//	if err != nil {
//		return
//	}
//	var ids string
//	for _, item := range list.List {
//		ids += gconv.String(item.SelfId) + ","
//	}
//	if len(ids) > 0 {
//		ids = ids[:len(ids)-1]
//	}
//	if ids == "" {
//		ids = "-1"
//	}
//
//	res = new(v1.LabelGroupSiteAllListRes)
//	res.Current = req.Current
//	res.List = make([]v1.LabelItem, 0)
//
//	m := dao.Label.Ctx(ctx)
//	if req.Status != nil {
//		m = m.Where(dao.Label.Columns().Status, req.Status)
//	}
//
//	m = m.Where(dao.Label.Columns().DeleteTime, 0).
//		Where(dao.Label.Columns().Belong, 0)
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.Label.Table() + "." + dao.Label.Columns().Id) // 最新在前面
//	}
//
//	// 查询列表
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.LabelItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.LabelItem, 0, total)
//	}
//
//	m = m.Fields(dao.Label.Table()+".*",
//		"CASE WHEN "+dao.Label.Columns().Id+" IN ("+ids+") THEN 1 ELSE 2 END AS ChooseStatus")
//	err = m.Scan(&res.List)
//	if err != nil {
//		return
//	}
//	res.Total = total
//	return
//}
//
//func (s *sLabel) GroupSiteAdd(ctx context.Context, req *v1.LabelGroupSiteAddReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//	var insertData []entity.Label
//	if _, err = basicLib.GroupSiteAdd(ctx, req.GroupSiteAddReqIn, basicLib.GroupSiteAddParams{
//		M: dao.Label.Ctx(ctx),
//		T: dao.Label.Table(),
//		B: req.Belong,
//	}, insertData); err != nil {
//		return
//	}
//
//	otherM := dao.LabelOtherRelation.Ctx(ctx).
//		Where(dao.LabelOtherRelation.Columns().Belong, req.Belong).
//		Where(dao.LabelOtherRelation.Columns().BelongId, req.BelongId)
//
//	if len(insertData) > 0 {
//		// 插入标签关联的数据
//		var insertOtherData []entity.LabelOtherRelation
//		if err = dao.LabelOtherRelation.Ctx(ctx).Fields(dao.LabelOtherRelation.Table()+".*",
//			gconv.String(req.BelongId)+" as self_id").Where(dao.LabelOtherRelation.Columns().LabelId, insertData).
//			Scan(&insertOtherData); err != nil {
//			return
//		}
//		if _, err = otherM.Data(insertOtherData).Replace(); err != nil {
//			return
//		}
//	}
//	return
//}
//
//func (s *sLabel) ValidCount(ctx context.Context) (uint, error) {
//	count, err := dao.Label.Ctx(ctx).Where(dao.Label.Columns().Status, 1).Where(dao.Label.Columns().DeleteTime, 0).Count()
//	if err != nil {
//		return 0, err
//	}
//
//	return uint(count), nil
//}
