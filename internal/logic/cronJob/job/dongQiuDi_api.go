package job

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	dao "gtcms/internal/dao/admin"
	"gtcms/internal/logic/ai"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/utils/crawler"
	"time"
)

type TaskDongQiuDiApi struct {
	Symbol   string
	Job      *entity.CronJob
	Param    *DongQiuDiApiParam
	Progress *CommonPageStatus
}

type DongQiuDiApiParam struct {
	Id            int `json:"id"`
	CompetitionId int `json:"competitionId"` // 对应的赛事id
	Type          int `json:"type"`          // 1足球，2篮球
}

func (t *TaskDongQiuDiApi) Name() string {
	return t.Symbol
}

func (t *TaskDongQiuDiApi) GetCron() string {
	return t.Job.Cron
}

func (t *TaskDongQiuDiApi) Run() error {
	url := ""

	for i := 0; i < 5; i++ {
		ctx := gctx.New()

		res, err := crawler.DongQiuDiApi(ctx, t.Param.Id, url)
		if err != nil {
			return err
		}
		url = res.Next

		for _, e := range res.Articles {
			if e.Title == "" {
				continue
			}

			status, article, err := crawler.DongQiuDiArticle(e.Id)
			if err != nil {
				g.Log().Line().Errorf(ctx, "dongqiudi api article fail: code %d, err:%s", status, err)
				continue
			}

			news := &entity.CrawlerNewsRaw{
				Source:        "dongQiuDi",
				Docid:         e.Scheme,
				Title:         e.Title,
				Url:           e.Url,
				Intro:         e.ShareTitle,
				CompetitionId: t.Param.CompetitionId,
				SourceCate:    "",
				CreateTime:    gconv.Int64(e.ShowTime) * 1000,
				UpdateTime:    gconv.Int64(e.ShowTime) * 1000,
				Content:       article,
				Type:          t.Param.Type,
			}
			count, _ := dao.CrawlerNewsRaw.Ctx(ctx).Where(dao.CrawlerNewsRaw.Columns().Docid, news.Docid).Count()
			if count > 0 {
				continue
			}

			// 先保存采集文
			_, err = dao.CrawlerNewsRaw.Ctx(ctx).Insert(news)
			if err != nil {
				g.Log().Line().Error(ctx, err)
			}

			// AI 改写文章
			newsList := make([]*entity.CrawlerNewsRaw, 0)
			rs, err := ai.RewriteMultipleArticles(article, 1)
			if err != nil {
				g.Log().Line().Error(ctx, err)
			}
			if rs == nil || len(rs) == 0 {
				continue
			}
			for k, v := range rs {
				v = gstr.ReplaceByMap(v, map[string]string{
					"\x00": "",  // 清除 NULL 字符
					"\n":   " ", // 替换换行符
					"\r":   "",  // 去掉回车符（避免 Windows 换行 \r\n 问题）
				})
				n := &entity.CrawlerNewsRaw{
					Source:        "dongQiuDi",
					Docid:         e.Scheme + gconv.String(k),
					Title:         e.Title,
					Url:           e.Url,
					Intro:         e.ShareTitle,
					CompetitionId: t.Param.CompetitionId,
					SourceCate:    "",
					CreateTime:    gconv.Int64(e.ShowTime) * 1000,
					//CreateTime: time.Now().UnixMilli(),
					UpdateTime: gconv.Int64(e.ShowTime) * 1000,
					Content:    v,
					Type:       t.Param.Type,
					//IsAi:       1,
				}
				newsList = append(newsList, n)
			}

			_, err = dao.CrawlerNewsRaw.Ctx(ctx).Insert(newsList)
			if err != nil {
				g.Log().Line().Error(ctx, err)
			}
		}

		// 更新进度
		t.Progress.UpdateTime = time.Now().UnixMilli()
		b, _ := json.Marshal(t.Progress)
		t.Job.Progress = string(b)

		g.Log().Line().Info(ctx, "CollectFinished", t.Job.Symbol, t.Progress)
	}

	return nil
}

func (t *TaskDongQiuDiApi) Init(ctx context.Context) (err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	p := new(DongQiuDiApiParam)
	err = json.Unmarshal([]byte(t.Job.Param), p)
	if err != nil {
		return err
	}
	t.Param = p
	pg := new(CommonPageStatus)
	err = json.Unmarshal([]byte(t.Job.Progress), pg)
	if err != nil {
		return err
	}
	pg.Page--
	if pg.Page <= 0 {
		pg.Page = 1
		pg.ByTime = 1
	}
	t.Progress = pg
	return err
}

func (t *TaskDongQiuDiApi) GetJob() *entity.CronJob {
	return t.Job
}
