package job

import (
	"context"
	"github.com/gogf/gf/v2/os/gctx"

	dao "gtcms/internal/dao/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/utils/collect_api"
	"strings"
	"sync"
)

var Jobs map[string]IJob = make(map[string]IJob)
var JobLocks map[string]*sync.Mutex = make(map[string]*sync.Mutex)
var lock *sync.Mutex = &sync.Mutex{}

func registerJob(j IJob) {
	lock.Lock()
	Jobs[j.Name()] = j
	JobLocks[j.Name()] = &sync.Mutex{}
	lock.Unlock()
}

func RegisterCronJobs() {
	ctx := gctx.New()
	cronJobs := make([]entity.CronJob, 0)
	err := dao.CronJob.Ctx(ctx).Where("status = 1").Scan(&cronJobs)

	if err != nil {
		panic(err.Error())
	}
	for _, ct := range cronJobs { // 对每一个任务进行正确的映射
		ctEntity := ct
		mapJobs(&ctEntity)
	}
}

type IJob interface {
	Name() string
	GetCron() string
	Run() error
	Init(ctx context.Context) error
	GetJob() *entity.CronJob
}

type NamiPagedProgress struct {
	ByTime     int `json:"by_time"`     // 球员是否可以按更新时间来更新。0:false, 表示还处在按id的全量更新阶段。 1:true,表示已经在按时间更新了
	UpdateTime int `json:"update_time"` // 球员，按更新时间 从0开始
	Id         int `json:"id"`          // 球员，按id查询 从0开始
}

type NamiFullProgress struct {
	UpdatedAt int64 `json:"updated_at"`
}

type CommonPageStatus struct {
	ByTime     int   `json:"by_time"`     // 按更新时间/页码来查询 0: 按页码， 1:按更新时间只爬第1页
	UpdateTime int64 `json:"update_time"` // 上次更新时间
	Page       int   `json:"page"`        // 上次爬的页码
}
type NamiPagedFunc = func(ctx context.Context, job *entity.CronJob, req collect_api.ReqList) (q *collect_api.ResQuery, err error)
type NamiFullFunc = func(ctx context.Context, job *entity.CronJob) (err error)

func mapJobs(j *entity.CronJob) {
	//if strings.HasPrefix(j.Symbol, "resave_image_") {
	//	registerJob(&ReSaveImageJob{Symbol: j.Symbol, Job: j})
	//	return
	//}
	//// 测试图片转存，先只注册图片相关的，测完放开
	//return
	if strings.HasPrefix(j.Symbol, "sina_news_") {
		//registerJob(&TaskSinaApi{
		//	Symbol: j.Symbol,
		//	Job:    j,
		//})
		//return
	}
	//if strings.HasPrefix(j.Symbol, "sina_comment_") {
	//	registerJob(&TaskSinaCommentApi{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//	})
	//	return
	//}
	//if strings.HasPrefix(j.Symbol, "skysport_news_") {
	//	registerJob(&SkysportNewsApi{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//	})
	//	return
	//}
	//if strings.HasPrefix(j.Symbol, "espn_news_") {
	//	registerJob(&EspnNewsApi{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//	})
	//	return
	//}
	//
	//if strings.HasPrefix(j.Symbol, "news_raw_content") {
	//	registerJob(&NewsRawContentApi{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//	})
	//	return
	//}
	//if strings.HasPrefix(j.Symbol, "dongqiudi_news_") {
	//	registerJob(&TaskDongQiuDiApi{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//	})
	//	return
	//}
	//if j.Symbol == "video_live_from_a" {
	//	registerJob(&VideoLiveFromAApi{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//	})
	//	return
	//}
	////if j.Symbol == "video_record_migu" {
	////	registerJob(&VideoLiveFromMiGu{
	////		Symbol: j.Symbol,
	////		Job:    j,
	////	})
	////	return
	////}
	//if strings.HasPrefix(j.Symbol, "zhibo8_news") {
	//	registerJob(&TaskZhibo8Api{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//	})
	//	return
	//}
	//if j.Symbol == "tx_video_collection_basketball" {
	//	registerJob(&VideoCollectFromTx{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//	})
	//	return
	//}
	//switch j.Symbol {
	//case "nami_football_player_base":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballPlayerBaseFunc,
	//	})
	//case "nami_football_team_base":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballTeamBaseFunc,
	//	})
	//case "nami_country":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballCountryFunc,
	//	})
	//case "nami_basketball_competition":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballCompetitionBaseFunc,
	//	})
	//case "nami_football_competition_base":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballCompetitionBaseFunc,
	//	})
	//case "nami_football_honor":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballHonorFunc,
	//	})
	//case "nami_football_player_honor":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballPlayerHonorFunc,
	//	})
	//case "nami_football_team_honor":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballTeamHonorFunc,
	//	})
	//case "nami_football_player_transfer":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballTransferFunc,
	//	})
	//case "nami_football_venue":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballVenueFunc,
	//	})
	//case "nami_football_season":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballSeasonFunc,
	//	})
	//case "nami_football_competition_stats":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballCompetitionStatsFunc,
	//	})
	//case "nami_football_deleted":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballDeletedFunc,
	//	})
	//case "nami_basketball_deleted":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballDeletedFunc,
	//	})
	//case "nami_football_schedule_diary":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballScheduleDiaryFunc,
	//	})
	////case "nami_football_video_collection":
	////	registerJob(&NamiPagedJob{
	////		Symbol: j.Symbol,
	////		Job:    j,
	////		Func:   funcs.NamiFootballVideoCollectionFunc,
	////	})
	////case "nami_basketball_video_collection":
	////	registerJob(&NamiPagedJob{
	////		Symbol: j.Symbol,
	////		Job:    j,
	////		Func:   funcs.NamiBasketballVideoCollectionFunc,
	////	})
	//case "nami_basketball_schedule_diary":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballScheduleDiaryFunc,
	//	})
	//case "nami_basketball_honor":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballHonorFunc,
	//	})
	//case "nami_basketball_player_base":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballPlayerBaseFunc,
	//	})
	//case "nami_basketball_player_honor":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballPlayerHonorFunc,
	//	})
	//case "nami_basketball_season":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballSeasonFunc,
	//	})
	//case "nami_basketball_team_base":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballTeamBaseFunc,
	//	})
	//case "nami_basketball_team_honor":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballTeamHonorFunc,
	//	})
	//case "nami_basketball_venue":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballVenueFunc,
	//	})
	//case "nami_basketball_competition_stats":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballCompetitionStatsFunc,
	//	})
	//case "nami_football_match":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballMatchFunc,
	//	})
	//case "nami_football_match_analysis":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballMatchAnalysisFunc,
	//	})
	//case "nami_basketball_match_analysis":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballMatchAnalysisFunc,
	//	})
	//case "nami_basketball_match":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballMatchFunc,
	//	})
	//case "nami_football_squad":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballSquadFunc,
	//	})
	//case "nami_basketball_squad":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballSquadFunc,
	//	})
	//case "video_record_tgrfst":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.VideoRecordFunc,
	//	})
	//case "video_record_77livejk":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.VideoRecordIdFunc,
	//	})
	//case "video_live_pushurl_v4":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.VideoLiveFunc,
	//	})
	//case "nami_snooker_match":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiSnookerMatchFunc,
	//	})
	//case "nami_snooker_match_live":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiSnookerMatchLiveFunc,
	//	})
	//case "nami_snooker_competition":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiSnookerCompetitionBaseFunc,
	//	})
	////case "nami_snooker_season":
	////	registerJob(&NamiPagedJob{
	////		Symbol: j.Symbol,
	////		Job:    j,
	////		Func:   funcs.NamiSnookerSeasonFunc,
	////	})
	//case "nami_snooker_team":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiSnookerTeamBaseFunc,
	//	})
	////case "nami_snooker_team_stats":
	////	registerJob(&NamiPagedJob{
	////		Symbol: j.Symbol,
	////		Job:    j,
	////		Func:   funcs.NamiSnookerTeamStatsFunc,
	////	})
	////case "nami_snooker_team_ranking":
	////	registerJob(&NamiPagedJob{
	////		Symbol: j.Symbol,
	////		Job:    j,
	////		Func:   funcs.NamiSnookerTeamRankingFunc,
	////	})
	//case "nami_snooker_deleted":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiSnookerDeletedFunc,
	//	})
	//
	//case "resave_image_basketball_competition", "resave_image_football_competition", "resave_image_basketball_team",
	//	"resave_image_football_team", "resave_image_basketball_player", "resave_image_football_player":
	//	registerJob(&ReSaveImageJob{Symbol: j.Symbol, Job: j})
	//case "nami_country_multi_language":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballCountryMultiLanguageFunc,
	//	})
	//case "nami_football_competition_multi_language":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballCompetitionMultiLanguageFunc,
	//	})
	//case "nami_football_team_multi_language":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballTeamMultiLanguageFunc,
	//	})
	//case "nami_football_player_multi_language":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballPlayerMultiLanguageFunc,
	//	})
	//case "nami_basketball_competition_multi_language":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballCompetitionMultiLanguageFunc,
	//	})
	//case "nami_basketball_team_multi_language":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballTeamMultiLanguageFunc,
	//	})
	//case "nami_football_match_live":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiFootballMatchLiveFunc,
	//	})
	//case "nami_basketball_match_live":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiBasketballMatchLiveFunc,
	//	})
	//	//case "nami_basketball_player_multi_language":
	//	//	registerJob(&NamiPagedJob{
	//	//		Symbol: j.Symbol,
	//	//		Job:    j,
	//	//		Func:   funcs.NamiBasketballPlayerMultiLanguageFunc,
	//	//	})
	//case "nami_lol_match":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiLolMatchFunc,
	//	})
	//case "nami_lol_competition":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiLolCompetitionBaseFunc,
	//	})
	//case "nami_lol_team":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiLolTeamBaseFunc,
	//	})
	//case "nami_lol_player":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiLolPlayerBaseFunc,
	//	})
	//case "nami_lol_deleted":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiLolDeletedFunc,
	//	})
	//case "nami_lol_match_live":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiLolMatchLiveFunc,
	//	})
	//case "nami_cs_match":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiCsMatchFunc,
	//	})
	//case "nami_cs_competition":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiCsCompetitionBaseFunc,
	//	})
	//case "nami_cs_team":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiCsTeamBaseFunc,
	//	})
	//case "nami_cs_player":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiCsPlayerBaseFunc,
	//	})
	//case "nami_cs_deleted":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiCsDeletedFunc,
	//	})
	//
	//case "nami_dota_match":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiDotaMatchFunc,
	//	})
	//case "nami_dota_competition":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiDotaCompetitionBaseFunc,
	//	})
	//case "nami_dota_team":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiDotaTeamBaseFunc,
	//	})
	//case "nami_dota_player":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiDotaPlayerBaseFunc,
	//	})
	//case "nami_dota_deleted":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiDotaDeletedFunc,
	//	})
	//
	//case "nami_kog_match":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiKogMatchFunc,
	//	})
	//case "nami_kog_competition":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiKogCompetitionBaseFunc,
	//	})
	//case "nami_kog_team":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiKogTeamBaseFunc,
	//	})
	//case "nami_kog_player":
	//	registerJob(&NamiPagedJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiKogPlayerBaseFunc,
	//	})
	//case "nami_kog_deleted":
	//	registerJob(&NamiFullJob{
	//		Symbol: j.Symbol,
	//		Job:    j,
	//		Func:   funcs.NamiKogDeletedFunc,
	//	})
	//}

}
