package funcs

//
//import (
//	"context"
//	"github.com/gogf/gf/v2/encoding/gjson"
//	"github.com/gogf/gf/v2/frame/g"
//	"gtcms/internal/dao"
//	"gtcms/internal/model/entity"
//	"gtcms/internal/utils"
//	"gtcms/internal/utils/collect_api/basketball"
//	"net/http"
//	"time"
//)
//
//// NamiBasketballCompetitionStats  纳米数据： 足球:赛事统计 （球员/球队/赛事）
//// 主任务symbol: "nami_basketball_competition_base"
//// 子任务symbol: {symbol}_id/time_{id/ts}
//
//var NamiBasketballCompetitionStatsFunc = func(ctx context.Context, job *entity.CronJob) (err error) {
//
//	cp := new(competitionStatsParam)
//	err = gjson.Unmarshal([]byte(job.Param), cp)
//	if err != nil {
//		return err
//	}
//
//	// 每 120 个元素分一组
//	groupSize := 120
//	chunks := chunkArray(cp.Ids, groupSize)
//
//	// 打印结果
//	for _, chunk := range chunks {
//		for _, id := range chunk {
//
//			retStats, statusCode, err := basketball.GetCompetitionStatsDetail(ctx, id)
//			if err != nil || statusCode != http.StatusOK {
//				return nil
//			}
//
//			retTable, statusCode, err := basketball.GetCompetitionTableDetail(ctx, id)
//			if err != nil || statusCode != http.StatusOK {
//				return nil
//			}
//
//			if retTable != nil && retStats != nil && retTable.SeasonId == retStats.SeasonId {
//				// TODO 处理两个信息，然后合到一块
//				e := &entity.CollectBasketballCompetitionStats{
//					Id:           id,
//					SeasonId:     retTable.SeasonId,
//					Promotions:   utils.MustJsonStr(retTable.Promotions),
//					Tables:       utils.MustJsonStr(retTable.Tables),
//					TeamsStats:   utils.MustJsonStr(retStats.TeamsStats),
//					PlayersStats: utils.MustJsonStr(retStats.PlayersStats),
//				}
//				_, err = dao.CollectBasketballCompetitionStats.Ctx(ctx).Save(e)
//			}
//		}
//		//请求间隔 请求次数：120次/min 60s
//		g.Log().Line().Debug(ctx, "wait 70 second")
//		time.Sleep(70 * time.Second)
//	}
//	return err
//
//}
