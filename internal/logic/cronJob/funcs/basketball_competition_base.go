package funcs

//
//import (
//	"context"
//	"gtcms/internal/dao"
//	"gtcms/internal/model/entity"
//	"gtcms/internal/utils/collect_api"
//	"gtcms/internal/utils/collect_api/basketball"
//	"net/http"
//)
//
//// NamiBasketballCompetitionBase  纳米数据： 足球:赛事数据 （球员/球队/赛事）
//// 主任务symbol: "nami_basketball_competition_base"
//// 子任务symbol: {symbol}_id/time_{id/ts}
//
//var NamiBasketballCompetitionBaseFunc = func(ctx context.Context, job *entity.CronJob, req collect_api.ReqList) (q *collect_api.ResQuery, err error) {
//
//	ret, q, statusCode, err := basketball.GetCompetitionList(ctx, req)
//	if err != nil || statusCode != http.StatusOK {
//		return q, err
//	}
//	competitions := make([]entity.CollectBasketballCompetition, 0)
//	for _, e := range ret {
//		competitions = append(competitions, basketCompetitionResultToEntity(&e))
//	}
//	_, err = dao.CollectBasketballCompetition.Ctx(ctx).Save(competitions)
//	return q, err
//}
//
//func basketCompetitionResultToEntity(e *basketball.CompetitionResult) (p entity.CollectBasketballCompetition) {
//
//	p = entity.CollectBasketballCompetition{
//		Id:           e.Id,
//		CategoryId:   e.CategoryId,
//		CountryId:    e.CountryId,
//		NameZh:       e.NameZh,
//		NameZht:      e.NameZht,
//		NameEn:       e.NameEn,
//		ShortNameZh:  e.ShortNameZh,
//		ShortNameZht: e.ShortNameZht,
//		ShortNameEn:  e.ShortNameEn,
//		Type:         e.Type,
//		Logo:         e.Logo,
//		UpdatedAt:    int64(e.UpdatedAt),
//	}
//	return p
//}
