package cronJob

import (
	"context"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/robfig/cron/v3"
	"gtcms/internal/service"
	"sync"
)

func init() {
	sj := sCronJob{
		crontaber: cron.New(),
		jobLock:   sync.Mutex{},
	}
	go sj.Daemon(gctx.New())
	service.RegisterCronJob(&sj)
}

// 定时任务
type sCronJob struct {
	jobLock   sync.Mutex
	crontaber *cron.Cron
}

func (s *sCronJob) Daemon(ctx context.Context) {
	//job.RegisterCronJobs()
	//RegisterAllCollectJob() // 这些是特殊任务，先不汇总进来
	//for _, j := range job.Jobs {
	//	j2 := j
	//	_, err := s.crontaber.AddFunc(j2.GetCron(), job.CronFunc(j2))
	//	if err != nil {
	//		g.Log().Line().Info(ctx, "addCronErr:", err)
	//	}
	//	cronStr := j2.GetCron()
	//	g.Log().Line().Info(ctx, "addCron:", j2.Name(), cronStr)
	//}
	//g.Log().Line().Debug(ctx, "AllRegisteredJobs:", job.Jobs)
	go s.crontaber.Start()
}
