package sitemap

//
//import (
//	"context"
//	"errors"
//	"github.com/gogf/gf/v2/database/gdb"
//	"github.com/gogf/gf/v2/frame/g"
//	"github.com/gogf/gf/v2/os/gcron"
//	"github.com/gogf/gf/v2/os/gtime"
//	"github.com/gogf/gf/v2/util/gconv"
//	v1 "gtcms/api/v1"
//	"gtcms/internal/consts"
//	"gtcms/internal/dao"
//	_ "gtcms/internal/logic/file"
//	_ "gtcms/internal/logic/storeConfig"
//	_ "gtcms/internal/logic/urlSetting"
//	"gtcms/internal/model"
//	"gtcms/internal/model/entity"
//	"gtcms/internal/service"
//	"gtcms/utility"
//	"time"
//)
//
//type sSitemap struct {
//}
//
//func init() {
//	service.RegisterSitemap(New())
//}
//
//func New() *sSitemap {
//	s := &sSitemap{}
//	//g.Go(ctx, func(ctx context.Context) {
//	//	s.run(true, nil)
//	//}, nil)
//
//	// 每天0点执行一次的任务
//	_, _ = gcron.AddSingleton(ctx, "# 0 0 * * *", func(ctx context.Context) {
//		s.run(false, nil)
//	})
//	return s
//}
//
//func (s *sSitemap) List(ctx context.Context, req *v1.SitemapListReq) (res *v1.SitemapListRes, err error) {
//	res = new(v1.SitemapListRes)
//	res.Current = req.Current
//	res.List = make([]v1.SitemapItem, 0)
//
//	m := dao.Sitemap.Ctx(ctx).Handler(utility.CreatorFilter)
//	if req.Status != nil {
//		m = m.Where(dao.Sitemap.Columns().Status, req.Status)
//	}
//	if req.GroupId != nil {
//		m = m.Where(dao.Sitemap.Columns().BelongId, req.GroupId).Where(dao.Sitemap.Columns().Belong, 1)
//	} else if req.SiteId != nil {
//		m = m.Where(dao.Sitemap.Columns().BelongId, req.SiteId).Where(dao.Sitemap.Columns().Belong, 2)
//	} else { // group/site必传一个
//		return nil, errors.New("no group or site")
//	}
//	m = m.Where(dao.Sitemap.Columns().DeleteTime, 0)
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.Sitemap.Table() + "." + dao.Sitemap.Columns().Id) // 最新在前面
//	}
//
//	// 查询列表
//	if req.GroupId != nil {
//		m = m.Fields("sitemap.*",
//			"site_group.name as belongName").
//			LeftJoin("site_group", "site_group.id = sitemap.belong_id")
//	} else {
//		m = m.Fields("sitemap.*",
//			"site.name as belongName").
//			LeftJoin("site", "site.id = sitemap.belong_id")
//	}
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.SitemapItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.SitemapItem, 0, total)
//	}
//
//	err = m.Scan(&res.List)
//	if err != nil {
//		return
//	}
//	res.Total = total
//	return
//}
//
//func (s *sSitemap) Add(ctx context.Context, req *v1.SitemapAddReq) (res *v1.SitemapAddRes, err error) {
//	res = new(v1.SitemapAddRes)
//
//	var entityData *entity.Sitemap
//	if err = gconv.Scan(req, &entityData); err != nil {
//		return
//	}
//	entityData.CreateTime = time.Now().UnixMilli()
//	self, err := service.Utility().GetSelf(ctx)
//	if err != nil {
//		return
//	}
//	entityData.Creater = self.Id
//
//	id, err := dao.Sitemap.Ctx(ctx).Cache(gdb.CacheOption{
//		Duration: -1,
//		Force:    false, // nil也缓存
//		Name:     consts.ColumnOneById + gconv.String(req.Belong) + ":" + gconv.String(req.BelongId),
//	}).InsertAndGetId(entityData)
//	if err != nil {
//		return
//	}
//
//	res = &v1.SitemapAddRes{
//		Id: uint(id),
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: entityData,
//	})
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sSitemap) deleteCache(ctx context.Context) {
//	g.Go(ctx, func(ctx context.Context) {
//		// 延迟一秒钟
//		<-time.After(1 * time.Second)
//
//		// 执行缓存更新操作
//		service.DbCache().PublishUpdate(ctx, model.MemoryUpdateMessage{
//			ClassId: consts.SitemapUpdate,
//			Key:     "",
//		})
//	}, nil)
//}
//
//func (s *sSitemap) Edit(ctx context.Context, req *v1.SitemapEditReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//
//	if req.Belong != nil {
//		update[dao.Sitemap.Columns().Belong] = req.Belong
//	}
//	if req.BelongId != nil {
//		update[dao.Sitemap.Columns().BelongId] = req.BelongId
//	}
//	if req.Status != nil {
//		update[dao.Sitemap.Columns().Status] = req.Status
//	}
//	if req.Remark != nil {
//		update[dao.Sitemap.Columns().Remark] = req.Remark
//	}
//	if req.Remark != nil {
//		update[dao.Sitemap.Columns().Remark] = req.Remark
//	}
//	if req.MainLevel != nil {
//		update[dao.Sitemap.Columns().MainLevel] = req.MainLevel
//	}
//	if req.MainRefreshRate != nil {
//		update[dao.Sitemap.Columns().MainRefreshRate] = req.MainRefreshRate
//	}
//	if req.ListLevel != nil {
//		update[dao.Sitemap.Columns().ListLevel] = req.ListLevel
//	}
//	if req.ListRefreshRate != nil {
//		update[dao.Sitemap.Columns().ListRefreshRate] = req.ListRefreshRate
//	}
//	if req.ContentLevel != nil {
//		update[dao.Sitemap.Columns().ContentLevel] = req.ContentLevel
//	}
//	if req.ContentRefreshRate != nil {
//		update[dao.Sitemap.Columns().ContentRefreshRate] = req.ContentRefreshRate
//	}
//	if req.CreateType != nil {
//		update[dao.Sitemap.Columns().CreateType] = req.CreateType
//	}
//	if req.Format != nil {
//		update[dao.Sitemap.Columns().Format] = req.Format
//	}
//	if req.Format != nil {
//		update[dao.Sitemap.Columns().LinkNum] = req.LinkNum
//	}
//
//	// 取旧数据
//	oldData, err := s.One(ctx, &v1.SitemapOneReq{
//		Id: req.Id,
//	})
//
//	update[dao.Sitemap.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	if _, err = dao.Sitemap.Ctx(ctx).Cache(gdb.CacheOption{
//		Duration: -1,
//		Force:    false, // nil也缓存
//		Name:     consts.SitemapOneById + gconv.String(req.Belong) + ":" + gconv.String(req.BelongId),
//	}).Where(dao.Sitemap.Columns().Id, req.Id).Update(update); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		OldRecordOrAttr: oldData,
//		NewRecordOrAttr: req,
//	})
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sSitemap) Delete(ctx context.Context, req *v1.SitemapDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	update[dao.Sitemap.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	update[dao.Sitemap.Columns().DeleteTime] = gtime.Now().UnixMilli()
//
//	// 取旧数据
//	oldData, err := s.One(ctx, &v1.SitemapOneReq{
//		Id: req.Id,
//	})
//	if err != nil {
//		return
//	}
//
//	if _, err = dao.Sitemap.Ctx(ctx).Cache(gdb.CacheOption{
//		Duration: -1,
//		Force:    false, // nil也缓存
//		Name:     consts.SitemapOneById + gconv.String(oldData.Belong) + ":" + gconv.String(oldData.BelongId),
//	}).Where(dao.Sitemap.Columns().Id, req.Id).Data(update).Update(); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: oldData,
//	})
//
//	s.deleteCache(ctx)
//	return
//}
//
//func (s *sSitemap) One(ctx context.Context, req *v1.SitemapOneReq) (res *v1.SitemapOneRes, err error) {
//	res = new(v1.SitemapOneRes)
//
//	sg, err := dao.Sitemap.Ctx(ctx).Where(dao.Sitemap.Columns().Id, req.Id).One()
//	if err != nil {
//		return
//	}
//
//	var item v1.SitemapItem
//	err = gconv.Scan(sg, &item)
//	if err != nil {
//		return
//	}
//	res = &v1.SitemapOneRes{
//		SitemapItem: item,
//	}
//	return
//}
//
//func (s *sSitemap) OneBySite(ctx context.Context, site *model.SiteItem) (one *entity.Sitemap) {
//	one = &entity.Sitemap{}
//	// 先取站点
//	record, err := dao.Sitemap.Ctx(ctx).Where(dao.Sitemap.Columns().BelongId, site.Id).One()
//	if err != nil {
//		return
//	}
//	if record != nil {
//		_ = record.Struct(&one)
//	}
//
//	if one.Id == 0 {
//		// 再取分组
//		record, err = dao.Sitemap.Ctx(ctx).Where(dao.Sitemap.Columns().BelongId, site.GroupId).One()
//		if err != nil {
//			return
//		}
//		if record != nil {
//			_ = record.Struct(&one)
//		}
//	}
//	if one.Format == 0 {
//		one.Format = 1
//	}
//	return
//}
//
////--------robots-------------
//
//func (s *sSitemap) RobotsList(ctx context.Context, req *v1.RobotsListReq) (res *v1.RobotsListRes, err error) {
//	res = new(v1.RobotsListRes)
//	res.Current = req.Current
//	res.List = make([]v1.RobotsItem, 0)
//
//	m := dao.Robots.Ctx(ctx).Handler(utility.CreatorFilter)
//	if req.Name != nil {
//		m = m.Where(dao.Robots.Columns().Name, req.Name)
//	}
//
//	m = m.Where(dao.Robots.Columns().DeleteTime, 0)
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.Robots.Table() + "." + dao.Robots.Columns().Id) // 最新在前面
//	}
//
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.RobotsItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.RobotsItem, 0, total)
//	}
//
//	err = m.Scan(&res.List)
//	if err != nil {
//		return
//	}
//	res.Total = total
//	return
//}
//
//func (s *sSitemap) RobotsAdd(ctx context.Context, req *v1.RobotsAddReq) (res *v1.RobotsAddRes, err error) {
//	res = new(v1.RobotsAddRes)
//
//	var entityData *entity.Robots
//	if err = gconv.Scan(req, &entityData); err != nil {
//		return
//	}
//	entityData.CreateTime = time.Now().UnixMilli()
//	self, err := service.Utility().GetSelf(ctx)
//	if err != nil {
//		return
//	}
//	entityData.Creater = self.Id
//
//	id, err := dao.Robots.Ctx(ctx).InsertAndGetId(entityData)
//	if err != nil {
//		return
//	}
//
//	res = &v1.RobotsAddRes{
//		Id: uint(id),
//	}
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: entityData,
//	})
//	//send to uapi
//	s.deleteRobotsCache(ctx)
//	return
//}
//
//func (s *sSitemap) deleteRobotsCache(ctx context.Context) {
//	g.Go(ctx, func(ctx context.Context) {
//		// 延迟一秒钟
//		<-time.After(1 * time.Second)
//
//		// 执行缓存更新操作
//		service.DbCache().PublishUpdate(ctx, model.MemoryUpdateMessage{
//			ClassId: consts.RobotsUpdate,
//			Key:     "",
//		})
//	}, nil)
//}
//
//func (s *sSitemap) RobotsEdit(ctx context.Context, req *v1.RobotsEditReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//
//	if req.Name != nil {
//		update[dao.Robots.Columns().Name] = req.Name
//	}
//	if req.Content != nil {
//		update[dao.Robots.Columns().Content] = req.Content
//	}
//
//	// 取旧数据
//	oldData, err := s.RobotsOne(ctx, &v1.RobotsOneReq{
//		Id: req.Id,
//	})
//
//	update[dao.Robots.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	if _, err = dao.Robots.Ctx(ctx).Where(dao.Robots.Columns().Id, req.Id).Update(update); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		OldRecordOrAttr: oldData,
//		NewRecordOrAttr: req,
//	})
//	//send to uapi
//	s.deleteRobotsCache(ctx)
//	return
//}
//
//func (s *sSitemap) RobotsDelete(ctx context.Context, req *v1.RobotsDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	update[dao.Robots.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	update[dao.Robots.Columns().DeleteTime] = gtime.Now().UnixMilli()
//
//	// 取旧数据
//	oldData, err := s.RobotsOne(ctx, &v1.RobotsOneReq{
//		Id: req.Id,
//	})
//	if err != nil {
//		return
//	}
//
//	if _, err = dao.Robots.Ctx(ctx).Where(dao.Sitemap.Columns().Id, req.Id).Data(update).Update(); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: oldData,
//	})
//	//send to uapi
//	s.deleteRobotsCache(ctx)
//	return
//}
//
//func (s *sSitemap) RobotsOne(ctx context.Context, req *v1.RobotsOneReq) (res *v1.RobotsOneRes, err error) {
//	res = new(v1.RobotsOneRes)
//
//	sg, err := dao.Robots.Ctx(ctx).Where(dao.Robots.Columns().Id, req.Id).One()
//	if err != nil {
//		return
//	}
//
//	var item v1.RobotsItem
//	err = gconv.Scan(sg, &item)
//	if err != nil {
//		return
//	}
//	res = &v1.RobotsOneRes{
//		RobotsItem: item,
//	}
//	return
//}
