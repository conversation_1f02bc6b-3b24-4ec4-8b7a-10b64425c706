package sitemap

//
//import (
//	"bytes"
//	"context"
//	"encoding/xml"
//	"fmt"
//	"github.com/gogf/gf/v2/database/gdb"
//	"github.com/gogf/gf/v2/frame/g"
//	"github.com/gogf/gf/v2/os/gctx"
//	"github.com/gogf/gf/v2/os/gtime"
//	"github.com/gogf/gf/v2/util/gconv"
//	"gtcms/internal/consts"
//	"gtcms/internal/dao"
//	"gtcms/internal/model"
//	"gtcms/internal/service"
//	"gtcms/utility"
//	"strings"
//	"time"
//)
//
//var (
//	ctx = gctx.New()
//)
//
//// URL表示一个Sitemap条目
//type URL struct {
//	Loc        string `xml:"loc"`
//	LastMod    string `xml:"lastmod"`
//	ChangeFreq string `xml:"changefreq"`
//	Priority   string `xml:"priority"`
//}
//
//// Sitemap表示一个Sitemap文件
//type Sitemap struct {
//	XMLName xml.Name `xml:"urlset"`
//	XmlNS   string   `xml:"xmlns,attr"`
//	URLs    []URL    `xml:"url"`
//}
//
//// SitemapIndex表示一个Sitemap索引文件
//type SitemapIndex struct {
//	XMLName  xml.Name       `xml:"sitemapindex"`
//	XmlNS    string         `xml:"xmlns,attr"`
//	Sitemaps []SitemapEntry `xml:"sitemap"`
//}
//
//// SitemapEntry表示一个Sitemap索引条目
//type SitemapEntry struct {
//	Loc     string `xml:"loc"`
//	LastMod string `xml:"lastmod"`
//}
//
//func (s *sSitemap) run(init bool, sList []*model.SiteItem) {
//	startTime := time.Now()
//	g.Log().Line().Info(ctx, "开始生成sitemap")
//	var siteList []*model.SiteItem
//	if sList != nil && len(sList) > 0 {
//		siteList = sList
//	} else {
//		if err := dao.Site.Ctx(ctx).Fields(dao.Site.Table()+".*",
//			dao.Domain.Table()+"."+dao.Domain.Columns().Name+" AS domainName").
//			LeftJoin(dao.Domain.Table()+" ON "+dao.Site.Table()+"."+
//				dao.Site.Columns().DomainId+"="+dao.Domain.Table()+"."+dao.Domain.Columns().Id).
//			Where(dao.Site.Columns().Status, 1).
//			Where(dao.Site.Columns().DeleteTime, 0).
//			WhereGT(dao.Domain.Columns().ExpireTime, time.Now().UnixMilli()).OrderDesc(dao.Site.Columns().Id).
//			Scan(&siteList); err != nil {
//			return
//		}
//	}
//
//	// 足球联赛
//	var allFbComp gdb.Result
//	allFbCompMap := make(map[int]string)
//	allFbComp, err := dao.CollectFootballCompetition.Ctx(ctx).Fields(dao.CollectFootballCompetition.Columns().Id,
//		dao.CollectFootballCompetition.Columns().ShortNameZh).
//		OrderAsc(dao.CollectFootballCompetition.Columns().Id).All()
//	if err != nil {
//		return
//	}
//	for _, v := range allFbComp {
//		compName := gconv.String(v[dao.CollectFootballCompetition.Columns().ShortNameZh])
//		allFbCompMap[gconv.Int(v[dao.CollectFootballCompetition.Columns().Id])] = compName
//	}
//
//	// 篮球联赛
//	var allBkComp gdb.Result
//	allBkCompMap := make(map[int]string)
//	allBkComp, err = dao.CollectBasketballCompetition.Ctx(ctx).Fields(dao.CollectBasketballCompetition.Columns().Id,
//		dao.CollectBasketballCompetition.Columns().ShortNameZh).
//		OrderAsc(dao.CollectBasketballCompetition.Columns().Id).All()
//	if err != nil {
//		return
//	}
//	for _, v := range allBkComp {
//		compName := service.Utility().GetPinyin(gconv.String(v[dao.CollectBasketballCompetition.Columns().ShortNameZh]))
//		allBkCompMap[gconv.Int(v[dao.CollectBasketballCompetition.Columns().Id])] = compName
//	}
//
//	// 足球赛事-未来24小时的比赛
//	now := time.Now()
//	now = now.Add(24 * time.Hour)
//	nowUnix := now.Unix()
//	allFbMatch, _ := s.makeFbMatchLive(nowUnix)
//
//	// 篮球赛事-未来24小时的比赛
//	allBkMatch, _ := s.makeBkMatchLive(nowUnix)
//
//	// 足球赛事-小于当前时间3个小时后的比赛
//	//now = time.Now()
//	//now = now.Add(3 * time.Hour)
//	//nowUnix = now.Unix()
//	//allFbMatch2, _ := s.makeFbMatchRecord(nowUnix)
//
//	// 篮球赛事-小于当前时间3个小时后的比赛
//	//allBkMatch2, _ := s.makeBkMatchRecord(nowUnix)
//
//	for _, site := range siteList {
//		routerList := make([]string, 0)
//		do, err := g.Redis().Do(ctx, "GET", gconv.String(consts.SitemapBySiteId+gconv.String(site.Id)))
//		if err != nil {
//			return
//		}
//		if !do.IsNil() && init {
//			continue
//		}
//
//		usRes, _ := service.UrlSetting().FastFill(ctx, site, consts.UrlCategoryMatchColumn)
//		for _, v := range allFbMatch {
//			url := s.makeMatchUrl(ctx, site, model.MatchMakeUrl{
//				Id:         consts.SportTypeFootball + gconv.String(v[dao.CollectFootballMatch.Columns().Id]),
//				CreateTime: gconv.Int64(v[dao.CollectFootballMatch.Columns().MatchTs]),
//				CompName:   allFbCompMap[gconv.Int(v[dao.CollectFootballMatch.Columns().CompetitionId])],
//				UrlMode:    usRes.Mode,
//				UrlContent: usRes.Content,
//				ModuleId:   consts.ModLiveFootball,
//			})
//			routerList = append(routerList, url)
//		}
//
//		for _, v := range allBkMatch {
//			url := s.makeMatchUrl(ctx, site, model.MatchMakeUrl{
//				Id:         consts.SportTypeBasketball + gconv.String(v[dao.CollectBasketballMatch.Columns().Id]),
//				CreateTime: gconv.Int64(v[dao.CollectBasketballMatch.Columns().MatchTs]),
//				CompName:   allBkCompMap[gconv.Int(v[dao.CollectBasketballMatch.Columns().CompetitionId])],
//				UrlMode:    usRes.Mode,
//				UrlContent: usRes.Content,
//				ModuleId:   consts.ModLiveFootball,
//			})
//			routerList = append(routerList, url)
//		}
//
//		if do.IsNil() { // 首次生成
//			//usRes, _ = service.UrlSetting().FastFill(ctx, site, consts.UrlCategoryRecordDetail)
//			//for _, v := range allFbMatch2 {
//			//	url := s.makeMatchUrl(ctx, site, model.MatchMakeUrl{
//			//		Id:         consts.SportTypeFootball + gconv.String(v[dao.CollectFootballMatch.Columns().Id]),
//			//		CreateTime: gconv.Int64(v[dao.CollectFootballMatch.Columns().MatchTs]),
//			//		CompName:   allFbCompMap[gconv.Int(v[dao.CollectFootballMatch.Columns().CompetitionId])],
//			//		UrlMode:    usRes.Mode,
//			//		UrlContent: usRes.Content,
//			//	})
//			//	routerList = append(routerList, service.Utility().RemoveTrailingLeadingSlash(url))
//			//}
//
//			//for _, v := range allBkMatch2 {
//			//	url := s.makeMatchUrl(ctx, site, model.MatchMakeUrl{
//			//		Id:         consts.SportTypeBasketball + gconv.String(v[dao.CollectBasketballMatch.Columns().Id]),
//			//		CreateTime: gconv.Int64(v[dao.CollectBasketballMatch.Columns().MatchTs]),
//			//		CompName:   allBkCompMap[gconv.Int(v[dao.CollectBasketballMatch.Columns().CompetitionId])],
//			//		UrlMode:    usRes.Mode,
//			//		UrlContent: usRes.Content,
//			//	})
//			//	routerList = append(routerList, service.Utility().RemoveTrailingLeadingSlash(url))
//			//}
//		}
//
//		// 新闻
//		rList5, _ := s.makeNews(site)
//
//		var rList6, rList7, rList8, rList9, rList10, rList11 []string
//		if do.IsNil() { // 首次生成
//			// 足球联赛
//			rList6, _ = s.makeFbComp(site, allFbComp)
//			// 篮球联赛
//			rList7, _ = s.makeBkComp(site, allBkComp)
//			// 足球球队
//			rList8, _ = s.makeFbTeam(site)
//			//// 篮球球队
//			//rList9, _ = s.makeBkTeam(site)
//			// 足球球员
//			rList10, _ = s.makeFbPlayer(site)
//			//// 篮球球员
//			//rList11, _ = s.makeBkPlayer(site)
//		}
//
//		routerList = append(routerList, rList5...)
//		routerList = append(routerList, rList6...)
//		routerList = append(routerList, rList7...)
//		routerList = append(routerList, rList8...)
//		routerList = append(routerList, rList9...)
//		routerList = append(routerList, rList10...)
//		routerList = append(routerList, rList11...)
//		s.save(routerList, site)
//		_, _ = g.Redis().Do(ctx, "SET", gconv.String(consts.SitemapBySiteId+gconv.String(site.Id)), 1)
//	}
//	g.Log().Line().Info(ctx, "结束生成sitemap,耗时：", time.Since(startTime))
//}
//
//func (s *sSitemap) makeFbMatchLive(nowUnix int64) (allFbMatch gdb.Result, err error) {
//	allFbMatch, err = dao.CollectFootballMatch.Ctx(ctx).Fields(dao.CollectFootballMatch.Columns().Id,
//		dao.CollectFootballMatch.Columns().CompetitionId, dao.CollectFootballMatch.Columns().MatchTs).
//		WhereGTE(dao.CollectFootballMatch.Columns().MatchTs, time.Now().Unix()).
//		WhereLTE(dao.CollectFootballMatch.Columns().MatchTs, nowUnix).
//		OrderAsc(dao.CollectFootballMatch.Columns().MatchTs).All()
//	if err != nil {
//		return
//	}
//	return
//}
//
//func (s *sSitemap) makeBkMatchLive(nowUnix int64) (allBkMatch gdb.Result, err error) {
//	allBkMatch, err = dao.CollectBasketballMatch.Ctx(ctx).Fields(dao.CollectBasketballMatch.Columns().Id,
//		dao.CollectBasketballMatch.Columns().CompetitionId, dao.CollectBasketballMatch.Columns().MatchTs).
//		WhereGTE(dao.CollectFootballMatch.Columns().MatchTs, time.Now().Unix()).
//		WhereLTE(dao.CollectFootballMatch.Columns().MatchTs, nowUnix).
//		OrderAsc(dao.CollectBasketballMatch.Columns().MatchTs).All()
//	if err != nil {
//		return
//	}
//	return
//}
//
//func (s *sSitemap) makeFbMatchRecord(nowUnix int64) (allFbMatch gdb.Result, err error) {
//	allFbMatch, err = dao.CollectFootballMatch.Ctx(ctx).Fields(dao.CollectFootballMatch.Columns().Id,
//		dao.CollectFootballMatch.Columns().CompetitionId, dao.CollectFootballMatch.Columns().MatchTs).
//		WhereLT(dao.CollectFootballMatch.Columns().MatchTs, nowUnix).
//		OrderAsc(dao.CollectFootballMatch.Columns().MatchTs).All()
//	if err != nil {
//		return
//	}
//	return
//}
//
//func (s *sSitemap) makeBkMatchRecord(nowUnix int64) (allBkMatch gdb.Result, err error) {
//	allBkMatch, err = dao.CollectBasketballMatch.Ctx(ctx).Fields(dao.CollectBasketballMatch.Columns().Id,
//		dao.CollectBasketballMatch.Columns().CompetitionId, dao.CollectBasketballMatch.Columns().MatchTs).
//		WhereLT(dao.CollectBasketballMatch.Columns().MatchTs, nowUnix).
//		OrderAsc(dao.CollectBasketballMatch.Columns().MatchTs).All()
//	if err != nil {
//		return
//	}
//	return
//}
//
//func (s *sSitemap) makeFbComp(site *model.SiteItem, allFbComp gdb.Result) (routerList []string, err error) {
//	usRes, _ := service.UrlSetting().FastFill(ctx, site, consts.UrlCategoryMatchColumn)
//	for _, v := range allFbComp {
//		compName := gconv.String(v[dao.CollectFootballCompetition.Columns().ShortNameZh])
//		url := s.makeCompUrl(model.MatchMakeUrl{
//			CompName:   service.Utility().GetPinyin(compName),
//			UrlMode:    usRes.Mode,
//			UrlContent: usRes.Content,
//		})
//		routerList = append(routerList, url)
//	}
//	return
//}
//
//func (s *sSitemap) makeBkComp(site *model.SiteItem, allBkComp gdb.Result) (routerList []string, err error) {
//	// 篮球联赛
//	usRes, _ := service.UrlSetting().FastFill(ctx, site, consts.UrlCategoryMatchColumn)
//	for _, v := range allBkComp {
//		compName := service.Utility().GetPinyin(gconv.String(v[dao.CollectBasketballCompetition.Columns().ShortNameZh]))
//		url := s.makeCompUrl(model.MatchMakeUrl{
//			CompName:   service.Utility().GetPinyin(compName),
//			UrlMode:    usRes.Mode,
//			UrlContent: usRes.Content,
//		})
//		routerList = append(routerList, url)
//	}
//	return
//}
//
//func (s *sSitemap) makeFbTeam(site *model.SiteItem) (routerList []string, err error) {
//	var allFbTeam gdb.Result
//	allFbTeam, err = dao.CollectFootballTeam.Ctx(ctx).Fields(dao.CollectFootballTeam.Columns().Id,
//		dao.CollectFootballTeam.Columns().NameEn).All()
//	if err != nil {
//		return
//	}
//	usRes, _ := service.UrlSetting().FastFill(ctx, site, consts.UrlCategoryTeam)
//	for _, v := range allFbTeam {
//		url := s.makeTeamUrl(model.TeamMakeUrl{
//			Id:         gconv.Int(v[dao.CollectFootballTeam.Columns().Id]),
//			NameEn:     gconv.String(v[dao.CollectFootballTeam.Columns().NameEn]),
//			UrlMode:    usRes.Mode,
//			UrlContent: usRes.Content,
//		})
//		routerList = append(routerList, url)
//	}
//	return
//}
//
//func (s *sSitemap) makeBkTeam(site *model.SiteItem) (routerList []string, err error) {
//	var allBkTeam gdb.Result
//	allBkTeam, err = dao.CollectBasketballTeam.Ctx(ctx).Fields(dao.CollectBasketballTeam.Columns().Id,
//		dao.CollectBasketballTeam.Columns().NameEn).All()
//	if err != nil {
//		return
//	}
//	usRes, _ := service.UrlSetting().FastFill(ctx, site, consts.UrlCategoryTeam)
//	for _, v := range allBkTeam {
//		url := s.makeTeamUrl(model.TeamMakeUrl{
//			Id:         gconv.Int(v[dao.CollectBasketballTeam.Columns().Id]),
//			NameEn:     gconv.String(v[dao.CollectBasketballTeam.Columns().NameEn]),
//			UrlMode:    usRes.Mode,
//			UrlContent: usRes.Content,
//		})
//		routerList = append(routerList, url)
//	}
//	return
//}
//
//func (s *sSitemap) makeFbPlayer(site *model.SiteItem) (routerList []string, err error) {
//	var allFbPlayer gdb.Result
//	allFbPlayer, err = dao.CollectFootballPlayer.Ctx(ctx).Fields(dao.CollectFootballPlayer.Columns().Id,
//		dao.CollectFootballPlayer.Columns().NameEn).All()
//	if err != nil {
//		return
//	}
//	usRes, _ := service.UrlSetting().FastFill(ctx, site, consts.UrlCategoryPlayer)
//	for _, v := range allFbPlayer {
//		url := s.makePlayerUrl(model.TeamMakeUrl{
//			Id:         gconv.Int(v[dao.CollectFootballPlayer.Columns().Id]),
//			NameEn:     gconv.String(v[dao.CollectFootballPlayer.Columns().NameEn]),
//			UrlMode:    usRes.Mode,
//			UrlContent: usRes.Content,
//		})
//		routerList = append(routerList, url)
//	}
//	return
//}
//
//func (s *sSitemap) makeBkPlayer(site *model.SiteItem) (routerList []string, err error) {
//	var allFbPlayer gdb.Result
//	allFbPlayer, err = dao.CollectBasketballPlayer.Ctx(ctx).Fields(dao.CollectBasketballPlayer.Columns().Id,
//		dao.CollectBasketballPlayer.Columns().NameEn).All()
//	if err != nil {
//		return
//	}
//	usRes, _ := service.UrlSetting().FastFill(ctx, site, consts.UrlCategoryPlayer)
//	for _, v := range allFbPlayer {
//		url := s.makePlayerUrl(model.TeamMakeUrl{
//			Id:         gconv.Int(v[dao.CollectBasketballPlayer.Columns().Id]),
//			NameEn:     gconv.String(v[dao.CollectBasketballPlayer.Columns().NameEn]),
//			UrlMode:    usRes.Mode,
//			UrlContent: usRes.Content,
//		})
//		routerList = append(routerList, url)
//	}
//	return
//}
//
//func (s *sSitemap) makeNews(site *model.SiteItem) (routerList []string, err error) {
//	start, end := service.Utility().GetYesterdayTimestamps()
//	var allNews gdb.Result
//	allNews, err = dao.News.Ctx(ctx).Fields(dao.News.Columns().Id, dao.News.Columns().BelongColId,
//		dao.News.Columns().CreateTime).
//		Where(dao.News.Columns().BelongSiteId, site.Id).
//		Where(dao.News.Columns().DeleteTime, 0).
//		WhereGT(dao.News.Columns().CreateTime, start).
//		WhereLT(dao.News.Columns().CreateTime, end).OrderDesc(dao.News.Columns().Id).All()
//	if err != nil {
//		return
//	}
//	for _, v := range allNews {
//		in := &model.NewsMakeUrl{
//			Id:         gconv.Uint(v[dao.News.Columns().Id]),
//			CreateTime: gconv.Int64(v[dao.News.Columns().CreateTime]),
//			ColumnsId:  gconv.Uint(v[dao.News.Columns().BelongColId]),
//		}
//		url := s.makeNewsUrl(ctx, site, in)
//		routerList = append(routerList, url)
//	}
//	return
//}
//
//func (s *sSitemap) save(urlList []string, site *model.SiteItem) {
//	g.Log().Line().Info(ctx, "开始生成sitemap", site.DomainName)
//
//	batchSize := 5000
//	one := s.OneBySite(ctx, site)
//	if one != nil {
//		batchSize = utility.If(one.LinkNum > 0, one.LinkNum, batchSize)
//	}
//
//	baseURL := "https://" + service.Utility().AddWWWPrefix(site.DomainName) + "/"
//	var sitemapFiles []string
//	var txtFiles []string
//
//	// 获取最大索引
//	maxIdx := 0
//	oldMaxIdx := 0
//	do, err := g.Redis().Do(ctx, "GET", gconv.String(consts.SitemapBySiteIdWithMaxIdx+gconv.String(site.Id)))
//	if err != nil {
//		return
//	}
//	if !do.IsNil() {
//		maxIdx = do.Int()
//		oldMaxIdx = maxIdx
//	}
//
//	for i := 0; i < len(urlList); i += batchSize {
//		var urls []URL
//		var txtUrls []string
//		for j := i; j < i+batchSize && j < len(urlList); j++ {
//			loc := baseURL + gconv.String(urlList[j])
//			urls = append(urls, URL{
//				Loc:        loc,
//				LastMod:    time.Now().Format("2006-01-02"),
//				ChangeFreq: "daily",
//				Priority:   "0.5",
//			})
//			txtUrls = append(txtUrls, loc)
//		}
//
//		sitemap := Sitemap{
//			XmlNS: "http://www.sitemaps.org/schemas/sitemap/0.9",
//			URLs:  urls,
//		}
//
//		var buf bytes.Buffer
//		encoder := xml.NewEncoder(&buf)
//		encoder.Indent("  ", "    ")
//		if err := encoder.Encode(sitemap); err != nil {
//			g.Log().Error(ctx, "生成Sitemap XML失败", err)
//			return
//		}
//
//		fileIdx := fmt.Sprintf("sitemap_%d.xml", maxIdx+1)
//		sitemapFiles = append(sitemapFiles, fileIdx)
//
//		xmlName := "sm/" + gconv.String(site.Id) + "/" + fileIdx
//		contentReader := bytes.NewReader(buf.Bytes())
//
//		_, err := service.File().SingleUpload(ctx, model.SingleUploadInput{
//			File: contentReader,
//			Name: xmlName,
//		})
//		if err != nil {
//			g.Log().Error(ctx, "上传Sitemap XML文件到云存储失败", err)
//			return
//		}
//		buf.Reset() // 重置 buffer
//
//		var txtBuf strings.Builder
//		for _, url := range txtUrls {
//			if _, err := txtBuf.WriteString(url + "\n"); err != nil {
//				g.Log().Error(ctx, "写入Sitemap TXT内容失败", err)
//				return
//			}
//		}
//
//		txtFileIdx := fmt.Sprintf("sitemap_%d.txt", maxIdx+1)
//		txtFiles = append(txtFiles, txtFileIdx)
//
//		txtName := "sm/" + gconv.String(site.Id) + "/" + txtFileIdx
//		contentReader2 := strings.NewReader(txtBuf.String())
//
//		_, err = service.File().SingleUpload(ctx, model.SingleUploadInput{
//			File: contentReader2,
//			Name: txtName,
//		})
//		if err != nil {
//			g.Log().Error(ctx, "上传Sitemap TXT文件到云存储失败", err)
//			return
//		}
//		maxIdx++ // 更新索引
//	}
//
//	var sitemaps []SitemapEntry
//	sitemapFiles = s.genSitemapFiles(oldMaxIdx, sitemapFiles, "xml")
//	for _, file := range sitemapFiles {
//		loc := baseURL + file
//		sitemaps = append(sitemaps, SitemapEntry{
//			Loc:     loc,
//			LastMod: time.Now().Format("2006-01-02"),
//		})
//	}
//
//	sitemapIndex := SitemapIndex{
//		XmlNS:    "http://www.sitemaps.org/schemas/sitemap/0.9",
//		Sitemaps: sitemaps,
//	}
//
//	var indexBuf bytes.Buffer
//	encoder := xml.NewEncoder(&indexBuf)
//	encoder.Indent("  ", "    ")
//	if err := encoder.Encode(sitemapIndex); err != nil {
//		g.Log().Error(ctx, "生成Sitemap索引 XML失败", err)
//		return
//	}
//
//	xmlIndexName := "sm/" + gconv.String(site.Id) + "/sitemap.xml"
//	contentReaderIndex := bytes.NewReader(indexBuf.Bytes())
//
//	_, err = service.File().SingleUpload(ctx, model.SingleUploadInput{
//		File: contentReaderIndex,
//		Name: xmlIndexName,
//	})
//	if err != nil {
//		g.Log().Error(ctx, "上传Sitemap索引文件到云存储失败", err)
//		return
//	}
//	indexBuf.Reset() // 重置 buffer
//
//	var txtIndexContent strings.Builder
//	txtFiles = s.genSitemapFiles(oldMaxIdx, txtFiles, "txt")
//	for _, file := range txtFiles {
//		loc := baseURL + file
//		if _, err := txtIndexContent.WriteString(loc + "\n"); err != nil {
//			g.Log().Error(ctx, "写入Sitemap TXT索引内容失败", err)
//			return
//		}
//	}
//
//	txtIndexName := "sm/" + gconv.String(site.Id) + "/sitemap.txt"
//	contentReaderTxtIndex := strings.NewReader(txtIndexContent.String())
//
//	_, err = service.File().SingleUpload(ctx, model.SingleUploadInput{
//		File: contentReaderTxtIndex,
//		Name: txtIndexName,
//	})
//	if err != nil {
//		g.Log().Error(ctx, "上传Sitemap TXT索引文件到云存储失败", err)
//		return
//	}
//
//	g.Log().Line().Info(ctx, "生成sitemap成功", site.DomainName)
//
//	// 保存最大下标值
//	_, _ = g.Redis().Do(ctx, "SET", gconv.String(consts.SitemapBySiteIdWithMaxIdx+gconv.String(site.Id)), maxIdx)
//}
//
//func (s *sSitemap) makeMatchUrl(ctx context.Context, site *model.SiteItem, params model.MatchMakeUrl) (url string) {
//	alias := ""
//	col, _ := service.Columns().GetColumnsByModuleId(ctx, site, gconv.String(params.ModuleId))
//	// 取一级栏目
//	if col != nil {
//		alias = col.Alias
//	}
//	switch params.UrlMode {
//	case consts.UrlModeInherit: // 继承模式
//		if col != nil {
//			url += col.Alias + "/"
//		}
//		//url += alias + "/"
//		url += params.Id + ".html"
//	case consts.UrlModeCustom: // 自定义模式 {columnName} {year} {mouth} {day} {letter} {number}
//		//year := gtime.NewFromTimeStamp(params.CreateTime).Format("Y")
//		//mouth := gtime.NewFromTimeStamp(params.CreateTime).Format("m")
//		//day := gtime.NewFromTimeStamp(params.CreateTime).Format("d")
//		//letter := ""
//		//number := ""
//		//params.UrlContent = service.Utility().ParseCustomContent(params.UrlContent, "", alias, "", "", "", "", "", "", "",
//		//	year, mouth, day, letter, number)
//		params.UrlContent = service.Utility().ParseCustomContent(params.UrlContent, "", alias, "", "", "", "", "", "", "",
//			"", "", "", "", "")
//		params.UrlContent = service.Utility().FormatPath(params.UrlContent)
//		url = params.UrlContent + params.Id + ".html"
//	default: // 根目录模式
//		url = params.Id + ".html"
//	}
//	url = service.Utility().RemoveSpacesAndSymbols(url)
//	return
//}
//
//// 构建联赛url
//func (s *sSitemap) makeCompUrl(params model.MatchMakeUrl) (url string) {
//	alias := params.CompName
//	switch params.UrlMode {
//	case consts.UrlModeInherit: // 继承模式
//		url = alias + "/"
//	case consts.UrlModeCustom: // 自定义模式 {columnName} {year} {mouth} {day} {letter} {number}
//		params.UrlContent = service.Utility().ParseCustomContent(params.UrlContent, "", alias)
//		params.UrlContent = service.Utility().FormatPath(params.UrlContent)
//		url = params.UrlContent
//	default: // 根目录模式
//	}
//	url = service.Utility().RemoveSpacesAndSymbols(url)
//	return
//}
//
//func (s *sSitemap) makeTeamUrl(params model.TeamMakeUrl) (url string) {
//	params.NameEn = strings.ReplaceAll(params.NameEn, " ", "")
//	switch params.UrlMode {
//	case consts.UrlModeCustom: // 自定义模式 {columnName} {year} {mouth} {day} {letter} {number}
//		params.UrlContent = service.Utility().ParseCustomContent(params.UrlContent, "", "", "", "",
//			"", "", "", "", "", "", "", "", "", "", "", params.NameEn, gconv.String(params.Id))
//		params.UrlContent = service.Utility().FormatPath(params.UrlContent)
//		url = params.UrlContent
//	case consts.UrlModePage:
//		params.UrlContent = service.Utility().ParseCustomContent(params.UrlContent, "", "", "", "",
//			"", "", "", "", "", "", "", "", "", "", "", params.NameEn, gconv.String(params.Id))
//		params.UrlContent = service.Utility().FormatPath(params.UrlContent)
//		url = service.Utility().RemoveTrailingLeadingSlash(params.UrlContent)
//		url += ".html"
//	default: // 根目录模式
//	}
//	url = service.Utility().RemoveSpacesAndSymbols(url)
//	return
//}
//
//func (s *sSitemap) makePlayerUrl(params model.TeamMakeUrl) (url string) {
//	params.NameEn = strings.ReplaceAll(params.NameEn, " ", "")
//	switch params.UrlMode {
//	case consts.UrlModeCustom: // 自定义模式 {columnName} {year} {mouth} {day} {letter} {number}
//		params.UrlContent = service.Utility().ParseCustomContent(params.UrlContent, "", "", "", "",
//			"", "", "", "", "", "", "", "", "", "", "", "", "", params.NameEn, gconv.String(params.Id))
//		params.UrlContent = service.Utility().FormatPath(params.UrlContent)
//		url = params.UrlContent
//	case consts.UrlModePage:
//		params.UrlContent = service.Utility().ParseCustomContent(params.UrlContent, "", "", "", "",
//			"", "", "", "", "", "", "", "", "", "", "", "", "", params.NameEn, gconv.String(params.Id))
//		params.UrlContent = service.Utility().FormatPath(params.UrlContent)
//		url = service.Utility().RemoveTrailingLeadingSlash(params.UrlContent)
//		url += ".html"
//	default: // 根目录模式
//	}
//	url = service.Utility().RemoveSpacesAndSymbols(url)
//	return
//}
//
//func (s *sSitemap) makeNewsUrl(ctx context.Context, site *model.SiteItem, params *model.NewsMakeUrl) (url string) {
//	usRes, _ := service.UrlSetting().FastFill(ctx, site, consts.UrlCategoryNewsColumns)
//	one, _ := service.Columns().One(ctx, params.ColumnsId)
//	alias := ""
//	if one != nil {
//		alias = one.Alias
//		params.ModuleId = one.ModuleId
//
//		if one.Pid != 0 {
//			oneParent, _ := service.Columns().One(ctx, gconv.Uint(one.Pid))
//			if oneParent != nil {
//				alias = oneParent.Alias + "/" + alias
//			}
//		}
//	}
//
//	switch usRes.Mode {
//	case consts.UrlModeInherit: // 继承模式
//		if alias != "" {
//			url = alias + "/" + gconv.String(params.Id) + ".html"
//		}
//	case consts.UrlModeCustom: // 自定义模式 {columnName} {year} {mouth} {day}
//		year := gtime.NewFromTimeStamp(params.CreateTime).Format("Y")
//		mouth := gtime.NewFromTimeStamp(params.CreateTime).Format("m")
//		day := gtime.NewFromTimeStamp(params.CreateTime).Format("d")
//		usRes.Content = service.Utility().ParseCustomContent(usRes.Content, "", alias, "", "", "", "", "", "", "", year, mouth, day)
//		usRes.Content = service.Utility().FormatPath(usRes.Content)
//		url = usRes.Content + gconv.String(params.Id) + ".html"
//	default: // 根目录模式
//		url = gconv.String(params.Id) + ".html"
//	}
//	url = strings.ReplaceAll(url, " ", "")
//	return
//}
//
//func (s *sSitemap) genSitemapFiles(maxIdx int, sitemapFiles []string, fileType string) []string {
//	for i := 1; i < maxIdx; i++ {
//		fileIdx := fmt.Sprintf("sitemap_%d."+fileType, i)
//		sitemapFiles = append(sitemapFiles, fileIdx)
//	}
//	return sitemapFiles
//}
