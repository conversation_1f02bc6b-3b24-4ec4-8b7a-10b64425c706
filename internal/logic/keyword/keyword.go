package keyword

//
//import (
//	"context"
//	"github.com/gogf/gf/v2/errors/gerror"
//	"github.com/gogf/gf/v2/frame/g"
//	"github.com/gogf/gf/v2/os/gtime"
//	"github.com/gogf/gf/v2/util/gconv"
//	v1 "gtcms/api/v1"
//	"gtcms/internal/dao"
//	"gtcms/internal/logic/basicLib"
//	"gtcms/internal/model"
//	"gtcms/internal/model/entity"
//	"gtcms/internal/service"
//	"gtcms/utility"
//)
//
//type sKeyword struct{}
//
//func init() {
//	service.RegisterKeyword(New())
//}
//
//func New() *sKeyword {
//	s := &sKeyword{}
//	return s
//}
//
//func (s *sKeyword) List(ctx context.Context, req *v1.KeywordListReq) (res *v1.KeywordListRes, err error) {
//	res = new(v1.KeywordListRes)
//	res.Current = req.Current
//	res.List = make([]v1.KeywordItem, 0)
//
//	m := dao.Keyword.Ctx(ctx).Where(dao.Keyword.Columns().Belong, req.Belong).Handler(utility.CreatorFilter)
//	if req.Name != nil {
//		m = m.WhereLike(dao.Keyword.Columns().Name, utility.WhereLike(*req.Name))
//	}
//	if req.Status != nil {
//		m = m.Where(dao.Keyword.Columns().Status, *req.Status)
//	}
//	if req.BelongId != nil {
//		m = m.Where(dao.Keyword.Columns().BelongId, *req.BelongId)
//	}
//
//	m = m.Where(dao.Keyword.Columns().DeleteTime, 0)
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.Keyword.Table() + "." + dao.Keyword.Columns().Id) // 最新在前面
//	}
//
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.KeywordItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.KeywordItem, 0, total)
//	}
//
//	err = m.Scan(&res.List)
//	if err != nil {
//		return
//	}
//
//	res.Total = total
//	return
//}
//
//func (s *sKeyword) Add(ctx context.Context, req *v1.KeywordAddReq) (res *v1.KeywordAddRes, err error) {
//	res = new(v1.KeywordAddRes)
//
//	m := dao.Keyword.Ctx(ctx).Where(dao.Keyword.Columns().Belong, req.Belong)
//	var entityData *entity.Keyword
//	if err = gconv.Scan(req, &entityData); err != nil {
//		return
//	}
//
//	entityData.CreateTime = gtime.Now().UnixMilli()
//	self, err := service.Utility().GetSelf(ctx)
//	if err != nil {
//		return
//	}
//	entityData.Creater = self.Id
//
//	id, err := m.InsertAndGetId(entityData)
//	if err != nil {
//		return
//	}
//
//	res = &v1.KeywordAddRes{
//		Id: uint(id),
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: entityData,
//	})
//	return
//}
//
//func (s *sKeyword) Edit(ctx context.Context, req *v1.KeywordEditReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	if req.Name != nil {
//		update[dao.Keyword.Columns().Name] = req.Name
//	}
//	if req.Url != nil {
//		update[dao.Keyword.Columns().Url] = req.Url
//	}
//	if req.Remark != nil {
//		update[dao.Keyword.Columns().Remark] = req.Remark
//	}
//	if req.Status != nil {
//		update[dao.Keyword.Columns().Status] = req.Status
//	}
//	if req.LinkProperty != nil {
//		update[dao.Keyword.Columns().LinkProperty] = req.LinkProperty
//	}
//
//	// 取旧数据
//	oldData, err := s.One(ctx, &v1.KeywordOneReq{
//		Id: req.Id,
//	})
//
//	m := dao.Keyword.Ctx(ctx)
//	update[dao.Keyword.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	if _, err = m.Where(dao.Keyword.Columns().Id, req.Id).Update(update); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		OldRecordOrAttr: oldData,
//		NewRecordOrAttr: req,
//	})
//	return
//}
//
//func (s *sKeyword) Delete(ctx context.Context, req *v1.KeywordDeleteReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//
//	update := g.Map{}
//	update[dao.Keyword.Columns().UpdateTime] = gtime.Now().UnixMilli()
//	update[dao.Keyword.Columns().DeleteTime] = gtime.Now().UnixMilli()
//
//	var ids []uint
//	if req.Id != 0 {
//		ids = append(ids, req.Id)
//	}
//	if len(req.Ids) > 0 {
//		ids = append(ids, req.Ids...)
//	}
//	if len(ids) < 1 {
//		err = gerror.New("id is empty")
//		return
//	}
//
//	// 取旧数据
//	oldData, err := s.One(ctx, &v1.KeywordOneReq{
//		Id: ids[0],
//	})
//
//	m := dao.Keyword.Ctx(ctx)
//	if _, err = m.Where(dao.Keyword.Columns().Id, ids).Data(update).Update(); err != nil {
//		return
//	}
//
//	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
//		NewRecordOrAttr: oldData,
//	})
//	return
//}
//
//func (s *sKeyword) One(ctx context.Context, req *v1.KeywordOneReq) (res *v1.KeywordOneRes, err error) {
//	res = new(v1.KeywordOneRes)
//
//	sg, err := dao.Keyword.Ctx(ctx).Where(dao.Keyword.Columns().Id, req.Id).One()
//	if err != nil {
//		return
//	}
//
//	var item v1.KeywordItem
//	err = gconv.Scan(sg, &item)
//	if err != nil {
//		return
//	}
//	res = &v1.KeywordOneRes{
//		KeywordItem: item,
//	}
//	return
//}
//
//func (s *sKeyword) GroupSiteAllList(ctx context.Context, req *v1.KeywordGroupSiteAllListReq) (res *v1.KeywordGroupSiteAllListRes, err error) {
//	list, err := s.List(ctx, &v1.KeywordListReq{
//		Belong:   req.Belong,
//		BelongId: req.BelongId,
//	})
//	if err != nil {
//		return
//	}
//	var ids string
//	for _, item := range list.List {
//		ids += gconv.String(item.SelfId) + ","
//	}
//	if len(ids) > 0 {
//		ids = ids[:len(ids)-1]
//	}
//	if ids == "" {
//		ids = "-1"
//	}
//
//	res = new(v1.KeywordGroupSiteAllListRes)
//	res.Current = req.Current
//	res.List = make([]v1.KeywordItem, 0)
//
//	m := dao.Keyword.Ctx(ctx)
//	if req.Name != nil {
//		m = m.WhereLike(dao.Keyword.Columns().Name, utility.WhereLike(*req.Name))
//	}
//	if req.Status != nil {
//		m = m.Where(dao.Keyword.Columns().Status, req.Status)
//	}
//
//	m = m.Where(dao.Keyword.Columns().DeleteTime, 0).
//		Where(dao.Keyword.Columns().Belong, 0)
//	total, err := m.Count()
//	if err != nil {
//		return
//	}
//	if total < 1 {
//		return
//	}
//	if len(req.OrderBy) > 0 {
//		m = m.Order(req.OrderBy)
//	} else {
//		m = m.OrderDesc(dao.Keyword.Table() + "." + dao.Keyword.Columns().Id) // 最新在前面
//	}
//
//	// 查询列表
//	if req.Current > 0 && req.PageSize > 0 {
//		m = m.Page(req.Current, req.PageSize)
//		res.List = make([]v1.KeywordItem, 0, req.PageSize)
//	} else {
//		res.List = make([]v1.KeywordItem, 0, total)
//	}
//
//	m = m.Fields(dao.Keyword.Table()+".*",
//		"CASE WHEN "+dao.Keyword.Columns().Id+" IN ("+ids+") THEN 1 ELSE 2 END AS ChooseStatus")
//	err = m.Scan(&res.List)
//	if err != nil {
//		return
//	}
//	res.Total = total
//	return
//}
//
//func (s *sKeyword) GroupSiteAdd(ctx context.Context, req *v1.KeywordGroupSiteAddReq) (res *v1.EmptyDataRes, err error) {
//	res = new(v1.EmptyDataRes)
//	var insertData []entity.Keyword
//	if _, err = basicLib.GroupSiteAdd(ctx, req.GroupSiteAddReqIn, basicLib.GroupSiteAddParams{
//		M: dao.Keyword.Ctx(ctx),
//		T: dao.Keyword.Table(),
//		B: req.Belong,
//	}, insertData); err != nil {
//		return
//	}
//	return
//}
//
//func (s *sKeyword) Options(ctx context.Context, req *v1.KeywordOptionsReq) (options []model.SelectOption, err error) {
//	options = make([]model.SelectOption, 0)
//	m := dao.Keyword.Ctx(ctx)
//	m = m.Fields("name as label,id as value").
//		Where(dao.Site.Columns().DeleteTime, 0)
//	if req.BelongId != nil {
//		m = m.Where(dao.Keyword.Columns().BelongId, req.BelongId)
//	}
//	if err = m.Scan(&options); err != nil {
//		return
//	}
//	return
//}
//
//func (s *sKeyword) ValidCount(ctx context.Context) (uint, error) {
//	count, err := dao.Keyword.Ctx(ctx).Where(dao.Keyword.Columns().Status, 1).Where(dao.Keyword.Columns().DeleteTime, 0).Count()
//	if err != nil {
//		return 0, err
//	}
//
//	return uint(count), nil
//}
