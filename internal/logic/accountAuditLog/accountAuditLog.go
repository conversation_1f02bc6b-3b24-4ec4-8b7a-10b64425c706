package accountAuditLog

import (
	"context"
	"encoding/json"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	v1 "gtcms/api/v1"

	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
	"gtcms/utility"
	"strings"
	"time"
)

type sAccountAuditLog struct {
	queue      chan *model.ChannelElemModifyLog
	quit       chan struct{}
	nodes      []*model.NodeInfo
	mapApiNode map[string]*model.NodeInfo
}

func init() {
	service.RegisterAccountAuditLog(New())
}

func New() service.IAccountAuditLog {
	ad := &sAccountAuditLog{
		queue:      make(chan *model.ChannelElemModifyLog, 1000),
		quit:       make(chan struct{}, 1),
		mapApiNode: make(map[string]*model.NodeInfo, 1),
	}
	ad.init()
	ad.cronJobInsertAdminModifyLog()
	ad.GetNodeLevelInfo()
	//rets := ad.getChildNode(405, 0)
	//fmt.Print(rets)
	//ad.getPath(4051)
	//go ad.testFillUserAttrs()
	return ad
}

func (s *sAccountAuditLog) ListModifyLog(ctx context.Context, in *v1.AccountAuditLogReq) (out *v1.AccountAuditLogRes, err error) {
	out = new(v1.AccountAuditLogRes)
	out.Current = in.Current
	cl := dao.AccountAuditLog.Columns()

	md := dao.AccountAuditLog.Ctx(ctx).Where(cl.Account, in.Account).OmitEmptyWhere().OmitNilWhere()
	if in.StartTime > 0 {
		md = md.WhereGTE(cl.CreateTime, in.StartTime)
	}
	if in.EndTime > 0 {
		md = md.WhereLTE(cl.CreateTime, in.EndTime)
	}

	if in.NodeID != nil {
		childNodeIDs := s.getChildNode(*in.NodeID, 0)
		childNodeIDs = append(childNodeIDs, *in.NodeID)
		md = md.WhereIn(cl.NodeId, childNodeIDs)
	}

	if in.Content != nil {
		v := "%" + *in.Content + "%"
		oneCon := md.Builder().WhereOrLike(cl.ModifyItem, v).WhereOrLike(cl.ValueBefore, v).WhereOrLike(cl.ValueAfter, v)
		md = md.Where(oneCon)
	}

	out.Total, err = md.Count()
	if err != nil {
		return nil, err
	}

	err = md.Page(in.Current, in.PageSize).OrderDesc(cl.Id).Scan(&out.List)
	return
}

func (s *sAccountAuditLog) GetNodeLevelInfo() (rets []*model.NodeInfo, err error) {
	rets = buildOrgTree(s.nodes, 0, 0)
	return rets, nil
}

func (s *sAccountAuditLog) getChildNode(nodeID int, callCnt int) (rets []int) {
	if callCnt > 100000 {
		return
	} else {
		callCnt++
	}
	var recs []int
	for _, v := range s.nodes {
		if v.ParentId == nodeID {
			recs = append(recs, v.Id)
			recs = append(recs, s.getChildNode(v.Id, callCnt)...)
		}
	}
	return recs
}

func buildOrgTree(items []*model.NodeInfo, parentID int, callCnt int) []*model.NodeInfo {
	var result []*model.NodeInfo
	if callCnt > 100000 {
		return result
	} else {
		callCnt++
	}

	for _, emp := range items {
		if emp.ParentId == parentID {
			children := buildOrgTree(items, emp.Id, callCnt)
			emp.Child = children
			result = append(result, emp)
		}
	}

	return result
}

func (s *sAccountAuditLog) IsNodeIdExist(ctx context.Context, id uint) (bool, error) {
	for _, v := range s.nodes {
		if v.Id == int(id) {
			return true, nil
		}
	}

	return false, nil
}

func (s *sAccountAuditLog) PushLog(ctx context.Context, input *model.AdminModifyInput) (err error) {
	defer func() {
		if err != nil {
			g.Log().Line().Warning(ctx, "PushLog err:[%s], AdminModifyInput:[%+v]", err, input)
		} else {
			g.Log().Line().Debugf(ctx, "PushLog success!, AdminModifyInput:[%+v]", input)
		}
	}()

	elem := &model.ChannelElemModifyLog{
		ModifyType:  input.ModifyType,
		ModifyItem:  input.ModifyItem,
		ValueBefore: gconv.String(input.OldRecordOrAttr),
		ValueAfter:  gconv.String(input.NewRecordOrAttr),
		UserIDs:     input.UserIDs,
		ExtendInfo:  input.ExtendInfo,
	}
	_ = s.FillLogFromContext(ctx, elem)
	_, isBothBasicType, err := s.checkValid(ctx, input)
	if err != nil {
		return err
	}

	if isBothBasicType {
		elem.DiffOld = elem.ValueBefore
		elem.DiffNew = elem.ValueAfter
	} else {
		attrField, oldDfVal, newDfVal := utility.GetDiffKeyValue(input.OldRecordOrAttr, input.NewRecordOrAttr)
		//用户没填值，使用 属性值填充
		if len(elem.ModifyItem) == 0 {
			elem.ModifyItem = attrField
		}
		elem.DiffOld = oldDfVal
		elem.DiffNew = newDfVal
	}

	elem.ModifyItem = eraseQuotationMark(elem.ModifyItem)
	elem.DiffOld = eraseQuotationMark(elem.DiffOld)
	elem.DiffNew = eraseQuotationMark(elem.DiffNew)

	s.queue <- elem
	return nil
}

func eraseQuotationMark(input string) string {
	if g.IsEmpty(input) {
		return ""
	}

	var str string
	// 去掉 "[""
	str = strings.ReplaceAll(input, "[\"", "")
	// 去掉 ""]"
	str = strings.ReplaceAll(str, "\"]", "")
	// 去掉双引号
	str = strings.ReplaceAll(str, `","`, ",")
	return str
}

func (s *sAccountAuditLog) FillLogFromContext(ctx context.Context, item *model.ChannelElemModifyLog) (err error) {
	// 1 从上下文获取ip
	item.LoginIp = service.Utility().GetClientIp(g.RequestFromCtx(ctx))

	// 2 从上下文获取 管理员信息
	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		glog.Warningf(ctx, "Get admin from context failed! ctx=[%+v]", ctx)
		return gerror.Wrapf(err, "Get Admin From context failed!")
	}
	if admin != nil {
		item.AdminId = admin.Id
		item.Account = admin.Account
	}

	nodes, apiUrl, _ := service.Utility().GetApiNode(ctx)
	if len(nodes) != 3 {
		glog.Warningf(ctx, "Get apiUrl from context failed! ctx=[%+v]", ctx)
		return nil
	}

	// 3 从上下文获取 填充 NodeId Object ModifyType
	mainNode := nodes[1]
	v, ok := s.mapApiNode[mainNode]
	if ok {
		item.NodeId = uint(v.Id)
		item.Object = v.Name
	} else {
		//如果找不到 遍历所有key  看是否包含
		for k, v := range s.mapApiNode {
			if strings.Contains(strings.ToLower(apiUrl), strings.ToLower(k)) {
				item.NodeId = uint(v.Id)
				item.Object = v.Name
				break
			}
		}
	}

	//if len(item.ModifyItem) == 0 {
	//	item.ModifyItem = item.Object
	//}

	if item.ModifyType == 0 {
		//如果业务没填，尝试从 路径底部节点填充
		// 新增可能包含的关键字
		cmdVal := strings.ToLower(nodes[2])
		for _, v := range []string{"add", "new"} {
			if strings.Contains(cmdVal, v) {
				item.ModifyType = model.AdminAdjustTypeAdd
				return nil
			}
		}

		for _, v := range []string{"edit", "update"} {
			if strings.Contains(cmdVal, v) {
				item.ModifyType = model.AdminAdjustTypeEdit
				return nil
			}
		}

		for _, v := range []string{"del", "delete", "remove"} {
			if strings.Contains(cmdVal, v) {
				item.ModifyType = model.AdminAdjustTypeDel
				return nil
			}
		}
	}

	if item.ModifyType == model.AdminAdjustTypeEdit {
		if len(item.UserIDs) > 1 {
			item.ModifyType = model.AdminAdjustTypeBatchEdit
		}
	}

	if item.ModifyType == model.AdminAdjustTypeDel {
		if len(item.UserIDs) > 1 {
			item.ModifyType = model.AdminAdjustTypeBatchDel
		}
	}
	return nil
}

func (s *sAccountAuditLog) checkValid(ctx context.Context, input *model.AdminModifyInput) (isAllMul, isAllBasicType bool, err error) {
	oldVar := gvar.New(input.OldRecordOrAttr)
	newVar := gvar.New(input.NewRecordOrAttr)
	oldIsMul := oldVar.IsMap() || oldVar.IsStruct()
	newIsMul := newVar.IsMap() || newVar.IsStruct()
	// 两个都是复合类型
	isAllMul = oldIsMul && newIsMul
	// 两个类型都是基本类型
	isAllBasicType = !oldIsMul && !newIsMul

	switch input.ModifyType {
	case model.AdminAdjustTypeAdd:
		if newVar.IsEmpty() {
			err = gerror.New("AdjustTypeAdd: NewRecordOrAttr is nil")
			return
		}
	case model.AdminAdjustTypeEdit:
		// 如果不满足 上述一种，表明传入参数有误
		if !(isAllMul || isAllBasicType) {
			err = gerror.New("AdjustTypeEdit: OldRecordOrAttr NewRecordOrAttr type not mismatch")
			return
		}

	case model.AdminAdjustTypeDel:
		if oldVar.IsEmpty() {
			err = gerror.New("AdjustTypeDel: OldRecordOrAttr is nil")
			return
		}

	case model.AdminAdjustTypeBatchEdit:
		// 如果不满足 上述一种，表明传入参数有误
		if !(isAllMul || isAllBasicType) {
			err = gerror.New("AdjustTypeBatchEdit: OldRecordOrAttr NewRecordOrAttr type not mismatch")
			return
		}

	case model.AdminAdjustTypeDownload:
	case model.AdminAdjustTypeUpload:
	case model.AdminAdjustTypeCover:
		// 如果不满足 上述一种，表明传入参数有误
		if isAllMul || isAllBasicType {
			err = gerror.New("AdjustTypeCover: OldRecordOrAttr NewRecordOrAttr type not mismatch")
			return
		}
	}

	return
}

func (s *sAccountAuditLog) cronJobInsertAdminModifyLog() {
	var batchSize int = 10
	fu := func(ctx context.Context) {
		// 如果队列满100，或者1秒超时，则执行批量读取
		var items []*model.ChannelElemModifyLog
		for {
			select {
			case item, ok := <-s.queue:
				if !ok {
					s.batchInsert(items)
					return
				}
				items = append(items, item)
				if len(items) == batchSize {
					s.batchInsert(items)
					//清空数组
					items = []*model.ChannelElemModifyLog{}
					continue
				}
			//case <-time.After(1 * time.Second):
			//	if len(items) > 0 {
			//		s.batchInsert(items)
			//		//清空数组
			//		items = []*model.ChannelElemModifyLog{}
			//	}
			case <-s.quit:
				return
			default:
				if len(items) > 0 {
					s.batchInsert(items)
					//清空数组
					items = []*model.ChannelElemModifyLog{}
				} else {
					select {
					case item, ok := <-s.queue:
						if !ok {
							return
						}
						items = append(items, item)
					}
				}
			}
		}
	}

	gutil.Go(context.Background(), fu, nil)
}

func (s *sAccountAuditLog) batchInsert(items []*model.ChannelElemModifyLog) {
	if len(items) == 0 {
		return
	}

	records := g.Slice{}
	ctx := context.Background()
	cl := dao.AccountAuditLog.Columns()
	maxIdRec, err := dao.AccountAuditLog.Ctx(ctx).Fields(cl.Id).OrderDesc(cl.Id).Limit(1).One()
	if err != nil {
		return
	}
	var maxId uint = 0
	if maxIdRec != nil {
		maxId = gconv.Uint(maxIdRec[cl.Id])
	}

	for _, v := range items {
		maxId++
		ext, _ := json.Marshal(v.ExtendInfo)
		rec := &entity.AccountAuditLog{
			Id:          int(maxId),
			AdminId:     v.AdminId,
			Account:     v.Account,
			LoginIp:     v.LoginIp,
			NodeId:      int(v.NodeId),
			Path:        s.getPath(int(v.NodeId)),
			Object:      v.Object,
			ModifyType:  int(v.ModifyType),
			ModifyItem:  v.ModifyItem,
			ValueBefore: v.ValueBefore,
			ValueAfter:  v.ValueAfter,
			DiffOld:     v.DiffOld,
			DiffNew:     v.DiffNew,
			ExtendInfo:  string(ext),
			CreateTime:  time.Now().UnixMilli(),
		}
		//_ = gconv.Struct(v, rec)
		records = append(records, rec)
	}

	ret, err := dao.AccountAuditLog.Ctx(ctx).Insert(records)
	if err != nil {
		glog.Warning(ctx, "Batch insert failed! err:%s, records = %+v", err, records)
		return
	}
	row, err := ret.RowsAffected()
	glog.Infof(ctx, "Batch insert! RowsAffected:%d, Total:%d", row, len(items))
}

func (s *sAccountAuditLog) testFillUserAttrs() {
	time.Sleep(5 * time.Second)
	for i := 0; i < 300; i++ {
		itm := &model.AdminModifyInput{
			//NodeId:          uint(grand.Intn(100)),
			//Object:          fmt.Sprintf("Object%d", i+1),
			ModifyType:      1,
			ModifyItem:      "标签",
			OldRecordOrAttr: "1",
			NewRecordOrAttr: "2",
			ExtendInfo:      "{\"KEY\": \"VALUE\"}",
		}
		//if i == 7 {
		//	time.Sleep(1200 * time.Millisecond)
		//}
		s.PushLog(context.Background(), itm)
	}
}

func (s *sAccountAuditLog) init() {
	cl := dao.AccountNodeConfig.Columns()
	ctx := context.Background()
	_ = dao.AccountNodeConfig.Ctx(ctx).Where(cl.DeleteTime, 0).Scan(&s.nodes)
	if len(s.nodes) == 0 {
		glog.Fatal(ctx, "Load AdminNodeConfig failed! Please check config")
	}

	for i, v := range s.nodes {
		if len(v.ApiNode) > 0 {
			s.mapApiNode[v.ApiNode] = s.nodes[i]
		}
	}
}

func (s *sAccountAuditLog) getPath(nodeId int) (path string) {
	var parentId int
	var paths []string
	for _, v := range s.nodes {
		if v.Id == nodeId {
			parentId = v.ParentId
			paths = append(paths, v.Name)
			break
		}
	}

	var i int = 0
	for parentId != 0 {
		i++
		if i == 100000 {
			break
		}
		for _, v := range s.nodes {
			if v.Id == parentId {
				parentId = v.ParentId
				paths = append(paths, v.Name)
				break
			}
		}
	}

	for i := len(paths) - 1; i >= 0; i-- {
		path += paths[i]
		if i != 0 {
			path += "/"
		}
	}

	return
}
