package dbCache

import (
	"context"
	"encoding/json"
	model "gtcms/internal/model/admin"
	"gtcms/internal/service"
	"sync"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/frame/g"
)

var ctx = context.Background()

type (
	sDbCache struct {
		mu       sync.Mutex
		updaters map[string]model.MemoryUpdater
	}
)

func init() {
	service.RegisterDbCache(New())
}

func New() service.IDbCache {
	s := &sDbCache{
		updaters: make(map[string]model.MemoryUpdater),
	}
	return s
}

func (s *sDbCache) Register(classID string, u model.MemoryUpdater) {
	s.mu.Lock()
	defer s.mu.Unlock()
	if s.updaters == nil {
		s.updaters = make(map[string]model.MemoryUpdater)
	}
	s.updaters[classID] = u
}

func (s *sDbCache) DeleteDbCache(ctx context.Context, name string) (*gvar.Var, error) {
	return g.DB().GetCache().Remove(ctx, "SelectCache:"+name)
	//return g.Redis().Do(ctx, "DEL", "SelectCache:"+name)
}

func (s *sDbCache) DeleteDbCacheGoFunc(ctx context.Context, name string) {
	go func() {
		g.DB().GetCache().Remove(ctx, "SelectCache:"+name)
		//g.Redis().Do(ctx, "DEL", "SelectCache:"+name)
	}()
	return
}

func (s *sDbCache) PublishUpdate(ctx context.Context, params model.MemoryUpdateMessage) {
	jsonData, err := json.Marshal(params)
	if err != nil {
		g.Log().Error(ctx, "Failed to marshal data:", err)
		return
	}

	_, err = g.Redis().Publish(ctx, "update_data", jsonData)
	if err != nil {
		g.Log().Error(ctx, "Failed to publish update:", err)
	} else {
		g.Log().Info(ctx, "Data update published for class ID:", params.ClassId, "key:", params.Key)
	}
}
