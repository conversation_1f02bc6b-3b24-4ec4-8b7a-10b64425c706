package event

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/util/gconv"

	dao "gtcms/internal/dao/admin"
	do "gtcms/internal/model/do/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

type (
	sEvent struct{}
)

func init() {
	service.RegisterEvent(New())
}

func New() service.IEvent {
	return &sEvent{}
}

// 验证码获取事件
func (s *sEvent) OnGetCaptcha(ctx context.Context) {
	fu := func(ctx context.Context) {
		check, _ := g.Redis().Get(ctx, "checkCaptchaCounter")
		if check.Int() == 0 {
			return
		}

		ip := service.Utility().GetClientIp(g.RequestFromCtx(ctx))
		if g.<PERSON>(ip) {
			return
		}
		counter, _ := g.<PERSON>is().Do(ctx, "ZINCRBY", "captchaCounter", 1, ip)
		if counter.Int() > 3000 {
			addBlack, _ := g.Redis().Get(ctx, "addBlack")
			if addBlack.Int() == 1 {
				g.Redis().Do(ctx, "ZADD", "ipBlackList", time.Now().Unix()+3600*24*30, ip)
			}
		}
	}
	g.Go(ctx, fu, nil)
}

// 登录失败事件
func (s *sEvent) OnLoginFail(ctx context.Context) {
	fu := func(ctx context.Context) {
		check, _ := g.Redis().Get(ctx, "checkLoginFailCounter")
		if check.Int() == 0 {
			return
		}
		ip := service.Utility().GetClientIp(g.RequestFromCtx(ctx))
		if g.IsEmpty(ip) {
			return
		}
		counter, _ := g.Redis().Do(ctx, "ZINCRBY", "loginFailCounter", 1, ip)
		if counter.Int() > 2000 {
			addBlack, _ := g.Redis().Get(ctx, "addBlack")
			if addBlack.Int() == 1 {
				g.Redis().Do(ctx, "ZADD", "ipBlackList", time.Now().Unix()+3600*24*30, ip)
			}
		}
	}

	g.Go(ctx, fu, nil)
}

// 登录成功事件
func (s *sEvent) OnLogin(ctx context.Context, account string, status int) {
	s.insertLoginLog(ctx, 1, account, status)
}

// 退出事件
func (s *sEvent) OnLogout(ctx context.Context, account string, status int) {
	s.insertLoginLog(ctx, 2, account, status)

}

// OnModifyPassword 修改密码事件
func (s *sEvent) OnModifyPassword(ctx context.Context, account string, status int) {
	s.insertLoginLog(ctx, 3, account, status)
}

func (s *sEvent) insertLoginLog(ctx context.Context, operType int, account string, status int) {
	var in *entity.Account
	var err error
	in, err = service.Account().GetByAccountName(ctx, account)
	if err != nil {
		return
	}

	val := do.AccountLoginLog{
		//AccountId:   in.Id,
		//AccountName: in.Account,
		SigninTime: time.Now().UnixMilli(),
		Ip:         service.Utility().GetClientIp(g.RequestFromCtx(ctx)),
		IpRegion:   nil,
		OperType:   operType,
		Status:     status,
		DeviceId:   nil,
		DeviceType: nil,
		Creater:    nil,
	}

	if in != nil {
		val.AccountId = in.Id
		val.AccountName = in.Account
	} else {
		val.AccountId = -1
		val.AccountName = account
	}

	if val.IpRegion == nil {
		val.IpRegion = service.Utility().GetIPCity(ctx, gconv.String(val.Ip))
	}

	// 写入登录日志
	_, err = dao.AccountLoginLog.Ctx(ctx).Data(val).Insert()
	if err != nil {
		g.Log().Line().Warningf(ctx, "AccountLoginLog insert err:%s ", err)
	}
}

func (s *sEvent) PublishConfigUpdate(ctx context.Context, channelName string) error {
	ret, err := g.Redis().Publish(ctx, channelName, fmt.Sprintf("Channel:%s modify, now:%s", channelName, time.Now()))
	if err != nil {
		g.Log().Line().Warningf(ctx, "%s Publish failed! err :%v", channelName, err)
		return err
	}
	g.Log().Line().Infof(ctx, "%s Publish success! result:%d! ", channelName, ret)
	return nil
}
