package videoCategory

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"

	dao "gtcms/internal/dao/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	"gtcms/internal/service"
	"time"
)

type sVideoCategory struct{}

func init() {
	service.RegisterVideoCategory(New())
}

func New() *sVideoCategory {
	return &sVideoCategory{}
}

// 新增
func (s *sVideoCategory) Add(ctx context.Context, req *v1.VideoCategoryAddReq) error {
	// 校验参数
	if 0 >= len(req.NameArr) {
		return gerror.New("分类名称不能为空")
	}
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	// 判断是否重复
	for _, v := range req.NameArr {
		if "" == v.CategoryName {
			return gerror.New("分类名称不能为空")
		}
		if consts.Zero > v.LanguageType || consts.Two < v.LanguageType {
			return gerror.New("语言类型错误")
		}
		// 判断是否重复
		total, err := dao.VideoCategoryLanguages.Ctx(ctx).
			Where(dao.VideoCategoryLanguages.Columns().Name, v.CategoryName).
			Where(dao.VideoCategoryLanguages.Columns().DeleteTime, consts.Zero).
			Count()
		if err != nil {
			return err
		}
		if total > 0 {
			return gerror.New("分类名：" + v.CategoryName + "已存在，不能重复添加")
		}
	}
	// 添加
	err := dao.VideoCategories.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 写入VideoCategories表
		var err1 error
		categoryData := entity.VideoCategories{
			Remark:     req.Remark,
			CreateTime: currentTime,
			UpdateTime: currentTime,
		}
		id, err1 := dao.VideoCategories.Ctx(ctx).Data(categoryData).InsertAndGetId()
		if err1 != nil {
			return err1
		}
		// 组装数据
		categoryLanguageData := g.List{}
		for _, v := range req.NameArr {
			categoryLanguageData = append(categoryLanguageData, g.Map{
				dao.VideoCategoryLanguages.Columns().CategoryId: gconv.Uint(id),
				dao.VideoCategoryLanguages.Columns().Name:       v.CategoryName,
				dao.VideoCategoryLanguages.Columns().LanguageId: gconv.Uint(v.LanguageType - 1), // 入库特殊处理，0特殊处理
				dao.VideoCategoryLanguages.Columns().CreateTime: currentTime,
				dao.VideoCategoryLanguages.Columns().UpdateTime: currentTime,
			})
		}
		// 批量插入VideoCategoriesLanguage表
		_, err1 = dao.VideoCategoryLanguages.Ctx(ctx).Data(categoryLanguageData).Insert()
		return err1
	})
	return err
}

// 修改
func (s *sVideoCategory) Edit(ctx context.Context, req *v1.VideoCategoryEditReq) error {
	// 校验参数
	if 0 >= len(req.NameArr) {
		return gerror.New("分类名称不能为空")
	}
	if 0 >= req.Id {
		return gerror.New("id不能为空")
	}
	currentTime := gconv.Uint64(time.Now().UnixMilli())
	// 判断是否重复
	for _, v := range req.NameArr {
		if "" == v.CategoryName {
			return gerror.New("分类名称不能为空")
		}
		if consts.Zero > v.LanguageType || consts.Two < v.LanguageType {
			return gerror.New("语言类型错误")
		}
		// 判断是否重复
		total, err := dao.VideoCategoryLanguages.Ctx(ctx).
			WhereNot(dao.VideoCategoryLanguages.Columns().CategoryId, req.Id).
			Where(dao.VideoCategoryLanguages.Columns().Name, v.CategoryName).
			Where(dao.VideoCategoryLanguages.Columns().DeleteTime, consts.Zero).
			Count()
		if err != nil {
			return err
		}
		if total > 0 {
			return gerror.New("分类名：" + v.CategoryName + "已存在，不能重复添加")
		}
	}
	// 添加
	err := dao.VideoCategories.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 写入VideoCategories表
		var err1 error
		categoryData := g.Map{
			dao.VideoCategories.Columns().Remark:     req.Remark,
			dao.VideoCategories.Columns().UpdateTime: currentTime,
		}
		_, err1 = dao.VideoCategories.Ctx(ctx).Where(dao.VideoCategories.Columns().Id, req.Id).Data(categoryData).Update()
		if err1 != nil {
			return err1
		}
		// 先把原来的数据删除
		_, err1 = dao.VideoCategoryLanguages.Ctx(ctx).Where(dao.VideoCategoryLanguages.Columns().CategoryId, req.Id).Delete()
		if err1 != nil {
			return err1
		}
		// 组装数据
		categoryLanguageData := g.List{}
		for _, v := range req.NameArr {
			categoryLanguageData = append(categoryLanguageData, g.Map{
				dao.VideoCategoryLanguages.Columns().CategoryId: gconv.Uint(req.Id),
				dao.VideoCategoryLanguages.Columns().Name:       v.CategoryName,
				dao.VideoCategoryLanguages.Columns().LanguageId: gconv.Uint(v.LanguageType - 1), // 入库特殊处理，0特殊处理
				dao.VideoCategoryLanguages.Columns().CreateTime: currentTime,
				dao.VideoCategoryLanguages.Columns().UpdateTime: currentTime,
			})
		}
		// 批量插入VideoCategoriesLanguage表
		_, err1 = dao.VideoCategoryLanguages.Ctx(ctx).Data(categoryLanguageData).Insert()
		return err1
	})
	return err
}

// 详情
func (s *sVideoCategory) Info(ctx context.Context, req *v1.VideoCategoryInfoReq) (out *v1.VideoCategoryInfoRes, err error) {
	out = new(v1.VideoCategoryInfoRes)
	// 参数校验
	if 0 >= req.Id {
		out.NameArr = []v1.VideoCategoryNameArrItem{}
		return out, gerror.New("参数错误")
	}
	var categoryData entity.VideoCategories
	err = dao.VideoCategories.Ctx(ctx).Where(dao.VideoCategories.Columns().Id, req.Id).Scan(&categoryData)
	if err != nil {
		return out, err
	}
	// 获取分类语言列表
	var categoryLanguageList []entity.VideoCategoryLanguages
	err = dao.VideoCategoryLanguages.Ctx(ctx).
		Where(dao.VideoCategoryLanguages.Columns().CategoryId, req.Id).
		Scan(&categoryLanguageList)
	if err != nil {
		return out, err
	}
	var nameArr = make([]v1.VideoCategoryNameArrItem, 0)
	for _, categoryLanguage := range categoryLanguageList {
		nameArr = append(nameArr, v1.VideoCategoryNameArrItem{
			LanguageType: gconv.Int(categoryLanguage.LanguageId + 1), // 0特殊处理
			CategoryName: categoryLanguage.Name,
		})
	}
	out.Id = categoryData.Id
	out.Remark = categoryData.Remark
	out.NameArr = nameArr
	return out, nil
}

// 列表
func (s *sVideoCategory) List(ctx context.Context, req *v1.VideoCategoryListReq) (out *v1.VideoCategoryListRes, err error) {
	out = new(v1.VideoCategoryListRes)
	// 获取语言
	currentLang := ctx.Value(consts.LanguageId)
	orm := dao.VideoCategories.Ctx(ctx).Where(dao.VideoCategories.Columns().DeleteTime, consts.Zero)
	// 分类名称模糊查询
	if "" != req.CategoryName {
		type categoryLanguage struct {
			CategoryId int `json:"category_id"`
		}
		var categoryLanguageList []*categoryLanguage
		err = dao.VideoCategoryLanguages.Ctx(ctx).
			WhereLike(dao.VideoCategoryLanguages.Columns().Name, "%"+req.CategoryName+"%").
			Where(dao.VideoCategoryLanguages.Columns().DeleteTime, consts.Zero).
			Scan(&categoryLanguageList)
		if err != nil {
			return
		}
		categoryIds := gutil.ListItemValuesUnique(categoryLanguageList, "CategoryId")
		if len(categoryIds) > 0 {
			orm = orm.Where(dao.NewsCategory.Columns().Id, categoryIds)
		}
	}
	// 获取总数
	out.Total, err = orm.Count()
	if err != nil {
		out.List = []v1.VideoCategoryListItem{}
		return out, err
	}
	var categoryList []*entity.VideoCategories
	// 排序
	orm = orm.OrderDesc(dao.VideoCategories.Columns().CreateTime)
	orm = orm.Page(req.Current, req.PageSize)
	err = orm.Scan(&categoryList)
	if err != nil {
		out.List = []v1.VideoCategoryListItem{}
		return out, err
	}
	// 提取分类id
	categoryIds := gutil.ListItemValuesUnique(categoryList, "Id")
	// 获取支持的语言类型
	type categoryLanguage struct {
		CategoryId int    `json:"category_id"`
		LanguageId int    `json:"language_id"`
		Name       string `json:"name"`
	}
	var categoryLanguageList []*categoryLanguage
	err = dao.VideoCategoryLanguages.Ctx(ctx).
		WhereIn(dao.VideoCategoryLanguages.Columns().CategoryId, categoryIds).
		Scan(&categoryLanguageList)
	if err != nil {
		out.List = []v1.VideoCategoryListItem{}
		return
	}
	categoryLanguageMap := make(map[int]map[int]string)
	for _, v := range categoryLanguageList {
		if _, ok := categoryLanguageMap[v.CategoryId]; !ok {
			categoryLanguageMap[v.CategoryId] = make(map[int]string)
		}
		categoryLanguageMap[v.CategoryId][v.LanguageId] = v.Name
	}
	// 组装返回值
	for _, v := range categoryList {
		one := v1.VideoCategoryListItem{
			Id:           v.Id,
			CategoryName: "",
			LanguageArr:  nil,
			VideoNum:     v.VideoCount,
		}
		// 根据当前的语言获取分类名称
		one.CategoryName = categoryLanguageMap[gconv.Int(v.Id)][gconv.Int(currentLang)]
		// 支持的语言
		_, ok := categoryLanguageMap[gconv.Int(v.Id)][consts.Zero]
		var IsZh, IsEn, IsId int
		if ok {
			IsZh = consts.One
		}
		_, ok = categoryLanguageMap[gconv.Int(v.Id)][consts.One]
		if ok {
			IsEn = consts.One
		}
		_, ok = categoryLanguageMap[gconv.Int(v.Id)][consts.Two]
		if ok {
			IsId = consts.One
		}
		// 支持的语言
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.One,
			LanguageTypeText: "ZH",
			IsSupport:        IsZh,
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Two,
			LanguageTypeText: "EN",
			IsSupport:        IsEn,
		})
		one.LanguageArr = append(one.LanguageArr, v1.LanguageArrItem{
			LanguageType:     consts.Three,
			LanguageTypeText: "ID",
			IsSupport:        IsId,
		})
		out.List = append(out.List, one)
	}
	return
}

// 删除
func (s *sVideoCategory) Delete(ctx context.Context, req *v1.VideoCategoryDeleteReq) error {
	// 参数校验
	if 1 > len(req.Ids) {
		return gerror.New("请选择要删除的分类")
	}
	// 判断是否存在视频
	for _, id := range req.Ids {
		count, err := dao.Videos.Ctx(ctx).Where(dao.Videos.Columns().CategoryId, id).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return gerror.New("该分类下有关联视频，不允许删除！")
		}
	}
	currentTime := time.Now().UnixMilli()
	err := dao.VideoCategories.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 把delete_time改成当前时间
		var err1 error
		_, err1 = dao.VideoCategories.Ctx(ctx).
			WhereIn(dao.VideoCategories.Columns().Id, req.Ids).
			Data(g.Map{
				dao.VideoCategories.Columns().DeleteTime: currentTime,
			}).
			Update()
		if err1 != nil {
			return err1
		}
		_, err1 = dao.VideoCategoryLanguages.Ctx(ctx).
			WhereIn(dao.VideoCategoryLanguages.Columns().CategoryId, req.Ids).
			Data(g.Map{
				dao.VideoCategoryLanguages.Columns().DeleteTime: currentTime,
			}).
			Update()
		return err1
	})
	return err
}
