package validRule

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/util/gvalid"
	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	"gtcms/internal/service"
	"reflect"
	"strings"
)

// Exist 存在校验规则
var Exist map[string]model.IsFieldValueExistInput

func init() {
	Exist = make(map[string]model.IsFieldValueExistInput)
	Exist["AttachmentCategory"] = model.IsFieldValueExistInput{TableName: dao.AttachmentCategory.Table(), FieldName: dao.AttachmentCategory.Columns().Id, DeleteTime: dao.AttachmentCategory.Columns().DeleteTime}
	Exist["Attachment"] = model.IsFieldValueExistInput{TableName: dao.Attachment.Table(), FieldName: dao.Attachment.Columns().Id, DeleteTime: dao.Attachment.Columns().DeleteTime}

}

func isEmpty(ctx context.Context, in gvalid.RuleFuncInput) bool {
	switch in.ValueType.Kind() {
	case reflect.Int:
		return in.Value.Int() == 0
	case reflect.Int8:
		return in.Value.Int8() == 0
	case reflect.Int16:
		return in.Value.Int16() == 0
	case reflect.Int32:
		return in.Value.Int32() == 0
	case reflect.Int64:
		return in.Value.Int64() == 0
	case reflect.Uint:
		return in.Value.Uint() == 0
	case reflect.Uint8:
		return in.Value.Uint8() == 0
	case reflect.Uint16:
		return in.Value.Uint16() == 0
	case reflect.Uint32:
		return in.Value.Uint32() == 0
	case reflect.Uint64:
		return in.Value.Uint64() == 0
	case reflect.Float32:
		return in.Value.Float32() == 0
	case reflect.Float64:
		return in.Value.Float64() == 0
	default:
		return false
	}
}

// RuleExist 校验记录是否存在(支持数组和单个)  使用格式 exist:TableName,FieldName,DeleteTime
func RuleExist(ctx context.Context, in gvalid.RuleFuncInput) error {
	if in.Value.IsNil() || in.Value.IsEmpty() || len(in.Value.Slice()) < 1 {
		return nil
	}
	if isEmpty(ctx, in) {
		return nil
	}

	rule := strings.TrimPrefix(strings.TrimPrefix(strings.TrimSpace(in.Rule), RuleExistKey), ":")
	if len(rule) < 1 {
		glog.Debug(ctx, "RuleExist:rule empty")
		return errors.New(in.Message)
	}
	keys := strings.Split(rule, ",")

	if len(keys) < 1 {
		glog.Debug(ctx, "RuleExist:Keys empty")
		return errors.New(in.Message)
	}
	mk := keys[0]
	existIn, ok := Exist[mk]
	if !ok {
		glog.Debugf(ctx, "RuleExist:Key %s can not find rule", mk)
		return errors.New(in.Message)
	}
	if len(keys) > 1 {
		fieldName := keys[1]
		if len(fieldName) > 0 {
			existIn.FieldName = fieldName
		}
	}
	if len(keys) > 2 {
		existIn.DeleteTime = keys[2]
	}
	existIn.Value = in.Value
	ok, err := service.Utility().IsFieldValueExist(ctx, existIn)
	if err != nil {
		glog.Error(ctx, err)
		return errors.New(in.Message)
	}
	if !ok {
		return errors.New(in.Message)
	}
	return nil

}
