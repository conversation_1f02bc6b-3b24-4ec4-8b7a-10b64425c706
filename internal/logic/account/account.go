package account

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/os/gtime"
	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	do "gtcms/internal/model/do/admin"
	entity "gtcms/internal/model/entity/admin"
	"gtcms/utility"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	"gtcms/internal/service"
)

type (
	sAccount struct{}
)

func init() {
	service.RegisterAccount(New())
}

var cl = dao.Account.Columns()

func delCache(id uint) gdb.CacheOption {
	return gdb.CacheOption{
		Duration: -1,
		Name:     consts.KeyAccountInfo(id),
		Force:    false,
	}
}

type ModifyLog struct {
	Account             string `json:"account"       description:"帐号"`
	Password            string `json:"password"      description:"密码"`
	NickName            string `json:"nickName"      description:"昵称"`
	Contact             string `json:"contact"       description:"联系方式"`
	Remark              string `json:"remark"        description:"备注"`
	RoleId              uint   `json:"roleId"        description:"角色id"`
	AuditPassword       string `json:"auditPassword" description:"私人密码"`
	IsOnline            int    `json:"isOnline"      description:"1:在线 2:离线"`
	IsAffect            int    `json:"isAffect"      description:"1:启用 2:停用"`
	IsRequireGoogleAuth int    `json:"isRequireGoogleAuth" dc:"是否谷歌验证码登录  1:需要 2:不用"`
}

func New() service.IAccount {
	return &sAccount{}
}

func (s *sAccount) Create(ctx context.Context, in *entity.Account) (err error) {
	var (
		isExisted bool
	)
	isExisted, err = s.IsAccountExist(ctx, in.Account, nil)
	if err != nil {
		return err
	}
	if isExisted {
		return gerror.New(g.I18n().Tf(ctx, `admin.account.occupied`, in.Account))
	}

	in.Password, err = service.Utility().HashPassword(in.Password)
	if err != nil {
		return gerror.Wrap(err, "HashPassword")
	}

	in.AuditPassword, err = service.Utility().HashPassword(in.AuditPassword)
	if err != nil {
		return gerror.Wrap(err, "Hash AuditPassword")
	}

	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		in.CreateAccount = admin.Account
		in.Creater = admin.Id
		in.CreateTime = time.Now().UnixMilli()
	}

	if in.IsRequireGoogleAuth == 1 {
		in.GoogleAuthSecret = s.GetGoogleAuthSecret()
	}
	_, err = dao.Account.Ctx(ctx).Data(in).Insert()

	if err == nil {
		in.Password = utility.MaskUserName(in.Password)
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			NewRecordOrAttr: in,
		})
	}
	return
}

func (s *sAccount) Delete(ctx context.Context, id uint) (err error) {
	var (
		isExisted bool
	)
	isExisted, err = s.IsIdExist(ctx, id)
	if err != nil {
		return err
	}
	if !isExisted {
		return gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}

	admin, _ := service.Utility().GetSelf(ctx)
	if id == admin.Id {
		return gerror.New("Cannot delete your own account!")
	}

	var oldData ModifyLog
	_ = dao.Account.Ctx(ctx).WherePri(id).Scan(&oldData)

	now := time.Now().UnixMilli()
	lb := &do.Account{
		DeleteTime: now,
	}
	if admin != nil {
		lb.UpdateAccount = admin.Account
	}

	update := g.Map{}
	update[dao.Account.Columns().UpdateTime] = gtime.Now().UnixMilli()
	update[dao.Account.Columns().DeleteTime] = gtime.Now().UnixMilli()

	_, err = dao.Account.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Data(update).Cache(delCache(id)).Update()
	if err == nil {
		delToken(ctx, id)
		oldData.Password = utility.MaskUserName(oldData.Password)
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
		})
	}
	return
}

func (s *sAccount) Edit(ctx context.Context, password, auditPassword *string, in *entity.Account) (err error) {
	var (
		isExisted bool
	)
	isExisted, err = s.IsIdExist(ctx, in.Id)
	if err != nil {
		return err
	}
	if !isExisted {
		return gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, in.Id))
	}

	var oldData ModifyLog
	_ = dao.Account.Ctx(ctx).WherePri(in.Id).Scan(&oldData)

	if oldData.Account != in.Account {
		return gerror.New(fmt.Sprintf("Account Cannot Be Modified!"))
	}

	self, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return
	}

	ac, err := service.RoleV2().Detail(ctx, self.RoleId)
	if err != nil {
		return
	}
	if consts.RoleLevelAdmin != ac.RoleLevel && oldData.RoleId != in.RoleId {
		return gerror.New(fmt.Sprintf("非管理员不允许修改角色!"))
	}

	//in.IsAffect = oldData.IsAffect
	isChangePassword := false
	if password != nil {
		in.Password, err = service.Utility().HashPassword(*password)
		if err != nil {
			return gerror.Wrap(err, "Hash Password")
		}
		isChangePassword = true
	} else {
		in.Password = oldData.Password
	}

	//if auditPassword != nil {
	//	in.AuditPassword, err = service.Utility().HashPassword(*auditPassword)
	//	if err != nil {
	//		return gerror.Wrap(err, "Hash AuditPassword")
	//	}
	//} else {
	//	in.AuditPassword = oldData.AuditPassword
	//}
	if in.IsRequireGoogleAuth == 1 {
		in.GoogleAuthSecret = s.GetGoogleAuthSecret()
	} else {
		in.GoogleAuthSecret = ""
	}

	in.UpdateAccount = self.Account
	in.UpdateTime = time.Now().UnixMilli()

	_, err = dao.Account.Ctx(ctx).WherePri(in.Id).Where(cl.DeleteTime, 0).
		Data(in).OmitEmptyData().OmitNilData().Cache(delCache(in.Id)).Update()

	// 如果编辑修改了 密码 ，就不能在访问
	if isChangePassword {
		delToken(ctx, in.Id)
	}

	_, _ = s.GetAdminFromMasterCache(ctx, in.Id)
	if err == nil {
		oldData.Password = utility.MaskUserName(oldData.Password)
		in.Password = utility.MaskUserName(in.Password)
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
			NewRecordOrAttr: in,
		})
	}

	return
}

func (s *sAccount) GetGoogleAuthSecret() (googleSecret string) {
	aesSecret, err := service.Utility().EncryptPassword("UGPTQWKSZPWEEFLQ", consts.GoogleAuthCryptoKey)
	if err != nil {
		return ""
	}
	return aesSecret
}
func (s *sAccount) Detail(ctx context.Context, id uint) (out *v1.AccountDetailRes, err error) {
	if id == 0 {
		admin, err := service.Utility().GetSelf(ctx)
		if err != nil {
			return nil, err
		}
		id = admin.Id
	}

	err = dao.Account.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Scan(&out)
	if out == nil {
		return nil, gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}

	if !g.IsEmpty(out.GoogleAuthSecret) {
		out.GoogleAuthBind = 1
		out.GoogleAuthSecret, _ = service.Utility().DecryptPassword(out.GoogleAuthSecret, consts.GoogleAuthCryptoKey)
	} else {
		out.GoogleAuthBind = 2
	}
	return
}

func (s *sAccount) GetByAccountName(ctx context.Context, account string) (in *entity.Account, err error) {
	_ = dao.Account.Ctx(ctx).Where(cl.Account, account).Where(cl.DeleteTime, 0).Scan(&in)
	return
}

func (s *sAccount) SetStatus(ctx context.Context, id uint, auditPassword string, isAffect int) (err error) {
	var (
		isExisted bool
	)
	isExisted, err = s.IsIdExist(ctx, id)
	if err != nil {
		return err
	}
	if !isExisted {
		return gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}

	admin, _ := service.Utility().GetSelf(ctx)
	//checked := service.Utility().CheckPasswordHash(auditPassword, admin.AuditPassword)
	//if !checked {
	//	service.Event().OnLoginFail(ctx)
	//	return gerror.New(g.I18n().T(ctx, `admin.accountOrPassword.invalid`))
	//}

	var oldData ModifyLog
	err = dao.Account.Ctx(ctx).WherePri(id).OmitEmptyData().OmitNilData().
		Where(cl.DeleteTime, 0).Scan(&oldData)
	if err != nil {
		return err
	}
	if oldData.IsAffect == isAffect {
		return gerror.New("User Status Already Set")
	}

	lb := &do.Account{
		IsAffect:   isAffect,
		UpdateTime: time.Now().UnixMilli(),
	}
	if admin != nil {
		lb.UpdateAccount = admin.Account
	}
	_, err = dao.Account.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Cache(delCache(id)).OmitEmptyData().OmitNilData().Update(lb)

	//var bef, aft = "停用", "开启"
	var bef, aft = "Disabled", "Enabled"
	if isAffect == 2 {
		bef, aft = aft, bef
		// 停用时立即删除 token
		delToken(ctx, id)
	}

	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			ModifyItem:      "Status",
			OldRecordOrAttr: bef,
			NewRecordOrAttr: aft,
		})
	}
	return
}

func (s *sAccount) List(ctx context.Context, in *v1.AccountListReq) (out *v1.AccountListRes, err error) {
	out = new(v1.AccountListRes)
	out.Current = in.Current
	md := dao.Account.Ctx(ctx).Where(cl.DeleteTime, 0).Handler(utility.AccountFilter)

	if in.StartTime > 0 {
		md = md.WhereGTE(cl.CreateTime, in.StartTime)
	}
	if in.EndTime > 0 {
		md = md.WhereLTE(cl.CreateTime, in.EndTime)
	}

	if in.RoleId != nil {
		md = md.Where(cl.RoleId, *in.RoleId)
	}
	if in.Account != nil {
		md = md.Where(cl.Account, *in.Account)
	}

	out.Total, err = md.Count()
	if err != nil {
		return nil, err
	}
	err = md.Page(in.Current, in.PageSize).Order("id desc").Scan(&out.List)

	mapRole, err := service.RoleV2().MapIDName(ctx)
	if err != nil {
		return
	}
	for _, v := range out.List {
		v.RoleName = mapRole[v.RoleId]
	}
	return
}

func (s *sAccount) MapIDName(ctx context.Context) (id2Name map[uint]string, err error) {
	var recs []*entity.Account
	err = dao.Account.Ctx(ctx).Where(cl.DeleteTime, 0).Scan(&recs)
	if err != nil {
		return
	}

	id2Name = make(map[uint]string, len(recs))
	for _, v := range recs {
		id2Name[v.Id] = v.Account
	}
	return
}

func (s *sAccount) IsAccountExist(ctx context.Context, account string, excludeIds []uint) (bool, error) {
	md := dao.Account.Ctx(ctx).Where(cl.Account, account).Where(cl.DeleteTime, 0)
	// 排除指定的ids
	if excludeIds != nil && len(excludeIds) > 0 {
		md = md.WhereNotIn("id", excludeIds)
	}
	count, err := md.Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *sAccount) IsIdExist(ctx context.Context, id uint) (bool, error) {
	count, err := dao.Account.Ctx(ctx).Where(cl.DeleteTime, 0).WherePri(id).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

//// ListAdminInfo 管理员信息列表
//func (s *sAccount) ListAdminInfo(ctx context.Context, in model.AdminInfoListInput) (out *model.AdminInfoListOutput, err error) {
//	out = new(model.AdminInfoListOutput)
//	m := dao.AccountInfo.Ctx(ctx)
//	if len(in.AdminIds) > 1 {
//		m = m.WhereIn(dao.AccountInfo.Columns().AdminId, in.AdminIds)
//	} else if len(in.AdminIds) == 1 {
//		m = m.Where(dao.AccountInfo.Columns().AdminId, in.AdminIds)
//	}
//	out.Total, err = m.Count()
//	if err != nil {
//		return out, err
//	}
//	if out.Total < 1 {
//		return out, nil
//	}
//	out.List = make([]entity.AccountInfo, 0, in.PageSize)
//	if in.Current > 0 && in.PageSize > 0 {
//		m = m.Page(in.Current, in.PageSize)
//	}
//	if err = m.Scan(&out.List); err != nil {
//		return out, err
//	}
//	return out, nil
//
//}

// AdminInfoMap list转map
//func (s *sAccount) AdminInfoMap(ctx context.Context, in []entity.AccountInfo) map[uint]entity.AccountInfo {
//	res := make(map[uint]entity.AccountInfo)
//	if len(in) < 1 {
//		return res
//	}
//	for _, row := range in {
//		res[uint(row.AdminId)] = row
//	}
//	return res
//}

func (s *sAccount) IsRoleUsed(ctx context.Context, roleId uint) (bool, error) {
	count, err := dao.Account.Ctx(ctx).Where(cl.RoleId, roleId).Where(cl.DeleteTime, 0).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *sAccount) ChildID(ctx context.Context, id uint) (out []uint, err error) {
	vals, err := dao.Account.Ctx(ctx).Where(cl.DeleteTime, 0).Fields(cl.Id).Where(cl.Creater, id).Array()
	if err != nil {
		return
	}
	out = gconv.Uints(vals)
	return
}

func (s *sAccount) GetAdminFromMasterCache(ctx context.Context, id uint) (admin *entity.Account, err error) {
	key := consts.KeyAccountInfo(id)
	err = dao.Account.Ctx(ctx).Master().Cache(gdb.CacheOption{
		Duration: consts.LargeCacheEx,
		Name:     key,
		Force:    false,
	}).WherePri(id).Where(cl.DeleteTime, 0).Scan(&admin)
	if err != nil {
		return
	}
	if admin == nil {
		return nil, gerror.NewCode(gcode.New(consts.NilCode, "", nil), g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}
	return
}

func (s *sAccount) UpdatePassword(ctx context.Context, id uint, newPassword string) (err error) {
	attrs := g.Map{
		cl.Password: newPassword,
	}
	_, err = dao.Account.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Data(attrs).Cache(delCache(id)).Update()
	if err == nil {
		delToken(ctx, id)
	}
	return
}

func (s *sAccount) SetOnlineStatus(ctx context.Context, id uint, status int) (err error) {
	attrs := g.Map{
		cl.IsOnline: status,
	}

	if status == consts.UserOnLine {
		attrs[cl.LastSigninTime] = time.Now().UnixMilli()
	}

	_, err = dao.Account.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Data(attrs).Cache(delCache(id)).Update()

	if status == consts.UserOffLine {
		//g.Redis().Del(ctx, consts.KeyAuthToken(id))
	}

	return
}

func delToken(ctx context.Context, id uint) {
	key := consts.KeyAuthToken(id)
	_, err := g.Redis().Del(ctx, key)
	g.Log().Line().Infof(ctx, "Delete token id= %d, key= %s, err= %s", id, key, err)
}

// 获取所有管理员账号
func (s *sAccount) GetAdminAccount(ctx context.Context) (accountId []uint) {
	all, err := dao.RolePermissionConfig.Ctx(ctx).Fields(dao.RolePermissionConfig.Columns().Id).
		Where(dao.RolePermissionConfig.Columns().RoleLevel, 1).All()
	if err != nil {
		return
	}
	var roleId []int
	for _, v := range all {
		roleId = append(roleId, gconv.Int(v["id"]))
	}
	// 自己创建的和管理员创建的
	result, err := dao.Account.Ctx(ctx).Fields(dao.Account.Columns().Id).Where(dao.Account.Columns().RoleId, roleId).All()
	if err != nil {
		return
	}
	for _, v := range result {
		accountId = append(accountId, gconv.Uint(v["id"]))
	}
	return
}

func (s *sAccount) SetGoogleAuth(ctx context.Context, id uint, auditPassword string, isAffect int) (err error) {
	var (
		isExisted bool
	)
	isExisted, err = s.IsIdExist(ctx, id)
	if err != nil {
		return err
	}
	if !isExisted {
		return gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}

	admin, _ := service.Utility().GetSelf(ctx)
	//checked := service.Utility().CheckPasswordHash(auditPassword, admin.AuditPassword)
	//if !checked {
	//	service.Event().OnLoginFail(ctx)
	//	return gerror.New(g.I18n().T(ctx, `admin.accountOrPassword.invalid`))
	//}

	var oldData ModifyLog
	err = dao.Account.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Scan(&oldData)
	if err != nil {
		return err
	}
	if oldData.IsRequireGoogleAuth == isAffect {
		return gerror.New("User GoogleAuth Already Set")
	}

	lb := &do.Account{
		IsRequireGoogleAuth: isAffect,
		UpdateTime:          time.Now().UnixMilli(),
	}
	if admin != nil {
		lb.UpdateAccount = admin.Account
	}
	_, err = dao.Account.Ctx(ctx).WherePri(id).Update(lb)
	//var bef, aft = "停用", "开启"
	var bef, aft = "Disabled", "Enabled"
	if isAffect == 2 {
		bef, aft = aft, bef
		// 停用时立即删除 token
		delToken(ctx, id)
	}
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			ModifyItem:      "GoogleAuth",
			OldRecordOrAttr: bef,
			NewRecordOrAttr: aft,
		})
	}
	return
}

func (s *sAccount) GetByAccount(ctx context.Context, account string) (out *entity.Account, err error) {
	err = dao.Account.Ctx(ctx).WherePri(cl.Account, account).Scan(&out)
	if out == nil {
		return nil, gerror.New(fmt.Sprintf("%s not exist", account))
	}
	return
}

func (s *sAccount) SetTmpl(ctx context.Context, req *v1.AccountSetTemplateReq) (res *v1.EmptyDataRes, err error) {
	res = new(v1.EmptyDataRes)
	return
	//self, err := service.Utility().GetSelf(ctx)
	//if err != nil {
	//	return
	//}
	//
	//if self.Id == 53 {
	//	update := g.Map{}
	//	if req.TemplateIds != nil {
	//		tmplJsonData, _ := json.Marshal(req.TemplateIds)
	//		update[dao.Account.Columns().TemplateIds] = tmplJsonData
	//	}
	//	if _, err = dao.Account.Ctx(ctx).Where(dao.Account.Columns().Id, req.Id).Update(update); err != nil {
	//		return
	//	}
	//}
}
