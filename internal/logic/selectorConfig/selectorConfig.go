package selectorConfig

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"gtcms/internal/consts"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	v1 "gtcms/api/v1"

	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	do "gtcms/internal/model/do/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
)

type (
	sSelectorConfig struct{}
)

func init() {
	service.RegisterSelectorConfig(New())
}

var cl = dao.SelectorConfigContent.Columns()

func New() service.ISelectorConfig {
	return &sSelectorConfig{}
}

func (s *sSelectorConfig) Create(ctx context.Context, in *entity.SelectorConfigContent) (err error) {
	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		in.CreateAccount = admin.Account
	}
	_, err = dao.SelectorConfigContent.Ctx(ctx).Cache(gdb.CacheOption{
		Duration: -1,
		Force:    true, // nil也缓存
		Name:     consts.SelectorConfigListByType + in.SelectType,
	}).Data(in).Insert()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			NewRecordOrAttr: in,
		})
	}
	return
}

func (s *sSelectorConfig) Delete(ctx context.Context, id uint) (err error) {
	var (
		isExisted bool
	)
	isExisted, err = s.IsIdExist(ctx, id)
	if err != nil {
		return err
	}
	if !isExisted {
		return gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}

	admin, _ := service.Utility().GetSelf(ctx)
	now := time.Now().UnixMilli()
	lb := &do.SelectorConfigContent{
		DeleteTime: now,
		UpdateTime: now,
	}
	if admin != nil {
		lb.UpdateAccount = admin.Account
	}

	var oldData entity.SelectorConfigContent
	_ = dao.SelectorConfigContent.Ctx(ctx).WherePri(id).Scan(&oldData)

	_, err = dao.SelectorConfigContent.Ctx(ctx).Cache(gdb.CacheOption{
		Duration: -1,
		Force:    true, // nil也缓存
		Name:     consts.SelectorConfigListByType + oldData.SelectType,
	}).WherePri(id).Delete()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
		})
	}
	s.deleteCache(ctx, oldData.SelectType)
	return
}

func (s *sSelectorConfig) Edit(ctx context.Context, id uint, in *entity.SelectorConfigContent) (err error) {
	var oldData entity.SelectorConfigContent
	_ = dao.SelectorConfigContent.Ctx(ctx).WherePri(id).Scan(&oldData)

	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		in.UpdateAccount = admin.Account
	}
	in.UpdateTime = time.Now().UnixMilli()

	_, err = dao.SelectorConfigContent.Ctx(ctx).Cache(gdb.CacheOption{
		Duration: -1,
		Force:    true, // nil也缓存
		Name:     consts.SelectorConfigListByType + in.SelectType,
	}).WherePri(id).Where(cl.DeleteTime, 0).Data(in).Update()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
			NewRecordOrAttr: in,
		})
	}
	s.deleteCache(ctx, in.SelectType)
	return
}

func (s *sSelectorConfig) Detail(ctx context.Context, id uint) (out *v1.SelectorConfigDetailRes, err error) {
	if id == 0 {
		admin, err := service.Utility().GetSelf(ctx)
		if err != nil {
			return nil, err
		}
		id = admin.Id
	}

	err = dao.SelectorConfigContent.Ctx(ctx).WherePri(id).Scan(&out)
	if out == nil {
		return nil, gerror.New(g.I18n().Tf(ctx, `admin.id.notExist`, id))
	}
	return
}

func (s *sSelectorConfig) List(ctx context.Context, in *v1.SelectorConfigListReq) (out *v1.SelectorConfigListRes, err error) {
	out = new(v1.SelectorConfigListRes)
	out.Current = in.Current
	md := dao.SelectorConfigContent.Ctx(ctx).Where(cl.DeleteTime, 0).Where(cl.SelectType, in.SelectType).
		Where(cl.IsOpen, in.IsOpen).OmitNilWhere().OmitEmptyWhere()

	if in.StartTime > 0 {
		md = md.WhereGTE(cl.CreateTime, in.StartTime)
	}
	if in.EndTime > 0 {
		md = md.WhereLTE(cl.CreateTime, in.EndTime)
	}

	if len(in.Key) > 0 {
		md = md.WhereLike(cl.Title, "%"+in.Key+"%")
	}

	out.Total, err = md.Count()
	if err != nil {
		return nil, err
	}
	err = md.Page(in.Current, in.PageSize).OrderDesc(cl.Sort).OrderAsc(cl.Id).Scan(&out.List)
	return
}

func (s *sSelectorConfig) IsIdExist(ctx context.Context, id uint) (bool, error) {
	count, err := dao.SelectorConfigContent.Ctx(ctx).WherePri(id).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *sSelectorConfig) deleteCache(ctx context.Context, typ string) {
	g.Go(ctx, func(ctx context.Context) {
		// 延迟一秒钟
		<-time.After(1 * time.Second)

		service.DbCache().PublishUpdate(ctx, model.MemoryUpdateMessage{
			ClassId: consts.SelectorConfigUpdate,
			Key:     typ,
		})
	}, nil)
}
