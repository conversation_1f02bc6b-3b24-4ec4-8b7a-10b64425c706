package roleV2

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/encoding/gjson"
	model "gtcms/internal/model/admin"

	"gtcms/utility"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	v1 "gtcms/api/v1"
	dao "gtcms/internal/dao/admin"
	do "gtcms/internal/model/do/admin"

	"gtcms/internal/service"
)

type (
	sRoleV2 struct{}
)

func init() {
	service.RegisterRoleV2(New())
}

func New() service.IRoleV2 {
	ret := &sRoleV2{}
	g.Go(context.Background(), func(ctx context.Context) {
		time.Sleep(3 * time.Second)
		ret.updateRolePerm(context.Background())
	}, nil)
	return ret
}

var cl = dao.RolePermissionConfig.Columns()

type PermId2Field struct {
	PermissionId    uint   `v:"required" json:"permissionId" dc:"权限id"`
	SensitiveFields string `v:"required" json:"sensitiveFields" dc:"脱敏字段"`
}

func (s *sRoleV2) Create(ctx context.Context, in *v1.RoleMgrAddReq) (err error) {
	var exist bool
	exist, err = s.checkNameExisted(ctx, in.Name, nil)
	if err != nil {
		return err
	}
	if exist {
		return gerror.New(g.I18n().Tf(ctx, "Role Name %s repeat!", in.Name))
	}

	err = service.PermissionV2().CheckRolePermissionCfg(ctx, in.Permissions)
	if err != nil {
		return err
	}

	var permissionIds []uint
	mapIdMask := make(v1.PermID2MaskedCfg)
	for _, v := range in.Permissions {
		permissionIds = append(permissionIds, v.PermissionId)
		if !g.IsEmpty(v.MapNameFieldPath) {
			mapIdMask[v.PermissionId] = v.MapNameFieldPath
		}
	}

	var prmJson string
	prmJson, err = gjson.EncodeString(permissionIds)
	if err != nil {
		return
	}
	var maskJson string
	if !g.IsEmpty(mapIdMask) {
		maskJson, err = gjson.EncodeString(mapIdMask)
		if err != nil {
			return
		}
	}

	item := do.RolePermissionConfig{
		Name:          in.Name,
		RoleLevel:     in.RoleLevel,
		PermissionSet: prmJson,
		MaskedFields:  maskJson,
		Remark:        in.Remark,
		OrderBy:       in.OrderBy,
		CreateTime:    time.Now().UnixMilli(),
	}

	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		item.CreateAccount = admin.Account
	}
	_, err = dao.RolePermissionConfig.Ctx(ctx).Data(item).OmitNilData().OmitEmptyData().Insert()

	if err == nil {
		newItem, _ := gjson.EncodeString(item)
		input := &model.AdminModifyInput{
			ModifyItem:      fmt.Sprintf("新增%s", in.Name),
			NewRecordOrAttr: newItem,
		}
		_ = service.AccountNodeConfig().PushLog(ctx, input)
	}
	return
}

func (s *sRoleV2) Edit(ctx context.Context, in *v1.RoleMgrEditReq) (err error) {
	var (
		exist bool
	)
	exist, err = s.checkIdExist(ctx, in.Id)
	if err != nil {
		return err
	}
	if !exist {
		return gerror.New(g.I18n().Tf(ctx, `role.id.notExist`, in.Id))
	}

	var oldRecord do.RolePermissionConfig
	dao.RolePermissionConfig.Ctx(ctx).WherePri(in.Id).Scan(&oldRecord)

	if in.Name != oldRecord.Name {
		exist, err = s.checkNameExisted(ctx, in.Name, []uint{in.Id})
		if err != nil {
			return err
		}
		if exist {
			return gerror.New(g.I18n().Tf(ctx, `permission.name.occupied`, in.Name))
		}
	}

	err = service.PermissionV2().CheckRolePermissionCfg(ctx, in.Permissions)
	if err != nil {
		return err
	}

	var permissionIds []uint
	mapIdMask := make(v1.PermID2MaskedCfg)
	for _, v := range in.Permissions {
		permissionIds = append(permissionIds, v.PermissionId)
		if !g.IsEmpty(v.MapNameFieldPath) {
			mapIdMask[v.PermissionId] = v.MapNameFieldPath
		}
	}

	var prmJson string
	prmJson, err = gjson.EncodeString(permissionIds)
	if err != nil {
		return
	}
	var maskJson string
	if !g.IsEmpty(mapIdMask) {
		maskJson, err = gjson.EncodeString(mapIdMask)
		if err != nil {
			return
		}
	}

	item := do.RolePermissionConfig{
		Id:            in.Id,
		Name:          in.Name,
		RoleLevel:     in.RoleLevel,
		PermissionSet: prmJson,
		MaskedFields:  maskJson,
		Remark:        in.Remark,
		OrderBy:       in.OrderBy,
		UpdateTime:    time.Now().UnixMilli(),
	}
	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		item.UpdateAccount = admin.Account
	}

	_, err = dao.RolePermissionConfig.Ctx(ctx).WherePri(in.Id).Data(item).OmitNilData().OmitEmptyData().Update()

	if err == nil {
		input := &model.AdminModifyInput{
			ModifyItem:      fmt.Sprintf("编辑：%s", in.Name),
			OldRecordOrAttr: oldRecord,
			NewRecordOrAttr: in,
		}
		_ = service.AccountNodeConfig().PushLog(ctx, input)
	}
	return
}

func (s *sRoleV2) Delete(ctx context.Context, in *v1.RoleMgrDeleteReq) (err error) {
	var (
		available bool
	)
	available, err = s.checkIdExist(ctx, in.Id)
	if err != nil {
		return err
	}
	if !available {
		return gerror.New(g.I18n().Tf(ctx, `role.id.notExist`, in.Id))
	}

	available, err = service.Account().IsRoleUsed(ctx, in.Id)
	if err != nil {
		return err
	}
	if available {
		return gerror.New(g.I18n().Tf(ctx, `role.id.hasBeenUsed`, in.Id))
	}

	mod := g.Map{cl.DeleteTime: time.Now().UnixMilli()}
	admin, _ := service.Utility().GetSelf(ctx)
	if admin != nil {
		mod[cl.UpdateAccount] = admin.Account
	}
	var oldRecord do.RolePermissionConfig
	dao.RolePermissionConfig.Ctx(ctx).WherePri(in.Id).Scan(&oldRecord)

	_, err = dao.RolePermissionConfig.Ctx(ctx).WherePri(in.Id).Data(mod).OmitNilData().OmitEmptyData().Update()
	if err != nil {
		return err
	}

	if err == nil {
		input := &model.AdminModifyInput{
			ModifyItem:      fmt.Sprintf("删除角色:%s", oldRecord.Name),
			OldRecordOrAttr: oldRecord,
		}
		_ = service.AccountNodeConfig().PushLog(ctx, input)
	}
	return
}

func (s *sRoleV2) List(ctx context.Context, in *v1.RoleMgrListReq) (out *v1.RoleMgrListRes, err error) {
	out = new(v1.RoleMgrListRes)
	out.Current = in.Current
	var md = dao.RolePermissionConfig.Ctx(ctx).Where(cl.DeleteTime, 0)
	if !g.IsEmpty(in.Name) {
		md = md.Where(cl.Name, in.Name)
	}

	out.Total, err = md.Count()
	if err != nil {
		return
	}

	err = md.Page(in.Current, in.PageSize).OrderDesc(cl.OrderBy).OrderAsc(cl.Id).Scan(&out.List)

	for _, v := range out.List {
		if g.IsEmpty(v.MaskedFields) {
			continue
		}

		v.MarkedFieldCfg = make(v1.PermID2MaskedCfg)
		err = gjson.Unmarshal([]byte(v.MaskedFields), &v.MarkedFieldCfg)
		if err != nil {
			return
		}
		v.MaskedFields = ""
		out.List = append(out.List)
	}
	return
}

func (s *sRoleV2) Detail(ctx context.Context, id uint) (out v1.RoleConfig, err error) {
	err = dao.RolePermissionConfig.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Scan(&out)
	return out, err
}

func (s *sRoleV2) checkNameExisted(ctx context.Context, name string, excludeIds []uint) (bool, error) {
	d := dao.RolePermissionConfig.Ctx(ctx).Where(cl.Name, name).Where(cl.DeleteTime, 0)
	// 排除指定的ids
	if len(excludeIds) > 0 {
		d = d.WhereNotIn("id", excludeIds)
	}
	count, err := d.Count()

	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *sRoleV2) AccessCheck(ctx context.Context, roleId uint, nodePath, url string) (isPassed bool, fieldPaths []string, err error) {
	// 1 url对应的权限没有配置 则完全放开可见
	isPassed, _ = service.PermissionV2().IsUrlOpen(ctx, url)
	if isPassed {
		return
	}

	// 2 url对应的权限有配置 则要校验该用户角色有没有权限
	var config v1.RoleConfig
	config, err = s.Detail(ctx, roleId)
	if err != nil {
		return
	}

	//nodePath = "/MemberManage/MemberList/UserMemberList"
	permId, err := service.PermissionV2().GetIDByNodeUrl(ctx, config.PermissionSet, nodePath, url)
	if err != nil {
		return
	}

	isPassed = permId > 0
	if !isPassed {
		return
	}

	if !g.IsEmpty(config.MaskedFields) {
		config.MarkedFieldCfg = make(v1.PermID2MaskedCfg)
		err = gjson.Unmarshal([]byte(config.MaskedFields), &config.MarkedFieldCfg)
		if err != nil {
			return
		}

		maskFields, ok := config.MarkedFieldCfg[permId]
		if !ok {
			return
		}

		fieldPaths = gmap.NewStrStrMapFrom(maskFields).Values()
	}

	return
}

func (s *sRoleV2) checkIdExist(ctx context.Context, id uint) (bool, error) {
	count, err := dao.RolePermissionConfig.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *sRoleV2) GetPermissionIds(ctx context.Context, roleId uint) (permIds []uint, err error) {
	gv, err := dao.RolePermissionConfig.Ctx(ctx).Fields("permission_id").Where("role_id=?", roleId).Array()
	if err != nil {
		return
	}
	permIds = gconv.Uints(gv)
	return
}

func (s *sRoleV2) GetPermissionIdSensitive(ctx context.Context, roleId uint) ([]uint, map[uint]string) {
	var permIds []uint
	var permId2Field map[uint]string
	var permId2Fields []PermId2Field
	permId2Field = make(map[uint]string)

	err := dao.RolePermissionConfig.Ctx(ctx).Fields("permission_id,sensitive_fields").Where("role_id=?", roleId).Scan(&permId2Fields)
	if err != nil {
		return permIds, permId2Field
	}

	for _, item := range permId2Fields {
		if item.PermissionId == 0 {
			continue
		}
		permIds = append(permIds, item.PermissionId)

		if item.SensitiveFields != " " && item.SensitiveFields != "" && item.SensitiveFields != "{}" {
			permId2Field[item.PermissionId] = item.SensitiveFields
		}
	}

	return permIds, permId2Field
}

func (s *sRoleV2) Unique(intSlice []string) []string {
	keys := make(map[string]bool)
	list := []string{}
	for _, entry := range intSlice {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

func (s *sRoleV2) Contain(ctx context.Context, subs []*v1.PermissionItem, item *v1.PermissionItem) bool {
	for i := 0; i < len(subs); i++ {
		if subs[i] == item {
			return true
		}
	}
	return false
}

func (s *sRoleV2) Options(ctx context.Context) (options []model.SelectOption, err error) {
	options = make([]model.SelectOption, 0)
	cl := dao.RolePermissionConfig.Columns()
	fields := fmt.Sprintf("%s as label, %s as value", cl.Name, cl.Id)
	err = dao.RolePermissionConfig.Ctx(ctx).Fields(fields).Where(cl.DeleteTime, 0).Handler(utility.RoleFilter).
		OrderDesc(cl.OrderBy).OrderAsc(cl.Id).Scan(&options)
	return
}

func (s *sRoleV2) updateRolePerm(ctx context.Context) {
	//var recs2 []*entity.RolePermissionConfig
	//cl2 := dao.RolePermissionConfig.Columns()
	//err := dao.RolePermissionConfig.Ctx(ctx).Where(cl2.DeleteTime, 0).Scan(&recs2)
	//for _, v := range recs2 {
	//	if len(v.PermissionSet) > 0 {
	//		return
	//	}
	//}

	//var recs []*entity.RolePermission
	//cl := dao.RolePermission.Columns()
	//err = dao.RolePermission.Ctx(ctx).Where(cl.DeleteTime, 0).Scan(&recs)
	//if err != nil {
	//	return
	//}
	//
	//roleIDPerms := make(map[uint][]uint)
	//for _, v := range recs {
	//	arr, ok := roleIDPerms[v.RoleId]
	//	if ok {
	//		arr = append(arr, v.PermissionId)
	//		roleIDPerms[v.RoleId] = arr
	//	} else {
	//		roleIDPerms[v.RoleId] = []uint{v.PermissionId}
	//	}
	//}

	//clm := dao.RolePermissionConfig.Columns()
	//for roleID, perms := range roleIDPerms {
	//	byt, _ := gjson.Marshal(perms)
	//	attrs := g.Map{clm.PermissionSet: string(byt)}
	//	_, err := dao.RolePermissionConfig.Ctx(ctx).WherePri(roleID).Data(attrs).Update()
	//	if err != nil {
	//		fmt.Println("updateRolePerm err:", err)
	//	}
	//}
	return
}

func (s *sRoleV2) MapIDName(ctx context.Context) (id2Name map[uint]string, err error) {
	type RoleIdName struct {
		Id   uint   `json:"id"            description:""`
		Name string `json:"name"          description:"角色名称"`
	}
	schemes := make([]RoleIdName, 0)

	cl := dao.RolePermissionConfig.Columns()
	err = dao.RolePermissionConfig.Ctx(ctx).Where(cl.DeleteTime, 0).Fields(cl.Id, cl.Name).WhereIn(cl.DeleteTime, 0).Scan(&schemes)
	if err != nil {
		return
	}

	id2Name = make(map[uint]string, len(schemes))
	for _, v := range schemes {
		id2Name[v.Id] = v.Name
	}
	return
}
