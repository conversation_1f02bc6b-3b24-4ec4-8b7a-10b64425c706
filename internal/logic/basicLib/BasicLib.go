package basicLib

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/util/gconv"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
	"time"
)

type GroupSiteAddParams struct {
	M *gdb.Model // 被添加表表model
	T string     // 被添加表
	B int        // 所属功能(1:组 2:站点)
}

// GroupSiteAdd 添加分组和站点下的各种子类，例如标签、友链等等
func GroupSiteAdd[T any](ctx context.Context, in model.GroupSiteAddReqIn, params GroupSiteAddParams, insertData []T) (insertList []uint, err error) {
	var deleteIds []uint
	for _, v := range in.RelList {
		if v.Mode == 1 {
			insertList = append(insertList, v.RelId)
		} else {
			deleteIds = append(deleteIds, v.RelId)
		}
	}

	if len(insertList) > 0 {
		var self *entity.Account
		if self, err = service.Utility().GetSelf(ctx); err != nil {
			return
		}
		var selfId uint
		if self != nil {
			selfId = self.Id
		}

		if err = params.M.Fields(
			params.T+".*",
			"0 as id",
			"id as self_id",
			gconv.String(in.Belong)+" as belong",
			gconv.String(in.BelongId)+" as belong_id",
			"0 as UpdateTime",
			"0 as DeleteTime",
			gconv.String(selfId)+" as creater",
			gconv.String(time.Now().UnixMilli())+" as CreateTime",
		).Where("id", insertList).Scan(&insertData); err != nil {
			return
		}

		if _, err = params.M.Data(insertData).Replace(); err != nil {
			return
		}
	}
	if len(deleteIds) > 0 {
		if _, err = params.M.Where("belong_id", in.BelongId).
			Where("self_id", deleteIds).Delete(); err != nil {
			return
		}
	}
	return
}
