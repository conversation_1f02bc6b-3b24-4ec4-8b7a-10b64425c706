package accountLoginMgr

import (
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"

	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
)

type (
	sAccountLoginMgr struct{}
)

func init() {
	service.RegisterAccountLoginMgr(New())
}

func New() service.IAccountLoginMgr {
	return &sAccountLoginMgr{}
}

func (s *sAccountLoginMgr) SignIn(ctx context.Context, in *v1.LoginInReq) (token string, err error) {
	defer func() {
		var status int = 1
		if err != nil {
			service.Event().OnLoginFail(ctx)
			status = 2
		}
		service.Event().OnLogin(ctx, in.Account, status)
	}()

	// check captcha
	if !service.Utility().VerifyCaptcha(in.Key, in.Captcha, true) {
		return token, gerror.New(g.I18n().T(ctx, `captch.verify.false`))
	}

	var admin *entity.Account
	admin, err = service.Account().GetByAccountName(ctx, in.Account)
	if err != nil {
		return token, err
	}
	if admin == nil {
		return token, gerror.New(g.I18n().T(ctx, `admin.account.invalid`))
	}

	if admin.IsAffect == 2 {
		return token, gerror.New(g.I18n().T(ctx, `admin.account.disabled`))
	}

	if admin.IsRequireGoogleAuth == 1 {
		if g.IsEmpty(admin.GoogleAuthSecret) {
			err = gerror.NewCode(gcode.CodeInvalidRequest, g.I18n().T(ctx, `请输入谷歌验证码`))
			return
		}
		var secret string
		secret, err = service.Utility().DecryptPassword(admin.GoogleAuthSecret, consts.GoogleAuthCryptoKey)
		if err != nil {
			return
		}
		if !service.GoogleAuthenticator().VerifyCode(secret, in.GoogleOtp) {
			err = gerror.NewCode(gcode.CodeInvalidRequest, g.I18n().T(ctx, `谷歌验证码验证失败`))
			return
		}
	}

	// 检查IP绑定是否开启
	loginConfig, err := service.CmsLoginConfig().DetailCache(ctx)
	if err != nil {
		g.Log().Error(ctx, err)
		err = gerror.NewCode(gcode.CodeInternalError)
		return
	}
	if loginConfig.IsIpBind == consts.IsYes {
		loginIp := service.Utility().GetClientIp(g.RequestFromCtx(ctx))
		loginRiskOut, loginRiskErr := service.CmsLoginRisk().List(ctx, &v1.CmsLoginRiskListReq{
			TabType: 1,
			Key:     loginIp,
			IsOpen:  consts.IsYes,
		})
		if loginRiskErr != nil {
			err = loginRiskErr
			return
		}
		if len(loginRiskOut.List) < 1 { // 没有找到匹配的IP
			return token, gerror.NewCode(gcode.CodeInvalidRequest, g.I18n().T(ctx, `admin.ip.login.disabled`))
		}
	}

	// check password
	checked := service.Utility().CheckPasswordHash(in.Password, admin.Password)
	if !checked {
		return token, gerror.New(g.I18n().T(ctx, `admin.password.invalid`))
	}

	if loginConfig.IsOpen == consts.IsYes { // // 检查是否后台关闭,如果关闭则只允许超级管理员登录
		// 查询当前登录的用户名是否是超管
		roleOut, roleErr := service.RoleV2().Detail(ctx, admin.RoleId)
		if roleErr != nil {
			g.Log().Error(ctx, roleErr)
			err = gerror.NewCode(gcode.CodeInternalError)
			return
		}
		if roleOut.RoleLevel != 1 { // 不是超管
			err = gerror.NewCode(gcode.CodeInvalidRequest, g.I18n().T(ctx, `admin.account.login.disabled`))
			return
		}
	}

	// 生成token
	token, err = service.Utility().GenerateJWT(admin.Id, "admin", consts.JWTSecretKey)
	if err != nil {
		return
	}
	// token写入redis
	err = g.Redis().SetEX(ctx, consts.KeyAuthToken(admin.Id), token, consts.TokenRedisEx)
	if err != nil {
		return
	}

	err = service.Account().SetOnlineStatus(ctx, admin.Id, consts.UserOnLine)
	return
}

func (s *sAccountLoginMgr) SignOut(ctx context.Context, in *v1.LoginOutReq) (err error) {
	var admin *entity.Account
	defer func() {
		var status int = 1
		if err != nil {
			status = 2
		}
		if admin != nil {
			service.Event().OnLogout(ctx, admin.Account, status)
		}
	}()

	admin, err = service.Utility().GetSelf(ctx)
	if err != nil {
		return err
	}

	err = service.Account().SetOnlineStatus(ctx, admin.Id, consts.UserOffLine)
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: "Online",
			NewRecordOrAttr: "Offline",
		})
	}
	return
}

func (s *sAccountLoginMgr) ListLoginLog(ctx context.Context, in *v1.LoginLogReq) (out *v1.LoginLogRes, err error) {
	out = &v1.LoginLogRes{}

	out.Current = in.Current
	cl := dao.AccountLoginLog.Columns()

	md := dao.AccountLoginLog.Ctx(ctx).Where(cl.AccountName, in.Account).Where(cl.Ip, in.Ip).
		Where(cl.OperType, in.LoginType).OmitEmptyWhere().OmitNilWhere()
	if in.StartTime > 0 {
		md = md.WhereGTE(cl.SigninTime, in.StartTime)
	}
	if in.EndTime > 0 {
		md = md.WhereLTE(cl.SigninTime, in.EndTime)
	}

	out.Total, err = md.Count()
	if err != nil {
		return nil, err
	}

	err = md.Page(in.Current, in.PageSize).OrderDesc(cl.Id).Scan(&out.List)

	return
}

func (s *sAccountLoginMgr) ModifyPassword(ctx context.Context, req *v1.ModifyPasswordReq) (err error) {
	var admin *entity.Account
	defer func() {
		var status int = 1
		if err != nil {
			status = 2
		}
		if admin != nil {
			service.Event().OnModifyPassword(ctx, admin.Account, status)
		}
	}()

	admin, err = service.Utility().GetSelf(ctx)
	if err != nil {
		return err
	}
	if admin.IsAffect == 2 {
		service.Event().OnLoginFail(ctx)
		return gerror.New(g.I18n().T(ctx, `admin.account.disabled`))
	}

	// check password
	checked := service.Utility().CheckPasswordHash(req.OldPassword, admin.Password)
	if !checked {
		service.Event().OnLoginFail(ctx)
		return gerror.New(g.I18n().T(ctx, `admin.accountOrPassword.invalid`))
	}

	var newPassword string
	newPassword, err = service.Utility().HashPassword(req.NewPassword)
	if err != nil {
		return gerror.Wrap(err, "HashPassword")
	}
	id := admin.Id

	err = service.Account().UpdatePassword(ctx, id, newPassword)
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			ModifyItem:      "modify password",
			OldRecordOrAttr: "******",
			NewRecordOrAttr: "******",
		})
	}
	return
}

// // 获取指定会员的登录日志（根据设备号统计）列表
// func (s *sAccountLoginMgr) ModifyPasswordReq(ctx context.Context, userId uint, whereList *v1.ListReq) (logGroups []v1.LogGroupByDevice, listRes *v1.ListRes, err error) {
//	logGroups = make([]v1.LogGroupByDevice, 0)
//	listRes = &v1.ListRes{
//		Current: whereList.Current,
//	}
//
//	models := service.TableDivision().GetAllModels(ctx, tableName, false)
//	for i := 0; i < len(models); i++ {
//		models[i].Fields(cls.DeviceId).Distinct().Where(cls.UserId, userId)
//	}
//
//	listRes.Total, err = g.Model("? as tem", g.DB().UnionAll(
//		models...,
//	)).Fields(cls.DeviceId).Distinct().Count()
//	if err != nil {
//		return
//	}
//
//	models = service.TableDivision().GetAllModels(ctx, tableName, false)
//	for i := 0; i < len(models); i++ {
//		models[i].Fields(fmt.Sprintf("%s,count(1) as n", cls.DeviceId)).Where(cls.UserId, userId).Group(cls.DeviceId)
//	}
//	// select id from (select id from a union all select id from b) as t group by id order by id desc
//	temM := g.Model("? as tem", g.DB().UnionAll(
//		models...,
//	)).Fields(fmt.Sprintf("%s,sum(n) as count", cls.DeviceId)).Group(cls.DeviceId)
//	if whereList.OrderBy != "" {
//		temM = temM.Order(whereList.OrderBy)
//	} else {
//		temM = temM.Order("count desc")
//	}
//	err = temM.Page(whereList.Current, whereList.PageSize).Scan(&logGroups)
//
//	return
// }
//
// // 获取指定会员的登录日志（根据IP统计）列表
// func (s *sAccountLoginMgr) UserSignInLogGroupByIp(ctx context.Context, userId uint, whereList *v1.ListReq) (logGroups []v1.LogGroupByIp, listRes *v1.ListRes, err error) {
//	logGroups = make([]v1.LogGroupByIp, 0)
//	listRes = &v1.ListRes{
//		Current: whereList.Current,
//	}
//
//	models := service.TableDivision().GetAllModels(ctx, tableName, false)
//	for i := 0; i < len(models); i++ {
//		models[i].Fields(cls.Ip).Distinct().Where(cls.UserId, userId)
//	}
//
//	listRes.Total, err = g.Model("? as tem", g.DB().UnionAll(
//		models...,
//	)).Fields(cls.Ip).Distinct().Count()
//	if err != nil {
//		return
//	}
//
//	models = service.TableDivision().GetAllModels(ctx, tableName, false)
//	for i := 0; i < len(models); i++ {
//		models[i].Fields(fmt.Sprintf("%s,count(1) as n", cls.Ip)).Where(cls.UserId, userId).Group(cls.Ip)
//	}
//	// select id from (select id from a union all select id from b) as t group by id order by id desc
//	temM := g.Model("? as tem", g.DB().UnionAll(
//		models...,
//	)).Fields(fmt.Sprintf("%s,sum(n) as count", cls.Ip)).Group(cls.Ip)
//	if whereList.OrderBy != "" {
//		temM = temM.Order(whereList.OrderBy)
//	} else {
//		temM = temM.Order("count desc")
//	}
//	err = temM.Page(whereList.Current, whereList.PageSize).Scan(&logGroups)
//
//	return
// }
//
// // ----
//
// func (s *sAccountLoginMgr) UserSignInLogSearch(ctx context.Context, startTime, endTime int64, pg *v1.ListReq, cond *v1.SearchCond, userIDs []uint) (signinLogs []v1.UserSignInLogList2Item, listRes *v1.ListRes, err error) {
//	signinLogs = make([]v1.UserSignInLogList2Item, 0)
//	listRes = &v1.ListRes{
//		Current: pg.Current,
//	}
//	models := service.TableDivision().GetModels(ctx, tableName, startTime, endTime, true)
//
//	for i := 0; i < len(models); i++ {
//		models[i] = models[i].WhereGTE(cls.SigninTime, startTime).WhereLTE(cls.SigninTime, endTime).OmitEmptyWhere().OmitNilWhere()
//		models[i] = models[i].WhereIn(cls.UserId, userIDs)
//		models[i] = models[i].WhereIn(cls.UserId, cond.UserIds)
//		models[i] = models[i].WhereIn(cls.Ip, cond.Ips)
//		models[i] = models[i].Where(cls.DeviceId, cond.DeviceId)
//		models[i] = models[i].Where(cls.DeviceType, cond.DeviceType)
//	}
//
//	// todo 如果数据量大，还是要优化
//	listRes.Total, err = g.DB().UnionAll(
//		models...,
//	).Count()
//	if err != nil {
//		return
//	}
//	err = g.DB().UnionAll(models...).
//		OrderDesc(cls.SigninTime).
//		Page(pg.Current, pg.PageSize).
//		Scan(&signinLogs)
//	if err != nil {
//		return
//	}
//
//	fetchUserIds := gdb.ListItemValuesUnique(signinLogs, "UserId")
//	if len(fetchUserIds) > 0 {
//		id2Phone, err := service.User().GetPhoneEmailAccountByUserIDs(ctx, gconv.Uints(fetchUserIds))
//		if err == nil {
//			for i, v := range signinLogs {
//				info, ok := id2Phone[v.UserId]
//				if ok {
//					signinLogs[i].UserAccount = info.Account
//					signinLogs[i].PhoneNum = info.PhoneNum
//					signinLogs[i].Email = info.Email
//				}
//			}
//		}
//	}
//	return
// }
//
// func (s *sAccountLoginMgr) UserSignInLogGroupByAppType(ctx context.Context, startTime int64, endTime int64) (logGroups []v1.LogGroupByAppType, err error) {
//	logGroups = make([]v1.LogGroupByAppType, 0)
//	models := service.TableDivision().GetModels(ctx, tableName, startTime, endTime, true)
//	for i := 0; i < len(models); i++ {
//		models[i] = models[i].Fields(fmt.Sprintf("%s,count(1) as n", cls.AppType)).
//			WhereGTE(cls.SigninTime, startTime).WhereLTE(cls.SigninTime, endTime).Group(cls.AppType)
//	}
//	err = g.Model("? as tem", g.DB().UnionAll(
//		models...,
//	)).Fields(fmt.Sprintf("%s,sum(n) as count", cls.AppType)).Group(cls.AppType).Scan(&logGroups)
//
//	return
// }
//
// func (s *sAccountLoginMgr) UserSignInLogGroupByVipLevel(ctx context.Context, startTime int64, endTime int64) (logGroups []v1.LogGroupByVipLevel, err error) {
//	logGroups = make([]v1.LogGroupByVipLevel, 0)
//	models := service.TableDivision().GetModels(ctx, tableName, startTime, endTime, true)
//	for i := 0; i < len(models); i++ {
//		models[i] = models[i].Fields(fmt.Sprintf("%s,count(1) as n", cls.VipLevel)).
//			WhereGTE(cls.SigninTime, startTime).WhereLTE(cls.SigninTime, endTime).Group(cls.VipLevel)
//	}
//	err = g.Model("? as tem", g.DB().UnionAll(
//		models...,
//	)).Fields(fmt.Sprintf("%s,sum(n) as count", cls.VipLevel)).Group(cls.VipLevel).Scan(&logGroups)
//	return
// }
//
// func (s *sAccountLoginMgr) ListRelation(ctx context.Context, qry *v1.RelationQuery, pageInfo *v1.ListReq) (out *v1.UserRelationListRes, err error) {
//	cl := dao.UserSigninLog.Columns()
//	lM := dao.UserSigninLog.Ctx(ctx).OmitEmptyWhere().OmitNilWhere()
//	if !g.IsEmpty(qry.Account) || !g.IsEmpty(qry.PhoneNum) || !g.IsEmpty(qry.Email) {
//		uid, err := service.User().GetUserIDsByPhoneEmailAccount(ctx, &model.PhoneEmailAccountInfo{
//			Account:  qry.Account,
//			PhoneNum: qry.PhoneNum,
//			Email:    qry.Email,
//		})
//		if err != nil {
//			return nil, err
//		}
//		if uid == 0 {
//			return nil, err
//		}
//		lM = lM.Where(cl.UserId, uid)
//	}
//
//	lM = lM.Where(cl.AppType, qry.DeviceType)
//	lM = lM.Where(cl.DeviceId, qry.DeviceId)
//	lM = lM.Where(cl.Ip, qry.LoginIp)
//	lM = lM.Where(cl.IpRegion, qry.LoginAddr)
//	if qry.StartTime > 0 {
//		lM = lM.WhereGTE(cl.SigninTime, qry.StartTime)
//	}
//	if qry.EndTime > 0 {
//		lM = lM.WhereLTE(cl.SigninTime, qry.EndTime)
//	}
//
//	total, err := lM.Count()
//	out = new(v1.UserRelationListRes)
//	out.Total = total
//	count := total
//	utility.PageLimitModify(&pageInfo.Current, &pageInfo.PageSize)
//	lM = lM.Page(pageInfo.Current, pageInfo.PageSize)
//	out.Current = pageInfo.Current
//	count = pageInfo.PageSize
//	//默认排序方式
//	lM = lM.OrderDesc(cl.Id)
//
//	recs := make([]*entity.UserSigninLog, 0, count)
//	if total < 1 {
//		return
//	}
//	err = lM.Scan(&recs)
//
//	var uids []uint
//	for _, v := range recs {
//		uids = append(uids, v.UserId)
//	}
//
//	mapIDInfo := make(map[uint]*model.PhoneEmailAccountInfo)
//	mapIDInfo, err = service.User().GetPhoneEmailAccountByUserIDs(ctx, uids)
//	if err != nil {
//		return
//	}
//
//	out.Items = make([]*model.UserRelationVo, 0, count)
//	for _, v := range recs {
//		itm := &model.UserRelationVo{
//			Id:            v.Id,
//			LoginDeviceId: v.DeviceId,
//			LoginIp:       v.Ip,
//			LoginAddr:     v.IpRegion,
//			DeviceType:    v.AppType,
//			CreateTime:    v.SigninTime,
//		}
//		info, ok := mapIDInfo[v.UserId]
//		if ok {
//			itm.Account = info.Account
//			itm.PhoneNum = info.PhoneNum
//			itm.Email = info.Email
//		}
//		out.Items = append(out.Items, itm)
//	}
//	out.TotalAccount, _ = lM.Fields(cl.UserId).Distinct().Count()
//	out.TotalIp, _ = lM.Fields(cl.Ip).Distinct().Count()
//	out.TotalDeviceId, _ = lM.Fields(cl.DeviceId).Distinct().Count()
//
//	return
// }
