package libfile

import (
	"context"
	"fmt"
	"github.com/huaweicloud/huaweicloud-sdk-go-obs/obs"
	"gtcms/internal/consts"
	"io"
	"net/url"
)

type ObsClient struct {
	client *obs.ObsClient
	config ObsS3Config
}

var _ S3Client = &ObsClient{}

const S3Obs = "obs"

// ObsS3Config aws s3的配置
type ObsS3Config struct {
	Endpoint            string `json:"endpoint"`
	AccessKeyID         string `json:"accessKeyID"`
	SecretAccessKey     string `json:"secretAccessKey"`
	BucketName          string `json:"bucketName"`
	Region              string `json:"region"`
	SessionToken        string `json:"sessionToken"`
	IsPublic            bool   `json:"isPublic"`
	GetObjectUrlExpires int    `json:"getObjectUrlExpires,omitempty"`
}

func NewObsS3Client(ctx context.Context, cfg ObsS3Config) (*ObsClient, error) {
	client, err := obs.New(cfg.AccessKeyID, cfg.SecretAccessKey, cfg.Endpoint)
	if err != nil {
		return nil, err
	}
	return &ObsClient{client: client, config: cfg}, nil
}

// GetObjectURL  获取对象的访问网址(只支持公开访问的)
func (c *ObsClient) GetObjectURL(ctx context.Context, objectName string) *url.URL {
	u := url.URL{
		Scheme: "https",
		Host:   fmt.Sprintf("%s.%s", c.config.BucketName, c.config.Endpoint),
		Path:   fmt.Sprintf("/%s", objectName),
	}
	return &u
}

func (c *ObsClient) GetPublicObjectURL(ctx context.Context, objectName string) *url.URL {
	return c.GetObjectURL(ctx, objectName)
}

// PutObject 上传对象(小文件)
func (c *ObsClient) PutObject(ctx context.Context, objectName string, reader io.Reader) error {
	input := &obs.PutObjectInput{
		PutObjectBasicInput: obs.PutObjectBasicInput{
			ObjectOperationInput: obs.ObjectOperationInput{
				Bucket: c.config.BucketName,
				Key:    objectName,
			},
		},
		Body: reader,
	}
	_, err := c.client.PutObject(input)
	return err
}

// CopyObject 复制对象（同桶或跨桶）
func (c *ObsClient) CopyObject(ctx context.Context, destObjectName, srcObjectName string) error {
	input := &obs.CopyObjectInput{
		ObjectOperationInput: obs.ObjectOperationInput{
			Bucket: c.config.BucketName,
			Key:    destObjectName,
		},
		CopySourceBucket: c.config.BucketName + "/" + srcObjectName,
	}
	_, err := c.client.CopyObject(input)
	return err
}

// RemoveObject 删除单个对象
func (c *ObsClient) RemoveObject(ctx context.Context, objectName string) error {
	input := &obs.DeleteObjectInput{
		Bucket: c.config.BucketName,
		Key:    objectName,
	}
	_, err := c.client.DeleteObject(input)
	return err
}

// RemoveObjects 批量删除对象
func (c *ObsClient) RemoveObjects(ctx context.Context, objectNames []string) error {
	objects := make([]obs.ObjectToDelete, len(objectNames))
	for i, name := range objectNames {
		objects[i] = obs.ObjectToDelete{Key: name}
	}
	input := &obs.DeleteObjectsInput{
		Bucket:  c.config.BucketName,
		Objects: objects,
	}
	_, err := c.client.DeleteObjects(input)
	return err
}

// PresignedGetObject 生成带签名的临时下载 URL
func (c *ObsClient) PresignedGetObject(ctx context.Context, objectName string) (*url.URL, error) {
	input := &obs.CreateSignedUrlInput{
		Method:  obs.HttpMethodGet,
		Bucket:  c.config.BucketName,
		Key:     objectName,
		Expires: int(c.config.GetObjectUrlExpires),
	}
	out, err := c.client.CreateSignedUrl(input)
	if err != nil {
		return nil, err
	}
	return url.Parse(out.SignedUrl)
}

func (c *ObsClient) GetName(ctx context.Context) string {
	return consts.FileTypeS3Obs
}

func (c *ObsClient) GetObject(ctx context.Context, objectName string) (io.ReadCloser, error) {
	//out, err := c.client.GetObject(ctx, c.config.BucketName, objectName, minio.GetObjectOptions{})
	//if err != nil {
	//	return nil, err
	//}
	//if out == nil {
	//	return nil, errors.New("GetObjectNil")
	//}
	//return out, err
	return nil, nil
}

func (c *ObsClient) GetObjectAttributes(ctx context.Context, objectName string) (out *ObjectAttributes, err error) {
	return nil, nil
}
