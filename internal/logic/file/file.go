package file

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/guid"
	"github.com/gogf/gf/v2/util/gutil"
	"golang.org/x/crypto/ssh"
	"gtcms/internal/consts"
	"gtcms/internal/errno"
	"gtcms/internal/logic/file/libfile"
	_ "gtcms/internal/logic/storeConfig"
	model "gtcms/internal/model/admin"
	"gtcms/internal/service"
	"io"
	"net/url"
	"os"
	"path"
	"strings"
	"sync"
	"time"

	"github.com/pkg/sftp"
)

func init() {
	service.RegisterFile(New())
}

type sFile struct {
	client        libfile.S3Client // 请通过getClient获取
	clientRwMutex sync.RWMutex
	configMutex   sync.Mutex
}

type fileConfig struct {
	Type  string              `json:"type"`
	Minio libfile.MinioConfig `json:"minio"`
	Aws   libfile.AwsS3Config `json:"aws"`
	Obs   libfile.ObsS3Config `json:"obs"`
}

func New() service.IFile {
	f := &sFile{}
	return f
}

// GetClient 获取客户端
func (s *sFile) GetClient(ctx context.Context) (libfile.S3Client, error) {
	//var err error
	//config := s.getConfig(ctx)
	//if config == nil { // 获取不到配置
	//	err = errno.T(ctx, errno.CodeFileClientConfigError)
	//	return s.client, err
	//}
	var err error
	var config *fileConfig
	v := g.Cfg().MustGet(ctx, "file")
	if v == nil {
		err = errno.T(ctx, errno.CodeFileClientConfigError)
		return nil, err
	}
	if err = v.Scan(&config); err != nil {
		return nil, err
	}
	s.clientRwMutex.RLock() // 客户端上读锁-这里没有使用defer,注意解锁
	if s.client != nil {
		s.clientRwMutex.RUnlock()
		return s.client, nil
	}
	s.clientRwMutex.RUnlock()      // 客户端解读锁
	s.clientRwMutex.Lock()         // 客户端上写锁
	defer s.clientRwMutex.Unlock() // 客户端解写锁
	var newErr error
	switch config.Type {
	case consts.FileTypeS3Minio:
		s.client, newErr = libfile.NewMinioClient(ctx, config.Minio)
		if newErr != nil {
			g.Log().Line().Error(ctx, newErr)
		}
	case consts.FileTypeS3Aws:
		s.client, newErr = libfile.NewAwsS3Client(ctx, config.Aws)
		if newErr != nil {
			g.Log().Line().Error(ctx, newErr)
		}
	case consts.FileTypeS3Obs:
		s.client, newErr = libfile.NewObsS3Client(ctx, config.Obs)
		if newErr != nil {
			g.Log().Line().Error(ctx, newErr)
		}
	}
	if s.client == nil {
		err = errno.T(ctx, errno.CodeFileClientConfigError)
	}
	return s.client, err
}

// getConfig 获取配置
func (s *sFile) getConfig(ctx context.Context) *fileConfig {
	s.configMutex.Lock()
	defer s.configMutex.Unlock()
	cacheKey := "file.config"
	fc := fileConfig{}
	data, err := gcache.Get(ctx, cacheKey) // 内存缓存
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return nil
	}
	if !data.IsNil() {
		err = data.Scan(&fc) // 这里忽略
		if err != nil {
			g.Log().Line().Error(ctx, err)
		}
	}
	if fc.Type != "" {
		return &fc
	}
	// 查询数据库
	baseConfig, baseErr := service.StoreConfig().BaseOne(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, baseErr)
		return nil
	}
	cloudConfig, cloudErr := service.StoreConfig().CloudOne(ctx, baseConfig.Type)
	if cloudErr != nil {
		g.Log().Line().Error(ctx, cloudErr)
		return nil
	}
	fc = fileConfig{
		Type: baseConfig.Type,
	}
	switch baseConfig.Type {
	case consts.FileTypeS3Aws:
		if convErr := gconv.Struct(cloudConfig.Config, &fc.Aws); convErr != nil {
			g.Log().Line().Error(ctx, convErr)
		}
	}
	err = gcache.Set(ctx, cacheKey, fc, 10*time.Second)
	if err != nil {
		g.Log().Line().Error(ctx, err)
	}
	s.clientRwMutex.Lock()         // 客户端上写锁
	defer s.clientRwMutex.Unlock() // 客户端解写锁
	s.client = nil                 // 主要是防止配置有变化,所以重新生成
	return &fc
}

// SingleUpload 上传单文件(默认上传到临时目录)
func (s *sFile) SingleUpload(ctx context.Context, in model.SingleUploadInput) (*model.SingleUploadOutput, error) {
	if in.File == nil {
		return nil, errno.T(ctx, errno.CodeFileUploadError)
	}
	if len(in.Name) < 1 && len(in.NameExt) < 1 {
		g.Log().Debugf(ctx, "sFile.SingleUpload: params name and nameExt is empty")
		return nil, errno.T(ctx, errno.CodeFileUploadError)
	}
	objectName := ""
	if in.Name != "" { // 指定了文件名,则不再放在临时目录下
		objectName = s.GetObjectName(ctx, model.GetObjectNameInput{IsTmp: false, Name: in.Name})
	} else {
		objectName = s.GetObjectName(ctx, model.GetObjectNameInput{IsTmp: false, NameExt: in.NameExt})
	}
	client, err := s.GetClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return nil, errno.T(ctx, errno.CodeFileClientError)
	}
	// 先读取 `in.File`，保证数据可复用
	data, err := io.ReadAll(in.File)
	if err != nil {
		return nil, fmt.Errorf("failed to read file data: %v", err)
	}

	// 重新创建 `io.Reader`
	tmpFile := bytes.NewReader(data)

	// 非体育图片，后台手动上传的图片需要上传到云
	if in.SportImg != 1 {
		// **上传到对象存储**
		err = client.PutObject(ctx, objectName, bytes.NewReader(data)) // 重新创建 `io.Reader`
		if err != nil {
			err = errors.Join(err, gerror.Newf("sFile.SingleUpload, objectName:%s", objectName))
			g.Log().Line().Error(ctx, err)
			return nil, errno.T(ctx, errno.CodeFileClientHandleError)
		}
	}

	gutil.Go(ctx, func(ctx context.Context) {
		_ = UploadImageViaSFTP(ctx, tmpFile, objectName, in.SportImg)
	}, nil)

	return &model.SingleUploadOutput{
		ObjectName: objectName,
	}, nil

}

// GetObjectName 获取对象名称
// 格式: pub/3hn1300cyj7r4t47ugp100t6jmkszl1y.jpg 临时目录: tmp/3hn1300cyj7r4t47ugp100t6jmkszl1y.jpg
func (s *sFile) GetObjectName(ctx context.Context, in model.GetObjectNameInput) string {
	prefix := consts.FilePrefixPublicDir // 默认公开目录
	names := make([]string, 0, 4)
	if in.IsTmp { // 如果需要放在临时目录
		prefix = consts.FilePrefixTmpDir
	}
	names = append(names, prefix)
	fileName := ""
	if len(in.Name) > 0 {
		fileName = in.Name
	} else {
		fileName = guid.S()
		if len(in.NameExt) > 0 {
			if !strings.HasPrefix(in.NameExt, ".") {
				in.NameExt = "." + in.NameExt
			}
			fileName = fileName + in.NameExt
		}
	}

	names = append(names, fileName)
	relativePath := path.Join(names...)
	return relativePath
}

// HandleUpload 处理单个已上传的图片
func (s *sFile) HandleUpload(ctx context.Context, srcObjectName string) string {
	res, err := s.HandleUploads(ctx, srcObjectName)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return ""
	}
	return res[srcObjectName]
}

// GetObjectAttributes 获取对象的信息
func (s *sFile) GetObjectAttributes(ctx context.Context, objectName string) (out *libfile.ObjectAttributes, err error) {
	client, err := s.GetClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return nil, errno.T(ctx, errno.CodeFileClientError)
	}
	out, err = client.GetObjectAttributes(ctx, objectName)
	if err != nil {
		g.Log().Error(ctx, err)
		err = gerror.NewCode(gcode.CodeInternalError)
	}
	return
}

// HandleUploads 批量处理上传的图片
func (s *sFile) HandleUploads(ctx context.Context, srcObjectNames ...string) (map[string]string, error) {
	objectNames := make(map[string]string)

	needCpObjectNames := make(map[string]string)
	tmpDir := consts.FilePrefixTmpDir + "/"
	pubDir := consts.FilePrefixPublicDir + "/"
	for _, srcObjectName := range srcObjectNames {
		if strings.HasPrefix(srcObjectName, tmpDir) { // 临时图片要复制正式目录
			destObjectName := strings.Replace(srcObjectName, tmpDir, pubDir, 1)
			objectNames[srcObjectName] = destObjectName
			needCpObjectNames[srcObjectName] = destObjectName
			continue
		}
		objectNames[srcObjectName] = srcObjectName // 其他情况保持不变, 正式目录保持不变
	}
	if len(needCpObjectNames) > 0 {
		if err := s.BatchCopy(ctx, needCpObjectNames); err != nil {
			err = errors.Join(err, gerror.Newf("sFile.HandleUploads:error"))
			return objectNames, err
		}
	}
	return objectNames, nil
}

// BatchCopy 批量复制文件
func (s *sFile) BatchCopy(ctx context.Context, objectNames map[string]string) error {
	if len(objectNames) < 1 {
		return gerror.NewCode(gcode.CodeInvalidParameter)
	}
	// 限制次数,避免复制太多
	if len(objectNames) > 100 {
		return gerror.NewCode(gcode.CodeInvalidRequest)
	}
	var (
		wg  sync.WaitGroup
		err error
	)
	wg.Add(len(objectNames))
	for srcObjectName, destObjectName := range objectNames {
		srcObjectNameTmp := srcObjectName
		destObjectNameTmp := destObjectName
		gutil.Go(ctx, func(ctx context.Context) {
			defer wg.Done()
			cpErr := s.Copy(ctx, destObjectNameTmp, srcObjectNameTmp)
			if cpErr != nil {
				err = errors.Join(err, cpErr)
			}
		}, nil)
	}
	wg.Wait()
	return err
}

// Copy 复制文件
func (s *sFile) Copy(ctx context.Context, destObjectName string, srcObjectName string) error {
	client, err := s.GetClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		err = errno.T(ctx, errno.CodeFileClientHandleError)
		return err
	}
	err = client.CopyObject(ctx, destObjectName, srcObjectName)
	if err != nil {
		err = errors.Join(err, gerror.Newf("sFile.Copy, srcObjectName:%s, destObjectName:%s", srcObjectName, destObjectName))
		g.Log().Line().Error(ctx, err)
		err = errno.T(ctx, errno.CodeFileClientHandleError)
		return err
	}
	g.Log().Line().Debugf(ctx, "sFile.Copy, srcObjectName:%s, destObjectName:%s", srcObjectName, destObjectName)
	return nil
}

// Remove 删除文件
func (s *sFile) Remove(ctx context.Context, objectNames ...string) error {
	if len(objectNames) < 1 {
		g.Log().Line().Debugf(ctx, "sFile.Remove:objectNames is empty")
		return nil
	}
	if len(objectNames) == 1 {
		return s.RemoveObject(ctx, objectNames[0])
	}
	return s.RemoveObjects(ctx, objectNames)
}

// RemoveObject 删除单个文件
func (s *sFile) RemoveObject(ctx context.Context, objectName string) error {
	client, err := s.GetClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return errno.T(ctx, errno.CodeFileClientError)
	}
	err = client.RemoveObject(ctx, objectName)
	if err != nil {
		err = errors.Join(err, gerror.Newf("sFile.RemoveObject, objectName:%s", objectName))
		g.Log().Line().Error(ctx, err)
		err = errno.T(ctx, errno.CodeFileClientHandleError)
	}
	return err
}

// RemoveObjects 删除多个文件
func (s *sFile) RemoveObjects(ctx context.Context, objectNames []string) error {
	client, err := s.GetClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return errno.T(ctx, errno.CodeFileClientError)
	}
	removeErr := client.RemoveObjects(ctx, objectNames)
	if removeErr != nil {
		g.Log().Line().Error(ctx, removeErr)
		removeErr = errno.T(ctx, errno.CodeFileClientHandleError)
	}
	return removeErr
}

// GetBackUpUrl 获取管理后理的文件链接
func (s *sFile) GetBackUpUrl(ctx context.Context, objectName string) string {
	if len(objectName) < 1 {
		return ""
	}
	if strings.HasPrefix(objectName, "http") {
		return objectName
	}
	out, err := s.GetUrl(ctx, model.GetUrlInput{ObjectName: objectName})
	if err != nil || out == nil {
		return ""
	}

	return out.Url.String()
}

// GetFrontendUrl 获取前台的文件链接
func (s *sFile) GetFrontendUrl(ctx context.Context, objectName string, domain *url.URL) string {
	if len(objectName) < 1 {
		return ""
	}
	if strings.HasPrefix(objectName, "http") {
		return objectName
	}
	out, err := s.GetUrl(ctx, model.GetUrlInput{ObjectName: objectName})
	if err != nil || out == nil {
		return ""
	}
	// 可以考虑转成接口的形式来返回
	if domain != nil && s.client.GetName(ctx) == consts.FileTypeS3Aws {
		out.Url.Scheme = domain.Scheme
		out.Url.Host = domain.Host
	}
	return out.Url.String()
}

// GetUrl 根据前缀决定是否返回参数
func (s *sFile) GetUrl(ctx context.Context, in model.GetUrlInput) (*model.GetUrlOutput, error) {
	in.ObjectName = strings.TrimPrefix(in.ObjectName, "/") // 防止前缀是/
	client, err := s.GetClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return nil, errno.T(ctx, errno.CodeFileClientError)
	}
	res := &model.GetUrlOutput{}
	publicDir, ok := s.client.(libfile.S3ClientPublicDir)
	if ok { // 如果是公开目录则直接生成网址返回, 目前是minio和aws是支持的,要注意其他云是否支持
		/*if strings.HasPrefix(in.ObjectName, consts.FilePrefixPublicDir+"/") {
			res.Url = publicDir.GetPublicObjectURL(ctx, in.ObjectName)
			return res, nil
		}*/
		res.Url = publicDir.GetPublicObjectURL(ctx, in.ObjectName)
		return res, nil
	}
	u, err := client.PresignedGetObject(context.Background(), in.ObjectName)
	if err != nil {
		err = errors.Join(err, gerror.Newf("sFile.GetUrl, objectName:%s", in.ObjectName))
		g.Log().Line().Error(ctx, err)
		return nil, errno.T(ctx, errno.CodeFileClientHandleError)
	}
	res.Url = u
	return res, nil
}

func (s *sFile) GetObject(ctx context.Context, objectName string) (io.ReadCloser, error) {
	client, err := s.GetClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return nil, errno.T(ctx, errno.CodeFileClientError)
	}
	f, err := client.GetObject(ctx, objectName)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return nil, err
	}
	return f, nil
}

// GetCanDeleteFile 返回可删除文件
/*func (s *sFile) GetCanDeleteFile(ctx context.Context, data map[string]interface{}, moduleName ...string) []string {
	res := make([]string, 0, len(data))
	if data == nil || len(data) < 1 {
		return res
	}
	if len(moduleName) < 1 {
		for name, _ := range consts.ModuleName {
			moduleName = append(moduleName, name)
		}
	}
	for _, name := range moduleName {
		modulePrefixList := s.GetModuleNamePrefix(ctx, name)
		for _, modulePrefix := range modulePrefixList {
			for _, tmpVal := range data {
				v := gvar.New(tmpVal)
				if v.IsNil() {
					continue
				}
				if v.IsStruct() || v.IsMap() { // 最多支持二级
					v2 := v.MapStrVar()
					for _, subVal := range v2 {
						if subVal.IsNil() {
							continue
						}
						if strings.HasPrefix(subVal.String(), modulePrefix) {
							res = append(res, subVal.String())
						}
					}
				} else {
					if strings.HasPrefix(v.String(), modulePrefix) {
						res = append(res, v.String())
					}
				}

			}
		}
	}
	return res
}*/

func UploadImageViaSFTP(ctx context.Context, reader io.Reader, filename string, sportImg int) error {
	// SSH 连接信息
	serverIP := "************"
	port := "12035"
	user := "root"
	password := "N62TXsumQ2fsZ75"
	remotePath := "/var/www/html/img/pub"

	switch sportImg {
	case 1: // 体育图片
		filename = gstr.Replace(filename, consts.FilePrefixPublicDir, "")
		remotePath = "/var/www/html"
	case 0: // 非体育图片
		filename = gstr.Replace(filename, consts.FilePrefixTmpDir, "")
	case 3: // pdf文件
		remotePath = "/var/www/html/img/pub/pdf/"
	}

	// SSH 配置
	config := &ssh.ClientConfig{
		User: user,
		Auth: []ssh.AuthMethod{
			ssh.Password(password),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Config: ssh.Config{
			Ciphers: []string{"aes128-ctr", "aes192-ctr", "aes256-ctr"}, // 添加兼容 cipher
		},
		Timeout: 10 * time.Second, // 避免卡死
	}

	// 连接到 SSH 服务器
	client, err := ssh.Dial("tcp", serverIP+":"+port, config)
	if err != nil {
		g.Log().Error(ctx, "failed to connect to server: %v", err)
		return fmt.Errorf("failed to connect to server: %v", err)
	}
	defer client.Close()

	// 创建 SFTP 客户端
	sftpClient, err := sftp.NewClient(client)
	if err != nil {
		g.Log().Error(ctx, "failed to create SFTP client: %v", err)
		return fmt.Errorf("failed to create SFTP client: %v", err)
	}
	defer sftpClient.Close()

	// 打开远程文件进行写入
	remoteFile, err := sftpClient.Create(remotePath + filename)
	if err != nil {
		g.Log().Error(ctx, "failed to create remote file: %v", err)
		return fmt.Errorf("failed to create remote file: %v", err)
	}
	defer remoteFile.Close()

	// **调试输出** 确保 reader 不是空的
	buf := make([]byte, 1024)
	n, err := reader.Read(buf)
	if err != nil && err != io.EOF {
		g.Log().Error(ctx, "failed to read image data: %v", err)
		return fmt.Errorf("failed to read image data: %v", err)
	}
	if n == 0 {
		g.Log().Error(ctx, "image data is empty, nothing to upload")
		return fmt.Errorf("image data is empty, nothing to upload")
	}

	// **重置 reader**，防止数据丢失
	newReader := io.MultiReader(bytes.NewReader(buf[:n]), reader)

	// **上传数据**
	written, err := io.Copy(remoteFile, newReader)
	if err != nil {
		g.Log().Error(ctx, "failed to write to remote file: %v", err)
		return fmt.Errorf("failed to write to remote file: %v", err)
	}

	// **确保数据完整**
	if written == 0 {
		g.Log().Error(ctx, "uploaded file is empty, check reader content")
		return fmt.Errorf("uploaded file is empty, check reader content")
	}

	g.Log().Info(ctx, "Image uploaded successfully: %s (%d bytes)\n", filename, written)
	return nil
}

func UploadImageViaSFTPByRsa(ctx context.Context, reader io.Reader, filename string, sportImg int) error {
	// SSH 配置
	serverIP := "************"
	port := "12035"
	user := "root"
	privateKeyPath := "/home/<USER>/.ssh/id_rsa"

	// 选择上传路径
	remotePath := "/var/www/html/img/pub"
	switch sportImg {
	case 1: // 体育图片
		filename = gstr.Replace(filename, consts.FilePrefixPublicDir, "")
		remotePath = "/var/www/html"
	case 0: // 非体育图片
		filename = gstr.Replace(filename, consts.FilePrefixTmpDir, "")
	case 3: // PDF 文件
		remotePath = "/var/www/html/img/pub/pdf/"
	}

	// 读取私钥
	key, err := os.ReadFile(privateKeyPath)
	if err != nil {
		g.Log().Error(ctx, "failed to read private key: ", err)
		return fmt.Errorf("读取私钥失败: %v", err)
	}

	signer, err := ssh.ParsePrivateKey(key)
	if err != nil {
		g.Log().Error(ctx, "failed to parse private key: ", err)
		return fmt.Errorf("解析私钥失败: %v", err)
	}

	// 构建 SSH 配置（私钥认证）
	config := &ssh.ClientConfig{
		User: user,
		Auth: []ssh.AuthMethod{
			ssh.PublicKeys(signer),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         10 * time.Second,
	}

	// 建立连接
	client, err := ssh.Dial("tcp", serverIP+":"+port, config)
	if err != nil {
		g.Log().Error(ctx, "failed to connect to server: ", err)
		return fmt.Errorf("SSH连接失败: %v", err)
	}
	defer client.Close()

	// 创建 SFTP 客户端
	sftpClient, err := sftp.NewClient(client)
	if err != nil {
		g.Log().Error(ctx, "failed to create SFTP client: ", err)
		return fmt.Errorf("创建SFTP失败: %v", err)
	}
	defer sftpClient.Close()

	// 预读取一部分数据以检查 reader 是否为空
	buf := make([]byte, 1024)
	n, err := reader.Read(buf)
	if err != nil && err != io.EOF {
		g.Log().Error(ctx, "failed to read image data: ", err)
		return fmt.Errorf("读取数据失败: %v", err)
	}
	if n == 0 {
		g.Log().Error(ctx, "image data is empty, nothing to upload")
		return fmt.Errorf("文件数据为空")
	}
	newReader := io.MultiReader(bytes.NewReader(buf[:n]), reader)

	// 创建远程文件（带路径）
	fullRemotePath := remotePath + filename
	remoteFile, err := sftpClient.Create(fullRemotePath)
	if err != nil {
		g.Log().Error(ctx, "failed to create remote file: ", err)
		return fmt.Errorf("创建远程文件失败: %v", err)
	}
	defer remoteFile.Close()

	// 传输数据
	written, err := io.Copy(remoteFile, newReader)
	if err != nil {
		g.Log().Error(ctx, "failed to write to remote file: ", err)
		return fmt.Errorf("写入远程文件失败: %v", err)
	}
	if written == 0 {
		g.Log().Error(ctx, "uploaded file is empty, check reader content")
		return fmt.Errorf("写入数据为空")
	}

	g.Log().Info(ctx, "Image uploaded successfully")
	return nil
}
