package storeConfig

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"gtcms/internal/consts"

	dao "gtcms/internal/dao/admin"
	model "gtcms/internal/model/admin"
	do "gtcms/internal/model/do/admin"
	entity "gtcms/internal/model/entity/admin"

	"gtcms/internal/service"
	"time"
)

type (
	sStoreConfig struct{}
)

func init() {
	service.RegisterStoreConfig(New())
}

func New() service.IStoreConfig {
	return &sStoreConfig{}
}

// GetTypesOption 存储类型列表
func (s *sStoreConfig) GetTypesOption(ctx context.Context) []model.SelectOption {
	res := make([]model.SelectOption, 0, 10)
	res = append(res, model.SelectOption{
		Label: g.I18n().T(ctx, fmt.Sprintf("file.type.s3.%s", consts.FileTypeS3Aws)),
		Value: consts.FileTypeS3Aws,
	})
	return res
}

// BaseOne 查询单个存储配置的基础配置
func (s *sStoreConfig) BaseOne(ctx context.Context) (out *entity.StoreBaseConfig, err error) {
	err = dao.StoreBaseConfig.Ctx(ctx).Where(dao.StoreBaseConfig.Columns().DeleteTime, 0).Limit(1).Scan(&out)
	if out == nil {
		return nil, gerror.NewCode(gcode.CodeNotFound)
	}
	return
}

// BaseEdit 修改单个存储配置的基础配置
func (s *sStoreConfig) BaseEdit(ctx context.Context, in model.StoreBaseConfigEdit) (err error) {
	var oldData *entity.StoreBaseConfig
	err = dao.StoreBaseConfig.Ctx(ctx).Where(dao.StoreBaseConfig.Columns().DeleteTime, 0).Scan(&oldData)
	if err != nil {
		return
	}
	if oldData == nil {
		err = gerror.NewCode(gcode.CodeNotFound)
		return
	}
	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return
	}
	oldUpdate := g.Map{
		dao.StoreBaseConfig.Columns().Type: oldData.Type,
	}
	update := g.Map{
		dao.StoreBaseConfig.Columns().Type:          in.Type,
		dao.StoreBaseConfig.Columns().UpdateAccount: admin.Account,
		dao.StoreBaseConfig.Columns().UpdateTime:    time.Now().UnixMilli(),
	}
	_, err = dao.StoreBaseConfig.Ctx(ctx).WherePri(oldData.Id).Data(update).Update()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			ModifyType:      model.AdminAdjustTypeEdit,
			OldRecordOrAttr: oldUpdate,
			NewRecordOrAttr: update,
		})
	}
	s.deleteCache(ctx)
	return
}

func (s *sStoreConfig) deleteCache(ctx context.Context) {
	g.Go(ctx, func(ctx context.Context) {
		// 延迟一秒钟
		<-time.After(1 * time.Second)

		service.DbCache().PublishUpdate(ctx, model.MemoryUpdateMessage{
			ClassId: consts.StoreUpdate,
			Key:     "",
		})
	}, nil)
}

// CloudOne 查看指定类型的存储配置-云配置
func (s *sStoreConfig) CloudOne(ctx context.Context, cloudType string) (out *model.StoreCloudConfig, err error) {
	err = dao.StoreCloudConfig.Ctx(ctx).Where(dao.StoreCloudConfig.Columns().Type, cloudType).Where(dao.StoreCloudConfig.Columns().DeleteTime, 0).
		OrderDesc(dao.StoreCloudConfig.Columns().Id).Limit(1).Scan(&out) // 取最新的
	if out == nil {
		err = gerror.NewCode(gcode.CodeNotFound)
	}
	return
}

// CloudOneEdit 修改存储配置-云配置
func (s *sStoreConfig) CloudOneEdit(ctx context.Context, in model.StoreCloudConfigEditInput) (err error) {
	oldData, err := s.CloudOne(ctx, in.Type)
	if err != nil {
		return
	}
	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return
	}
	update := g.Map{
		dao.StoreCloudConfig.Columns().Config:        in.Config,
		dao.StoreCloudConfig.Columns().UpdateTime:    time.Now().UnixMilli(),
		dao.StoreCloudConfig.Columns().UpdateAccount: admin.Account,
	}
	_, err = dao.StoreCloudConfig.Ctx(ctx).WherePri(oldData.Id).Data(update).Update()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			ModifyType:      model.AdminAdjustTypeEdit,
			NewRecordOrAttr: "Edit " + gconv.String(oldData.Id),
		})
	}
	s.deleteCache(ctx)
	return
}

// CloudOneAdd 添加存储配置-云配置
func (s *sStoreConfig) CloudOneAdd(ctx context.Context, in model.StoreCloudConfigAddInput) (id uint, err error) {
	cloudConfig, err := s.CloudOne(ctx, in.Type)
	if err != nil && !gerror.HasCode(err, gcode.CodeNotFound) {
		return
	}
	if cloudConfig != nil { // 已存在记录,不再新增
		err = gerror.NewCode(gcode.CodeInvalidOperation)
		return
	}
	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return
	}
	doData := &do.StoreCloudConfig{
		Type:          in.Type,
		Config:        in.Config,
		CreateTime:    time.Now().UnixMilli(),
		CreateAccount: admin.Account,
		UpdateTime:    time.Now().UnixMilli(),
		UpdateAccount: admin.Account,
		DeleteTime:    0,
	}
	insertId, insertErr := dao.StoreCloudConfig.Ctx(ctx).Data(doData).InsertAndGetId()
	if insertErr != nil {
		g.Log().Line().Error(ctx, insertErr)
		err = gerror.NewCode(gcode.CodeDbOperationError)
		return
	}
	id = uint(insertId)
	_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
		ModifyType:      model.AdminAdjustTypeAdd,
		NewRecordOrAttr: "Add " + gconv.String(insertId),
	})
	s.deleteCache(ctx)
	return
}
