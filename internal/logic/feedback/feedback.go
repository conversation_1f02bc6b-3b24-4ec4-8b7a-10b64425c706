package feedback

import (
	"context"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"

	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"

	"gtcms/internal/service"
)

type sFeedback struct{}

func init() {
	service.RegisterFeedback(New())
}
func New() *sFeedback {
	return &sFeedback{}
}

// 反馈列表
func (f *sFeedback) FeedbackList(ctx context.Context, req *v1.FeedbackListReq) (res *v1.FeedbackListRes, err error) {
	res = &v1.FeedbackListRes{}
	res.Current = req.Current
	res.Offset = req.Offset

	md := dao.Feedback.Ctx(ctx)
	if req.Keyword != "" {
		md = md.Where(dao.Feedback.Columns().Desc, "%"+req.Keyword+"%")
	}
	if req.FeedbackType != 0 {
		md = md.Where(dao.Feedback.Columns().FeedbackType, req.FeedbackType)
	}
	if req.StartTime > 0 {
		md = md.Where(dao.Feedback.Columns().FeedbackTime, ">=", req.StartTime)
	}

	if req.EndTime > 0 {
		md = md.Where(dao.Feedback.Columns().FeedbackTime, "<=", req.EndTime)
	}

	if req.UserId > 0 {
		md = md.Where(dao.Feedback.Columns().UserId, req.UserId)
	}
	var out []*entity.Feedback
	count, err := md.Count()
	if err != nil {
		return nil, err
	}
	res.Total = count

	if count == 0 {
		return &v1.FeedbackListRes{
			List: make([]*v1.FeedbackListItem, 0),
		}, nil
	}

	err = md.Page(req.Current, req.PageSize).Order("id desc").Scan(&out)
	if err != nil {
		return nil, err
	}

	if len(out) == 0 {
		return &v1.FeedbackListRes{
			List: make([]*v1.FeedbackListItem, 0),
		}, nil
	}
	err = gconv.SliceStruct(out, &res.List)
	if err != nil {
		return nil, err
	}

	return
}

// 反馈详情
func (f *sFeedback) FeedbackOne(ctx context.Context, req *v1.FeedbackOneReq) (res *v1.FeedbackOneRes, err error) {

	var feedback *entity.Feedback
	err = dao.Feedback.Ctx(ctx).Where(dao.Feedback.Columns().Id, req.Id).Scan(&feedback)
	if err != nil {
		return nil, err
	}
	if feedback == nil {
		return nil, nil
	}
	err = gconv.Struct(feedback, &res)
	if err != nil {
		return nil, err
	}
	return
}

// 删除反馈
func (f *sFeedback) FeedbackDelete(ctx context.Context, req *v1.FeedbackDeleteReq) (res *v1.FeedbackDeleteRes, err error) {
	_, err = dao.Feedback.Ctx(ctx).Where(dao.Feedback.Columns().Id, req.Id).Delete()
	if err != nil {
		return nil, err
	}
	return
}

// 处理反馈
func (f *sFeedback) FeedbackComplete(ctx context.Context, req *v1.FeedbackCompleteReq) (res *v1.FeedbackCompleteRes, err error) {

	admin, err := service.Utility().GetSelf(ctx)
	if err != nil {
		return nil, err
	}
	_, err = dao.Feedback.Ctx(ctx).Data(do.Feedback{
		FeedbackResult:  req.FeedbackResult,
		CompleteRemark:  req.CompleteRemark,
		CompleteTime:    gtime.TimestampMilli(),
		CompleteAccount: admin.Account,
		//CompleteNickName: admin.NickName,
		FeedbackStatus: consts.FeedbackStatusProcessed,
	}).Where(dao.Feedback.Columns().Id, req.Id).Update()
	if err != nil {
		return nil, err
	}

	return
}
