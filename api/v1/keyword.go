package v1

//
//import (
//	"github.com/gogf/gf/v2/frame/g"
//	model "gtcms/internal/model/admin"
//	entity "gtcms/internal/model/entity/admin"
//)
//
//type KeywordListReq struct {
//	g.Meta   `path:"/keyword/list" tags:"关键词管理" method:"post" summary:"关键词列表"`
//	Name     *string `json:"name" dc:"关键词名称"`
//	Status   *int    `json:"status" dc:"状态(1:启用 2:禁用)"`
//	Belong   int     `v:"required" json:"belong" dc:"所属功能(0:基础库 1:组 2:站点)"`
//	BelongId *uint   `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id"`
//	ListReq
//}
//
//type KeywordListRes struct {
//	ListRes
//	List []KeywordItem `json:"list" dc:"列表"`
//}
//
//type KeywordItem struct {
//	entity.Keyword
//	ChooseStatus int `json:"chooseStatus" dc:"选中状态(1:选中 2:未选中)"`
//}
//
//type KeywordAddReq struct {
//	g.Meta       `path:"/keyword/add" tags:"关键词管理" method:"post" summary:"添加关键词"`
//	Name         string `v:"required" json:"name"         dc:"名称"`
//	Url          string `v:"required" json:"url"          dc:"链接"`
//	Status       int    `json:"status"       dc:"状态(1:启用 2:禁用)"`
//	LinkProperty int    `json:"linkProperty" dc:"链接属性(1:新窗口打开 2:nofollow)"`
//	Remark       string `json:"remark"       dc:"备注"`
//	Belong       int    `v:"required" json:"belong" dc:"所属功能(0:栏目 1:组 2:站点)"`
//	BelongId     *uint  `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
//}
//
//type KeywordAddRes struct {
//	Id uint `json:"id" dc:"新添记录的id"`
//}
//
//type KeywordEditReq struct {
//	g.Meta       `path:"/keyword/edit" tags:"关键词管理" method:"post" summary:"编辑关键词"`
//	Id           uint    `v:"required" json:"id"         dc:"id"`
//	Name         *string `json:"name"         dc:"名称"`
//	Url          *string `json:"url"          dc:"链接"`
//	Status       *int    `json:"status"       dc:"状态(1:启用 2:禁用)"`
//	LinkProperty *int    `json:"linkProperty" dc:"链接属性(1:新窗口打开 2:nofollow)"`
//	Remark       *string `json:"remark"       dc:"备注"`
//	Belong       int     `v:"required" json:"belong" dc:"所属功能(0:关键词 1:分组关键词 2:站点关键词)"`
//}
//
//type KeywordDeleteReq struct {
//	g.Meta `path:"/keyword/delete" tags:"关键词管理" method:"post" summary:"删除关键词"`
//	Id     uint   `json:"id"         dc:"id"`
//	Ids    []uint `json:"ids"         dc:"ids"`
//	Belong int    `v:"required" json:"belong" dc:"所属功能(0:关键词 1:分组关键词 2:站点关键词)" d:"0"`
//}
//
//type KeywordOneReq struct {
//	g.Meta `path:"/keyword/one" method:"post" tags:"关键词管理" summary:"关键词->获取"`
//	Id     uint `v:"required" json:"id" dc:"id"`
//}
//type KeywordOneRes struct {
//	KeywordItem
//}
//
//type KeywordGroupSiteAllListReq struct {
//	g.Meta   `path:"/keyword/groupSiteAllList" tags:"关键词管理" method:"post" summary:"分组/站点关键词列表(全部)"`
//	Name     *string `json:"name" dc:"关键词名称"`
//	Status   *int    `json:"status" dc:"状态(1:启用 2:禁用)"`
//	Belong   int     `v:"required" json:"belong" dc:"所属功能(1:组 2:站点)"`
//	BelongId *uint   `v:"required" json:"belongId" dc:"所属功能id"`
//	ListReq
//}
//
//type KeywordGroupSiteAllListRes struct {
//	ListRes
//	List []KeywordItem `json:"list" dc:"列表"`
//}
//
//type KeywordGroupSiteAddReq struct {
//	g.Meta `path:"/keyword/groupSiteAdd" tags:"关键词管理" method:"post" summary:"分组/站点关键词添加"`
//	model.GroupSiteAddReqIn
//}
//
//type KeywordOptionsReq struct {
//	g.Meta   `path:"/keyword/options" method:"post" tags:"关键词管理" summary:"关键词->选项（供选择器）"`
//	Belong   int   `v:"required" json:"belong" dc:"所属功能(0:栏目 1:分组关键词 2:站点关键词)"`
//	BelongId *uint `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
//}
//
//type KeywordOptionsRes struct {
//	Options []model.SelectOption `json:"options" dc:"选择器选项数组"`
//}
