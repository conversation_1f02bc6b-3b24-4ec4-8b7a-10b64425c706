package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	model "gtcms/internal/model/admin"
)

type SiteGroupListReq struct {
	g.Meta     `path:"/siteGroup/list" tags:"分组管理" method:"post" summary:"组列表"`
	Id         *int    `json:"id" dc:""`
	Name       *string `json:"name" dc:"名称"`
	CreateTime *int64  `json:"createTime" dc:"创建时间"`
	Creater    *uint   `json:"creater"    dc:"创建者"`
	Status     *int    `json:"status"       dc:"状态(1:启用 2:关闭)"`
	Language   *string `json:"language"   dc:"语言"`
	ListReq
}

type SiteGroupListRes struct {
	ListRes
	List []SiteGroupItem `json:"list" dc:"列表"`
}

type SiteGroupListFrdRes struct {
	List []SiteGroupListFrdItem `json:"list" dc:"列表"`
}

type SiteGroupListFrdItem struct {
	//SiteGroupItem
	Id    uint   `json:"id"         dc:"id"`
	Name  string `json:"name"       dc:"组名"`
	Links int    `json:"links" dc:"友链数量"`
}

type SiteGroupItem struct {
	Id                             uint                     `json:"id"         dc:"id"`
	Name                           string                   `json:"name"       dc:"组名"`
	Status                         int                      `json:"status"     dc:"状态(1:开启2:关闭)"`
	TmplId                         uint                     `json:"tmplId"     dc:"模版id"`
	TmplName                       string                   `json:"tmplName"   dc:"模版名称"`
	Remark                         string                   `json:"remark"     dc:"备注"`
	SiteCount                      int                      `json:"siteCount"  dc:"站点数量"`
	CreateTime                     int64                    `json:"createTime" dc:"创建时间"`
	VideoProducts                  []VideoProduct           `json:"videoProducts" dc:"直播源产品列表"`
	Creater                        uint                     `json:"creater" dc:"创建人"`
	TdkId                          int                      `json:"tdkId"           dc:"tdkId"`
	UrlId                          int                      `json:"urlId"           dc:"urlId"`
	UrlName                        string                   `json:"urlName"           dc:"urlName"`
	TdkName                        string                   `json:"tdkName"           dc:"tdkName"`
	VideoProductsShowPc            int                      `json:"videoProductsShowPc" dc:"直播产品是否显示pc端(1:是 2:否)"`
	VideoProductsShowMobile        int                      `json:"videoProductsShowMobile" dc:"直播产品是否显示移动端(1:是 2:否)"`
	OpenReferer                    int                      `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrl          int                      `json:"openVideoProductsCtrl" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMin           int                      `json:"videoProductsCtrlMin" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEnd        int                      `json:"videoProductsCtrlMinEnd" dc:"开赛后多少分钟"`
	PublicWelfareShow              int                      `json:"publicWelfareShow" dc:"是否展示公益信号(1:是 2:否)"`
	Language                       string                   `json:"language"          dc:"语言"`
	OpenSignalDomain               int                      `json:"openSignalDomain" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSetting       VideoProductsShowSetting `json:"videoProductsShowSetting" dc:"直播产品显示设置"`
	NewsUpdatesNum                 int                      `json:"newsUpdatesNum" dc:"新闻更新数量"`
	OpenRefererMobile              int                      `json:"openRefererMobile" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrlMobile    int                      `json:"openVideoProductsCtrlMobile" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMinMobile     int                      `json:"videoProductsCtrlMinMobile" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEndMobile  int                      `json:"videoProductsCtrlMinEndMobile" dc:"开赛后多少分钟"`
	PublicWelfareShowMobile        int                      `json:"publicWelfareShowMobile" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomainMobile         int                      `json:"openSignalDomainMobile" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSettingMobile VideoProductsShowSetting `json:"videoProductsShowSettingMobile" dc:"直播产品显示设置"`
	ModelFake                      int                      `json:"modelFake" dc:"模版伪原创(1-纯数字，2-纯字母，3-数字+字母，4-字母+数字)"`
	ModelFakeBit                   int                      `json:"modelFakeBit" dc:"模版伪原创的位数"`

	OpenFriendlyLink int    `json:"openFriendlyLink" dc:"开启手动友链(1是 2否)"`
	FriendlyLinks    int    `json:"friendlyLinks" dc:"友链数量"`
	Keyword          string `json:"keyword"`
}

type SiteGroupAddReq struct {
	g.Meta                         `path:"/siteGroup/add" tags:"分组管理" method:"post" summary:"添加组"`
	Name                           string                   `v:"required"    json:"name"       dc:"组名"`
	Status                         int                      `v:"between:1,2" json:"status"   dc:"状态(1:开启2:关闭)" d:"1"`
	TmplId                         uint                     `json:"tmplId"       dc:"模版id"`
	Remark                         string                   `json:"remark"     dc:"备注"`
	VideoProductList               []VideoProduct           `json:"videoProductList" dc:"直播源产品列表" d:"[]"`
	TdkId                          int                      `json:"tdkId"           dc:"tdkId"`
	UrlId                          int                      `json:"urlId"           dc:"urlId"`
	VideoProductsShowPc            int                      `json:"videoProductsShowPc" dc:"直播产品是否显示pc端(1:是 2:否)"`
	VideoProductsShowMobile        int                      `json:"videoProductsShowMobile" dc:"直播产品是否显示移动端(1:是 2:否)"`
	OpenReferer                    int                      `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrl          int                      `json:"openVideoProductsCtrl" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMin           int                      `json:"videoProductsCtrlMin" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEnd        int                      `json:"videoProductsCtrlMinEnd" dc:"开赛后多少分钟"`
	Language                       string                   `v:"required" json:"language"          dc:"语言"`
	PublicWelfareShow              int                      `json:"publicWelfareShow" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomain               int                      `json:"openSignalDomain" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSetting       VideoProductsShowSetting `json:"videoProductsShowSetting" dc:"直播产品显示设置"`
	NewsUpdatesNum                 int                      `json:"newsUpdatesNum" dc:"新闻更新数量"`
	OpenRefererMobile              int                      `json:"openRefererMobile" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrlMobile    int                      `json:"openVideoProductsCtrlMobile" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMinMobile     int                      `json:"videoProductsCtrlMinMobile" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEndMobile  int                      `json:"videoProductsCtrlMinEndMobile" dc:"开赛后多少分钟"`
	PublicWelfareShowMobile        int                      `json:"publicWelfareShowMobile" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomainMobile         int                      `json:"openSignalDomainMobile" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSettingMobile VideoProductsShowSetting `json:"videoProductsShowSettingMobile" dc:"直播产品显示设置"`
	ModelFake                      int                      `json:"modelFake" dc:"模版伪原创(1-纯数字，2-纯字母，3-数字+字母，4-字母+数字)"`
	ModelFakeBit                   int                      `json:"modelFakeBit" dc:"模版伪原创的位数"`
	Keyword                        string                   `json:"keyword" dc:"关键词(用于文章)"`
}

type VideoProduct struct {
	Id                   uint   `json:"id"                   dc:"id"`
	Id2                  int64  `json:"id2"                   dc:"id2"`
	Status               int    `json:"status"               dc:"状态(1:显示 2:隐藏)"`
	Name                 string `json:"name"                 dc:"产品名称"`
	Sort                 int    `json:"sort"                 dc:"排序"`
	AllowWildcardDomain  int    `json:"allowWildcardDomain"  dc:"是否支持泛域名(1:是 2:否)"`
	WildcardDomainLength int    `json:"wildcardDomainLength" dc:"泛域名长度(字母+数字)"`
	Url                  string `json:"url"                  dc:"直播源地址"`
	IsVideoRecord        int    `json:"isVideoRecord" dc:"是否录像跳转url(1:是 2:否)"`
	Type                 int    `json:"type"                 dc:"类型(乐球/球世界/)"`
	RandName             int    `json:"randName"               dc:"是否随机名称(1:是 2:否)"`
}

type VideoProductsShowSetting struct {
	WeekdayOn   int   `json:"weekdayOn" dc:"工作日几点上线"`
	WeekdayOff  int   `json:"weekdayOff" dc:"工作日几点下线"`
	WeekdayList []int `json:"weekdayList" dc:"工作日详情"`

	WeekendOn   int   `json:"weekendOn" dc:"周末几点上线"`
	WeekendOff  int   `json:"weekendOff" dc:"周末几点下线"`
	WeekendList []int `json:"weekendList" dc:"周末详情"`
}

type SiteGroupAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type SiteGroupEditReq struct {
	g.Meta                         `path:"/siteGroup/edit" tags:"分组管理" method:"post" summary:"编辑组"`
	Id                             uint                      `v:"required" json:"id"         dc:"id"`
	Name                           *string                   `json:"name"  dc:"组名"`
	Status                         *int                      `v:"between:1,2" json:"status"   dc:"状态(1:开启2:关闭)"`
	TmplId                         *uint                     `json:"tmplId"     dc:"模版id"`
	Remark                         *string                   `json:"remark"     dc:"备注"`
	VideoProductList               *[]VideoProduct           `json:"videoProductList" dc:"直播源产品列表"`
	TdkId                          *int                      `json:"tdkId"           dc:"tdkId"`
	UrlId                          *int                      `json:"urlId"           dc:"urlId"`
	VideoProductsShowPc            *int                      `json:"videoProductsShowPc" dc:"直播产品是否显示pc端(1:是 2:否)"`
	VideoProductsShowMobile        *int                      `json:"videoProductsShowMobile" dc:"直播产品是否显示移动端(1:是 2:否)"`
	OpenReferer                    *int                      `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrl          *int                      `json:"openVideoProductsCtrl" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMin           *int                      `json:"videoProductsCtrlMin" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEnd        *int                      `json:"videoProductsCtrlMinEnd" dc:"开赛后多少分钟"`
	PublicWelfareShow              *int                      `json:"publicWelfareShow" dc:"是否展示公益信号(1:是 2:否)"`
	Language                       *string                   `json:"language"          dc:"语言"`
	OpenSignalDomain               *int                      `json:"openSignalDomain" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSetting       *VideoProductsShowSetting `json:"videoProductsShowSetting" dc:"直播产品显示设置"`
	NewsUpdatesNum                 *int                      `json:"newsUpdatesNum" dc:"新闻更新数量"`
	OpenRefererMobile              *int                      `json:"openRefererMobile" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrlMobile    *int                      `json:"openVideoProductsCtrlMobile" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMinMobile     *int                      `json:"videoProductsCtrlMinMobile" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEndMobile  *int                      `json:"videoProductsCtrlMinEndMobile" dc:"开赛后多少分钟"`
	PublicWelfareShowMobile        *int                      `json:"publicWelfareShowMobile" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomainMobile         *int                      `json:"openSignalDomainMobile" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSettingMobile *VideoProductsShowSetting `json:"videoProductsShowSettingMobile" dc:"直播产品显示设置"`
	Creater                        *string                   `json:"creater"           dc:"创建者"`
	ModelFake                      *int                      `json:"modelFake" dc:"模版伪原创(1-纯数字，2-纯字母，3-数字+字母，4-字母+数字)"`
	ModelFakeBit                   *int                      `json:"modelFakeBit" dc:"模版伪原创的位数"`
	Keyword                        *string                   `json:"keyword" dc:"关键词(用于文章)"`
}

type SiteGroupDeleteReq struct {
	g.Meta `path:"/siteGroup/delete" tags:"分组管理" method:"post" summary:"删除组"`
	Id     uint `v:"required" json:"id"         dc:"id"`
}

type SiteGroupOneReq struct {
	g.Meta `path:"/siteGroup/one" method:"post" tags:"分组管理" summary:"组->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type SiteGroupOneRes struct {
	SiteGroupItem
}

type SiteGroupOptionsReq struct {
	g.Meta `path:"/siteGroup/options" method:"post" tags:"分组管理" summary:"组->选项（供选择器）"`
}

type SiteGroupOptionsRes struct {
	Options []SiteGroupOptionsWithLink `json:"options" dc:"选择器选项数组"`
}

// 新增友链参数
type SiteGroupOptionsWithLink struct {
	model.SelectOption
}

type SiteGroupImportReq struct {
	g.Meta `path:"/siteGroup/import" method:"post" mime:"multipart/form-data" tags:"分组管理" summary:"批量新建"`
	File   *ghttp.UploadFile `json:"file" type:"file" dc:"选择导入文件"`
}

type SiteGroupExportReq struct {
	g.Meta `path:"/siteGroup/export" method:"post" tags:"分组管理" summary:"导出"`
	ListReq
}

type SiteGroupEditBatchReq struct {
	g.Meta         `path:"/siteGroup/editBatch" tags:"分组管理" method:"post" summary:"批量更新"`
	Ids            []uint  `v:"required" json:"ids"        dc:"id"`
	Status         *int    `json:"status"            dc:"状态(1:启用 2:关闭)"`
	TmplId         *uint   `json:"tmplId"            dc:"模版id"`
	TdkId          *int    `json:"tdkId"           dc:"tdkId"`
	UrlId          *int    `json:"urlId"           dc:"urlId"`
	StatisticsCode *string `json:"statisticsCode"    dc:"统计代码"`
	Creater        *string `json:"creater"           dc:"创建者"`

	VideoProductList               *[]VideoProduct           `json:"videoProductList" dc:"直播源产品列表"`
	VideoProductsShowPc            *int                      `json:"videoProductsShowPc" dc:"直播产品是否显示pc端(1:是 2:否)"`
	VideoProductsShowMobile        *int                      `json:"videoProductsShowMobile" dc:"直播产品是否显示移动端(1:是 2:否)"`
	OpenReferer                    *int                      `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrl          *int                      `json:"openVideoProductsCtrl" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMin           *int                      `json:"videoProductsCtrlMin" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEnd        *int                      `json:"videoProductsCtrlMinEnd" dc:"开赛后多少分钟"`
	PublicWelfareShow              *int                      `json:"publicWelfareShow" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomain               *int                      `json:"openSignalDomain" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSetting       *VideoProductsShowSetting `json:"videoProductsShowSetting" dc:"直播产品显示设置"`
	OpenRefererMobile              *int                      `json:"openRefererMobile" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrlMobile    *int                      `json:"openVideoProductsCtrlMobile" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMinMobile     *int                      `json:"videoProductsCtrlMinMobile" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEndMobile  *int                      `json:"videoProductsCtrlMinEndMobile" dc:"开赛后多少分钟"`
	PublicWelfareShowMobile        *int                      `json:"publicWelfareShowMobile" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomainMobile         *int                      `json:"openSignalDomainMobile" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSettingMobile *VideoProductsShowSetting `json:"videoProductsShowSettingMobile" dc:"直播产品显示设置"`
}

type SiteGroupExportSiteReq struct {
	g.Meta `path:"/siteGroup/exportSite" method:"post" tags:"分组管理" summary:"导出分组下的所有站点"`
	Ids    []uint `v:"required" json:"ids"        dc:"分组id"`
}

type SiteGroupEditSignalBatchReq struct {
	g.Meta                         `path:"/siteGroup/editSignalBatch" tags:"分组管理" method:"post" summary:"批量更新信号源"`
	Ids                            []uint                    `v:"required" json:"ids"        dc:"id"`
	VideoProductList               *[]VideoProduct           `json:"videoProductList" dc:"直播源产品列表"`
	VideoProductsShowPc            *int                      `json:"videoProductsShowPc" dc:"直播产品是否显示pc端(1:是 2:否)"`
	VideoProductsShowMobile        *int                      `json:"videoProductsShowMobile" dc:"直播产品是否显示移动端(1:是 2:否)"`
	OpenReferer                    *int                      `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrl          *int                      `json:"openVideoProductsCtrl" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMin           *int                      `json:"videoProductsCtrlMin" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEnd        *int                      `json:"videoProductsCtrlMinEnd" dc:"开赛后多少分钟"`
	PublicWelfareShow              *int                      `json:"publicWelfareShow" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomain               *int                      `json:"openSignalDomain" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSetting       *VideoProductsShowSetting `json:"videoProductsShowSetting" dc:"直播产品显示设置"`
	OpenRefererMobile              *int                      `json:"openRefererMobile" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrlMobile    *int                      `json:"openVideoProductsCtrlMobile" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMinMobile     *int                      `json:"videoProductsCtrlMinMobile" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEndMobile  *int                      `json:"videoProductsCtrlMinEndMobile" dc:"开赛后多少分钟"`
	PublicWelfareShowMobile        *int                      `json:"publicWelfareShowMobile" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomainMobile         *int                      `json:"openSignalDomainMobile" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSettingMobile *VideoProductsShowSetting `json:"videoProductsShowSettingMobile" dc:"直播产品显示设置"`
}
