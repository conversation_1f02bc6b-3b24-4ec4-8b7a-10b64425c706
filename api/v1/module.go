package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"
)

type ModuleListReq struct {
	g.Meta `path:"/module/list" tags:"模块" method:"post" summary:"模块列表"`
	IdLess *int `json:"idLess" dc:"id小于"`
	ListReq
}

type ModuleListRes struct {
	ListRes
	List []entity.Module `json:"list" dc:"列表"`
}

type ModuleAddReq struct {
	g.Meta `path:"/module/add" tags:"模块" method:"post" summary:"添加模块"`
	Id     uint   `v:"required" json:"id"       dc:"id"`
	Name   string `v:"required" json:"name"       dc:"名称"`
}

type ModuleAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type ModuleEditReq struct {
	g.Meta `path:"/module/edit" tags:"模块" method:"post" summary:"编辑模块"`
	Id     uint    `v:"required" json:"id"         dc:"id"`
	Name   *string `json:"Name"         dc:"名称"`
}

type ModuleDeleteReq struct {
	g.Meta `path:"/module/delete" tags:"模块" method:"post" summary:"删除模块"`
	Ids    []uint `v:"required" json:"id"         dc:"id"`
}

type ModuleOptionsReq struct {
	g.Meta `path:"/module/options" method:"post" tags:"模块" summary:"模块->选项（供选择器）"`
}

type ModuleOptionsRes struct {
	Options []model.SelectOption `json:"options" dc:"选择器选项数组"`
}
