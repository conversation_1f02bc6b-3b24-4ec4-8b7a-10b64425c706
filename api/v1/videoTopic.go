package v1

import "github.com/gogf/gf/v2/frame/g"

type VideoTopic struct {
	Sort      uint                       `v:"required|between:0,9999" json:"sort" dc:"排序值"`
	TopicImgs string                     `v:"required" json:"topic_imgs" dc:"专题图片"`
	NameArr   []VideoTopicNameArrItem    `v:"required" json:"name_arr" dc:"标题数组"`
	IsAppShow int                        `v:"required|between:0,1" json:"is_app_show" dc:"是否显示在app中 1:是 0:否"`
	VideoArr  []VideoTopicArticleArrItem `v:"required" json:"video_arr" dc:"视频列表"`
}

type VideoTopicNameArrItem struct {
	LanguageType int    `v:"required|between:0,2" json:"language_type" dc:"语言类型 0：中文 1：英文 2：印尼语"`
	TopicName    string `v:"required" json:"topic_name" dc:"专题名称"`
	ShortName    string `json:"short_name" dc:"短标题"`
}

type VideoTopicArticleArrItem struct {
	VideoId   uint   `v:"required" json:"video_id" dc:"视频id"`
	VideoName string `v:"required" json:"video_name" dc:"视频名称"`
}

type VideoTopicAddReq struct {
	g.Meta `path:"/videoTopic/add" method:"post" tags:"精选专题" summary:"新增"`
	VideoTopic
}

type VideoTopicEditReq struct {
	g.Meta `path:"/videoTopic/edit" method:"post" tags:"精选专题" summary:"编辑"`
	Id     uint `v:"required" json:"id" dc:"专题id"`
	VideoTopic
}

type VideoTopicInfoReq struct {
	g.Meta `path:"/videoTopic/info" method:"get,post" tags:"精选专题" summary:"详情"`
	Id     uint `v:"required" json:"id" dc:"专题id"`
}

type VideoTopicInfoRes struct {
	Id uint `json:"id" dc:"专题id"`
	VideoTopic
}

type VideoTopicListReq struct {
	g.Meta    `path:"/videoTopic/list" method:"get,post" tags:"精选专题" summary:"列表"`
	TopicName string `json:"topic_name" dc:"专题名称"`
	ListReq
}

type VideoTopicListRes struct {
	List []VideoTopicListItem `json:"list"`
	ListRes
}

type VideoTopicListItem struct {
	Id          uint              `json:"id" dc:"专题id"`
	TopicName   string            `json:"topic_name" dc:"专题名称"`
	ShortName   string            `json:"short_name" dc:"短标题"`
	LanguageArr []LanguageArrItem `json:"language_arr" dc:"语言列表"`
	Sort        uint              `json:"sort" dc:"排序值"`
	CreateTime  string            `json:"create_time" dc:"创建时间"`
	VideoNum    uint              `json:"video_num" dc:"视频数量"`
	IsAppShow   uint              `json:"is_app_show" dc:"是否显示在app中 1:是 0:否"`
	CollectNum  int               `json:"collect_num" dc:"收藏数"`
	ShareNum    int               `json:"share_num" dc:"分享数"`
	ViewNum     int               `json:"view_num" dc:"浏览数"`
}

type VideoTopicDeleteReq struct {
	g.Meta `path:"/videoTopic/delete" method:"post" tags:"精选专题" summary:"删除（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"专题id数组"`
}
