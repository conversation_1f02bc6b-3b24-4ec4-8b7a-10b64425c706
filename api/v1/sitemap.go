package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
	entity "gtcms/internal/model/entity/admin"
)

type SitemapListReq struct {
	g.Meta  `path:"/sitemap/list" tags:"sitemap管理" method:"post" summary:"sitemap列表"`
	GroupId *uint `json:"groupId"    dc:"所属分组(分组和站点必传一个）"`
	SiteId  *uint `json:"siteId"    dc:"所属站点(分组和站点必传一个)"`
	Status  *int  `json:"status"       dc:"状态(1:启用 2:禁用)"`
	ListReq
}

type SitemapListRes struct {
	ListRes
	List []SitemapItem `json:"list" dc:"sitemap列表"`
}

type SitemapItem struct {
	entity.Sitemap
	BelongName string `json:"belongName" dc:"所属分组/站点名称"`
}

type SitemapAddReq struct {
	g.Meta             `path:"/sitemap/add" tags:"sitemap管理" method:"post" summary:"添加sitemap"`
	Belong             uint            `json:"belong" dc:"所属：1分组 2站点"`
	BelongId           uint            `v:"required" json:"belongId" dc:"所属分组id"`
	Format             int             `json:"format"             dc:"格式(1:xml地图 2:txt地图 3:html地图)"`
	CreateType         int             `json:"createType"         dc:"生成方式(1:手动 2:自动)"`
	MainRefreshRate    int             `json:"mainRefreshRate"    dc:"首页更新频率(1:每天 2:每星期 3:每月)"`
	ListRefreshRate    int             `json:"listRefreshRate"    dc:"列表页更新频率(1:每天 2:每星期 3:每月)"`
	ContentRefreshRate int             `json:"contentRefreshRate" dc:"内容页更新频率(1:每天 2:每星期 3:每月)"`
	MainLevel          decimal.Decimal `json:"mainLevel"          dc:"首页优先级别"`
	ListLevel          decimal.Decimal `json:"listLevel"          dc:"列表页优先级别"`
	ContentLevel       decimal.Decimal `json:"contentLevel"       dc:"内容页优先级别"`
	Status             int             `json:"status"             dc:"状态(1:启用 2:禁用)"`
	Remark             string          `json:"remark" dc:"说明"`
	LinkNum            int             `json:"linkNum"            dc:"链接数量"`
}

type SitemapAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type SitemapEditReq struct {
	g.Meta             `path:"/sitemap/edit" tags:"sitemap管理" method:"post" summary:"编辑sitemap"`
	Id                 uint             `v:"required" json:"id"         dc:"id"`
	Belong             *uint            `json:"belong" dc:"所属：1分组 2站点"`
	BelongId           *uint            `json:"belongId" dc:"所属分组id"`
	Format             *int             `json:"format"             dc:"格式(1:xml地图 2:txt地图 3:html地图)"`
	CreateType         *int             `json:"createType"         dc:"生成方式(1:手动 2:自动)"`
	MainRefreshRate    *int             `json:"mainRefreshRate"    dc:"首页更新频率(1:每天 2:每星期 3:每月)"`
	ListRefreshRate    *int             `json:"listRefreshRate"    dc:"列表页更新频率(1:每天 2:每星期 3:每月)"`
	ContentRefreshRate *int             `json:"contentRefreshRate" dc:"内容页更新频率(1:每天 2:每星期 3:每月)"`
	MainLevel          *decimal.Decimal `json:"mainLevel"          dc:"首页优先级别"`
	ListLevel          *decimal.Decimal `json:"listLevel"          dc:"列表页优先级别"`
	ContentLevel       *decimal.Decimal `json:"contentLevel"       dc:"内容页优先级别"`
	Status             *int             `json:"status"             dc:"状态(1:启用 2:禁用)"`
	Remark             *string          `json:"remark" dc:"说明"`
	LinkNum            *int             `json:"linkNum"            dc:"链接数量"`
}

type SitemapDeleteReq struct {
	g.Meta `path:"/sitemap/delete" tags:"sitemap管理" method:"post" summary:"删除sitemap"`
	Id     uint `v:"required" json:"id"         dc:"id"`
}

type SitemapOneReq struct {
	g.Meta `path:"/sitemap/one" method:"post" tags:"sitemap管理" summary:"sitemap->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type SitemapOneRes struct {
	SitemapItem
}

type SitemapFastFillReq struct {
	g.Meta   `path:"/urlSetting/fastFill" method:"post" tags:"url设置管理" summary:"使用url配置快速填充内容"`
	Category int `v:"required" json:"category" dc:"2站点 3栏目 4新闻 5标签"`
	GroupId  int `json:"groupId" dc:"所属组id, 如果能取到组id可尽量使用这个"`
	BelongId int `json:"belongId" dc:"在上面category中所属的对应id，比如站点就是站点id"`
}
type SitemapFastFillRes struct {
	Content string `json:"content" dc:"内容数据"`
}

type RobotsListReq struct {
	g.Meta `path:"/robots/list" tags:"robots管理" method:"post" summary:"robots列表"`
	Name   *uint `json:"name"    dc:"name"`
	ListReq
}

type RobotsListRes struct {
	ListRes
	List []RobotsItem `json:"list" dc:"robots列表"`
}

type RobotsItem struct {
	entity.Robots
}

type RobotsAddReq struct {
	g.Meta  `path:"/robots/add" tags:"robots管理" method:"post" summary:"添加robots"`
	Name    string `json:"name" dc:"name"`
	Content string `json:"content" dc:"content"`
}

type RobotsAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type RobotsEditReq struct {
	g.Meta  `path:"/robots/edit" tags:"robots管理" method:"post" summary:"编辑robots"`
	Id      uint    `v:"required" json:"id"         dc:"id"`
	Name    *string `json:"name" dc:"说明"`
	Content *string `json:"content" dc:"说明"`
}

type RobotsDeleteReq struct {
	g.Meta `path:"/robots/delete" tags:"robots管理" method:"post" summary:"删除robots"`
	Id     uint `v:"required" json:"id"         dc:"id"`
}

type RobotsOneReq struct {
	g.Meta `path:"/robots/one" method:"post" tags:"robots管理" summary:"robots->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type RobotsOneRes struct {
	RobotsItem
}
