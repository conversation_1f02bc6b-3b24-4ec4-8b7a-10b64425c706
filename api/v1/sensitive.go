package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	model "gtcms/internal/model/admin"
)

type SensitiveListReq struct {
	g.<PERSON>a `path:"/sensitive/list" method:"post" tags:"敏感词库" summary:"列表"`
	ListReq
	ClassId   int    `json:"classId"       dc:"分类ID"`
	Key       string `json:"key"           dc:"敏感词"`
	IsOpen    int    `json:"isOpen"        dc:"是否启用：1开 2关"`
	StartTime int64  `json:"startTime" dc:"开始时间"`
	EndTime   int64  `json:"endTime" dc:"结束时间"`
}
type SensitiveListRes struct {
	ListRes
	List []*SensitiveVo `json:"list"`
}

type SensitiveVo struct {
	Id        uint   `json:"id"            dc:""`
	ClassId   int    `json:"classId"       dc:"分类ID"`
	ClassName string `json:"className"       dc:"分类名称"`
	Word      string `json:"word"          dc:"敏感词"`
	IsOpen    int    `json:"isOpen"        dc:"是否启用：1开 2关"`
}

type SensitiveAddReq struct {
	g.<PERSON>a  `path:"/sensitive/add" method:"post" tags:"敏感词库" summary:"新增"`
	ClassId int    `json:"classId"       dc:"分类ID"`
	Word    string `json:"word"          dc:"敏感词"`
	IsOpen  int    `json:"isOpen"        dc:"是否启用：1开 2关"`
}

type SensitiveDeleteReq struct {
	g.Meta `path:"/sensitive/delete" method:"post" tags:"敏感词库" summary:"删除"`
	Id     uint `v:"required" json:"id" dc:"id"`
}

type SensitiveEditReq struct {
	g.Meta  `path:"/sensitive/edit" method:"post" tags:"敏感词库" summary:"编辑"`
	Id      uint   `json:"id"            dc:""`
	ClassId int    `json:"classId"       dc:"分类ID"`
	Word    string `json:"word"          dc:"敏感词"`
	IsOpen  int    `json:"isOpen"        dc:"是否启用：1开 2关"`
}

type SensitiveDetailReq struct {
	g.Meta `path:"/sensitive/detail" method:"post" tags:"敏感词库" summary:"详情"`
	Id     uint `v:" " json:"id"`
}
type SensitiveDetailRes struct {
	SensitiveVo
}

type SensitiveOptionsReq struct {
	g.Meta `path:"/sensitive/options" method:"post" tags:"标签" summary:"分类选择器"`
}
type SensitiveOptionsRes struct {
	Options []model.SelectOption `json:"options" dc:"选择器选项数组"`
}
