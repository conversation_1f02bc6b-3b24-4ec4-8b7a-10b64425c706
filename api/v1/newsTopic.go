package v1

import "github.com/gogf/gf/v2/frame/g"

type NewsTopic struct {
	Sort       uint                      `v:"required|between:0,9999" json:"sort" dc:"排序值"`
	TopicImgs  string                    `v:"required" json:"topic_imgs" dc:"专题图片"`
	NameArr    []NewsTopicNameArrItem    `v:"required" json:"name_arr" dc:"标题数组"`
	IsAppShow  int                       `v:"required|between:0,1" json:"is_app_show" dc:"是否显示在app中 1:是 0:否"`
	ArticleArr []NewsTopicArticleArrItem `v:"required" json:"article_arr" dc:"文章列表"`
}

type NewsTopicNameArrItem struct {
	LanguageType int    `v:"required|between:0,2" json:"language_type" dc:"语言类型 0：中文 1：英文 2：印尼语"`
	TopicName    string `v:"required" json:"topic_name" dc:"专题名称"`
	ShortName    string `json:"short_name" dc:"短标题"`
}

type NewsTopicArticleArrItem struct {
	ArticleId   uint   `v:"required" json:"article_id" dc:"文章id"`
	ArticleName string `json:"article_name" dc:"文章名称"`
}

type NewsTopicAddReq struct {
	g.Meta `path:"/newsTopic/add" method:"post" tags:"精选专题" summary:"新增"`
	NewsTopic
}

type NewsTopicEditReq struct {
	g.Meta `path:"/newsTopic/edit" method:"post" tags:"精选专题" summary:"编辑"`
	Id     uint `v:"required" json:"id" dc:"专题id"`
	NewsTopic
}

type NewsTopicInfoReq struct {
	g.Meta `path:"/newsTopic/info" method:"get,post" tags:"精选专题" summary:"详情"`
	Id     uint `v:"required" json:"id" dc:"专题id"`
}

type NewsTopicInfoRes struct {
	Id uint `json:"id" dc:"专题id"`
	NewsTopic
}

type NewsTopicListReq struct {
	g.Meta    `path:"/newsTopic/list" method:"get,post" tags:"精选专题" summary:"列表"`
	TopicName string `json:"topic_name" dc:"专题名称"`
	ListReq
}

type NewsTopicListRes struct {
	List []NewsTopicListItem `json:"list"`
	ListRes
}

type NewsTopicListItem struct {
	Id          uint              `json:"id" dc:"专题id"`
	TopicName   string            `json:"topic_name" dc:"专题名称"`
	ShortName   string            `json:"short_name" dc:"短标题"`
	LanguageArr []LanguageArrItem `json:"language_arr" dc:"语言列表"`
	Sort        uint              `json:"sort" dc:"排序值"`
	CreateTime  string            `json:"create_time" dc:"创建时间"`
	ArticleNum  uint              `json:"article_num" dc:"文章数量"`
	IsAppShow   uint              `json:"is_app_show" dc:"是否显示在app中 1:是 0:否"`
	CollectNum  int               `json:"collect_num" dc:"收藏数"`
	ShareNum    int               `json:"share_num" dc:"分享数"`
	ViewNum     int               `json:"view_num" dc:"浏览数"`
}

type NewsTopicDeleteReq struct {
	g.Meta `path:"/newsTopic/delete" method:"post" tags:"精选专题" summary:"删除（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"专题id数组"`
}
