package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	model "gtcms/internal/model/admin"
)

type AttachmentCategoryListReq struct {
	g.Meta `path:"/attachment/category/list" method:"post" tags:"素材中心" summary:"素材分类-列表"`
	Type   int `json:"type" dc:"素材类型[1 图片, 2视频]" v:"required|in:1,2"`
}
type AttachmentCategoryListRes struct {
	List []*AttachmentCategory `json:"list"`
}

type AttachmentCategory struct {
	model.AttachmentCategory
	Children []*AttachmentCategory `json:"children" dc:"子分类"`
}

type AttachmentCategoryAddReq struct {
	g.Meta     `path:"/attachment/category/add" method:"post" tags:"素材中心" summary:"素材分类-新增"`
	CategoryId uint   `json:"categoryId" dc:"父分类ID(一级分类则传0)" v:"exist:AttachmentCategory"`
	Type       int    `json:"type" dc:"素材类型[1 图片, 2视频]" v:"required|in:1,2"`
	Name       string `json:"name" dc:"名称" v:"required|max-length:60"`
	OrderBy    int    `json:"orderBy" dc:"排序值[值大排前]" v:"min:0" d:"0"`
}

type AttachmentCategoryEditReq struct {
	g.Meta  `path:"/attachment/category/edit" method:"post" tags:"素材中心" summary:"素材分类-编辑"`
	Id      uint    `json:"id" dc:"id" v:"required|exist:AttachmentCategory"`
	Name    *string `json:"name" dc:"名称" v:"min-length:1|max-length:60"`
	OrderBy *int    `json:"orderBy" dc:"排序值[值大排前]" v:"min:0"`
}

type AttachmentCategoryDeleteReq struct {
	g.Meta `path:"/attachment/category/delete" method:"post" tags:"素材中心" summary:"素材分类-删除"`
	Ids    []uint `json:"ids" dc:"id数组" v:"required|exist:AttachmentCategory"`
}

type AttachmentListReq struct {
	g.Meta `path:"/attachment/list" method:"post" tags:"素材中心" summary:"素材-列表"`
	ListReq
	Type int    `json:"type" dc:"素材类型[1 图片, 2视频]" v:"required|in:1,2"`
	Name string `json:"name" dc:"名称"`
}

type AttachmentListRes struct {
	List []AttachmentItem `json:"list"`
	ListRes
}

type AttachmentItem struct {
	model.Attachment
	Size int64  `json:"size" dc:"大小"`
	Url  string `json:"url" dc:"链接"`
}

type AttachmentAddReq struct {
	g.Meta     `path:"/attachment/add" method:"post" tags:"素材中心" summary:"素材-新增"`
	CategoryId uint   `json:"categoryId" dc:"分类ID" v:"required|exist:AttachmentCategory"`
	Type       int    `json:"type" dc:"素材类型[1 图片, 2视频]" v:"required|in:1,2"`
	Name       string `json:"name" dc:"名称" v:"required|max-length:60"`
	Key        string `json:"key" dc:"对象KEY" v:"required"`
}

type AttachmentRenameReq struct {
	g.Meta  `path:"/attachment/rename" method:"post" tags:"素材中心" summary:"素材-重命名"`
	Id      uint   `json:"id" dc:"id" v:"required|exist:Attachment"`
	NewName string `json:"newName" dc:"新名称" v:"required"`
}

type AttachmentMoveReq struct {
	g.Meta     `path:"/attachment/move" method:"post" tags:"素材中心" summary:"素材-移动到指定的分类"`
	Ids        []uint `json:"ids" dc:"id数组" v:"required|exist:Attachment"`
	CategoryId uint   `json:"categoryId" dc:"分类ID" v:"required|exist:AttachmentCategory"`
}

type AttachmentDeleteReq struct {
	g.Meta `path:"/attachment/delete" method:"post" tags:"素材中心" summary:"素材-删除"`
	Ids    []uint `json:"ids" dc:"id数组" v:"required|exist:Attachment"`
}
