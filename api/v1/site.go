package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	model "gtcms/internal/model/admin"
)

type SiteListReq struct {
	g.Meta          `path:"/site/list" tags:"站点管理" method:"post" summary:"站点列表"`
	Name            *string `json:"name" dc:"站点名称"`
	DomainId        *uint   `json:"domainId" dc:"站点域名id"`
	DomainName      *string `json:"domainName" dc:"站点域名名称"`
	GroupId         *uint   `json:"groupId"    dc:"所属分组"`
	CreateTime      *int64  `json:"createTime" dc:"创建时间"`
	CreateTimeStart *int64  `json:"createTimeStart"    dc:"时间开始"`
	CreateTimeEnd   *int64  `json:"createTimeEnd"    dc:"时间结束"`
	Creater         *uint   `json:"creater"    dc:"创建者"`
	Status          *int    `json:"status"       dc:"状态(1:启用 2:关闭)"`
	SeoStatus       *int    `json:"seoStatus"   dc:"seo状态"`
	Language        *string `json:"language"   dc:"语言"`
	ServerId        *int    `json:"serverId"          dc:"服务器id"`
	HasSiteChild    *int    `json:"hasSiteChild" dc:"是否有子站点(1是 2否)"`
	TmplId          *uint   `json:"tmplId"       dc:"模版id"`
	UseProducts     *int    `json:"useProducts" dc:"是否设置直播产品"`
	ListReq
}

type SiteListRes struct {
	ListRes
	List []SiteItem `json:"list" dc:"列表"`
}

type SiteItem struct {
	Id                    uint   `json:"id"                             orm:"id"                                 description:""`
	Name                  string `json:"name"                           orm:"name"                               description:"名称"`
	GroupId               uint   `json:"groupId"                        orm:"group_id"                           description:"组id"`
	DomainId              uint   `json:"domainId"                       orm:"domain_id"                          description:"域名id"`
	Status                int    `json:"status"                         orm:"status"                             description:"状态(1:启用 2:关闭)"`
	TmplId                uint   `json:"tmplId"                         orm:"tmpl_id"                            description:"模版id"`
	Remark                string `json:"remark"                         orm:"remark"                             description:"备注"`
	Creater               uint   `json:"creater"                        orm:"creater"                            description:"创建者id"`
	CreateName            string `json:"createName"                     orm:"create_name"                        description:"创建者"`
	Icp                   string `json:"icp"                            orm:"icp"                                description:"备案号"`
	Picp                  string `json:"picp"                           orm:"picp"                               description:"公安备案号"`
	SeoTitle              string `json:"seoTitle"                       orm:"seo_title"                          description:"首页title"`
	SeoKeyword            string `json:"seoKeyword"                     orm:"seo_keyword"                        description:"首页关键词"`
	SeoDesc               string `json:"seoDesc"                        orm:"seo_desc"                           description:"首页描述"`
	StatisticsCode        string `json:"statisticsCode"                 orm:"statistics_code"                    description:"统计代码"`
	BaiduPush             string `json:"baiduPush"                      orm:"baidu_push"                         description:"百度推送"`
	ShenmaPushAccount     string `json:"shenmaPushAccount"              orm:"shenma_push_account"                description:"神马推送-站长平台账号"`
	ShenmaPushAuthkey     string `json:"shenmaPushAuthkey"              orm:"shenma_push_authkey"                description:"神马推送-域名authkey"`
	ToutiaoPush           string `json:"toutiaoPush"                    orm:"toutiao_push"                       description:"今日头条推送"`
	Logo                  string `json:"logo"                           orm:"logo"                               description:"站点logo"`
	Favicon               string `json:"favicon"                        orm:"favicon"                            description:"网站图标"`
	CreateTime            int64  `json:"createTime"                     orm:"create_time"                        description:"创建时间"`
	UpdateTime            int64  `json:"updateTime"                     orm:"update_time"                        description:"修改时间"`
	DeleteTime            int64  `json:"deleteTime"                     orm:"delete_time"                        description:"删除时间"`
	Views                 int    `json:"views"                          orm:"views"                              description:"每日访问次数"`
	TotalViews            int    `json:"totalViews"                     orm:"total_views"                        description:"历史总访问次数"`
	TdkId                 uint   `json:"tdkId"                          orm:"tdk_id"                             description:"对应tdk_tmpl表的id"`
	UrlId                 uint   `json:"urlId"                          orm:"url_id"                             description:"对应url_settting表的id"`
	SeoStatus             uint   `json:"seoStatus"                      orm:"seo_status"                         description:"seo状态"`
	BaiduVerifyCode       string `json:"baiduVerifyCode"                orm:"baidu_verify_code"                  description:"百度站长平台验证码"`
	AutoPublishNews       int    `json:"autoPublishNews"                orm:"auto_publish_news"                  description:"是否展示信号源"`
	GoogleVerifyCode      string `json:"googleVerifyCode"               orm:"google_verify_code"                 description:"谷歌站长平台验证码"`
	KeyUpdatesNews        int    `json:"keyUpdatesNews"                 orm:"key_updates_news"                   description:"是否展示广告"`
	AutoPublishNewsTime   int64  `json:"autoPublishNewsTime"            orm:"auto_publish_news_time"             description:"自动发布新闻时间"`
	HasAutoPublishNewsNum int    `json:"hasAutoPublishNewsNum"          orm:"has_auto_publish_news_num"          description:"今天已自动发布新闻篇数"`
	Baidu                 uint   `json:"baidu"                          orm:"baidu"                              description:"百度收录的数量"`
	Sougou                uint   `json:"sougou"                         orm:"sougou"                             description:"搜狗收录的数量"`
	DomainJump            int    `json:"domainJump"                     orm:"domain_jump"                        description:"域名跳转(1-www跳@，2-@跳www)"`
	Jump301               string `json:"jump301"                        orm:"jump301"                            description:"301站点"`

	GroupName   string  `json:"groupName"         dc:"组名称"`
	DomainName  string  `json:"domainName"        dc:"域名"`
	TmplName    string  `json:"tmplName"          dc:"模版名称"`
	RunningTime int     `json:"runningTime"       dc:"运行时长"`
	DomainPrice float64 `json:"domainPrice"     dc:"域名价格"`
	TdkName     string  `json:"tdkName"          dc:"tdk名称"`
	UrlName     string  `json:"urlName"          dc:"url名称"`
	ServerId    int     `json:"serverId"          dc:"服务器id"`

	VideoProductsCtrlSecMobile     int                      `json:"videoProductsCtrlSecMobile"     orm:"video_products_ctrl_sec_mobile"     description:"开赛前多少分钟"`
	VideoProductsCtrlSec           int                      `json:"videoProductsCtrlSec"           orm:"video_products_ctrl_sec"            description:"开赛前多少分钟"`
	UseProducts                    int                      `json:"useProducts" dc:"是否设置直播产品"`
	VideoProducts                  []VideoProduct           `json:"videoProducts" dc:"直播源产品列表"`
	VideoProductsShowPc            int                      `json:"videoProductsShowPc" dc:"直播产品是否显示pc端(1:是 2:否)"`
	VideoProductsShowMobile        int                      `json:"videoProductsShowMobile" dc:"直播产品是否显示移动端(1:是 2:否)"`
	OpenReferer                    int                      `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrl          int                      `json:"openVideoProductsCtrl" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMin           int                      `json:"videoProductsCtrlMin" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEnd        int                      `json:"videoProductsCtrlMinEnd" dc:"开赛后多少分钟"`
	PublicWelfareShow              int                      `json:"publicWelfareShow" dc:"是否展示公益信号(1:是 2:否)"`
	Language                       string                   `json:"language"          dc:"语言"`
	OpenSignalDomain               int                      `json:"openSignalDomain" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSetting       VideoProductsShowSetting `json:"videoProductsShowSetting" dc:"直播产品显示设置"`
	NewsUpdatesNum                 int                      `json:"newsUpdatesNum" dc:"新闻更新数量"`
	OpenRefererMobile              int                      `json:"openRefererMobile" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrlMobile    int                      `json:"openVideoProductsCtrlMobile" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMinMobile     int                      `json:"videoProductsCtrlMinMobile" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEndMobile  int                      `json:"videoProductsCtrlMinEndMobile" dc:"开赛后多少分钟"`
	PublicWelfareShowMobile        int                      `json:"publicWelfareShowMobile" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomainMobile         int                      `json:"openSignalDomainMobile" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSettingMobile VideoProductsShowSetting `json:"videoProductsShowSettingMobile" dc:"直播产品显示设置"`
}

type SiteItem2 struct {
	Id                    uint   `json:"id"                             orm:"id"                                 description:""`
	Name                  string `json:"name"                           orm:"name"                               description:"名称"`
	GroupId               uint   `json:"groupId"                        orm:"group_id"                           description:"组id"`
	DomainId              uint   `json:"domainId"                       orm:"domain_id"                          description:"域名id"`
	Status                int    `json:"status"                         orm:"status"                             description:"状态(1:启用 2:关闭)"`
	TmplId                uint   `json:"tmplId"                         orm:"tmpl_id"                            description:"模版id"`
	Remark                string `json:"remark"                         orm:"remark"                             description:"备注"`
	Creater               uint   `json:"creater"                        orm:"creater"                            description:"创建者id"`
	CreateName            string `json:"createName"                     orm:"create_name"                        description:"创建者"`
	Icp                   string `json:"icp"                            orm:"icp"                                description:"备案号"`
	Picp                  string `json:"picp"                           orm:"picp"                               description:"公安备案号"`
	SeoTitle              string `json:"seoTitle"                       orm:"seo_title"                          description:"首页title"`
	SeoKeyword            string `json:"seoKeyword"                     orm:"seo_keyword"                        description:"首页关键词"`
	SeoDesc               string `json:"seoDesc"                        orm:"seo_desc"                           description:"首页描述"`
	StatisticsCode        string `json:"statisticsCode"                 orm:"statistics_code"                    description:"统计代码"`
	BaiduPush             string `json:"baiduPush"                      orm:"baidu_push"                         description:"百度推送"`
	ShenmaPushAccount     string `json:"shenmaPushAccount"              orm:"shenma_push_account"                description:"神马推送-站长平台账号"`
	ShenmaPushAuthkey     string `json:"shenmaPushAuthkey"              orm:"shenma_push_authkey"                description:"神马推送-域名authkey"`
	ToutiaoPush           string `json:"toutiaoPush"                    orm:"toutiao_push"                       description:"今日头条推送"`
	Logo                  string `json:"logo"                           orm:"logo"                               description:"站点logo"`
	Favicon               string `json:"favicon"                        orm:"favicon"                            description:"网站图标"`
	CreateTime            int64  `json:"createTime"                     orm:"create_time"                        description:"创建时间"`
	UpdateTime            int64  `json:"updateTime"                     orm:"update_time"                        description:"修改时间"`
	DeleteTime            int64  `json:"deleteTime"                     orm:"delete_time"                        description:"删除时间"`
	Views                 int    `json:"views"                          orm:"views"                              description:"每日访问次数"`
	TotalViews            int    `json:"totalViews"                     orm:"total_views"                        description:"历史总访问次数"`
	TdkId                 uint   `json:"tdkId"                          orm:"tdk_id"                             description:"对应tdk_tmpl表的id"`
	UrlId                 uint   `json:"urlId"                          orm:"url_id"                             description:"对应url_settting表的id"`
	SeoStatus             uint   `json:"seoStatus"                      orm:"seo_status"                         description:"seo状态"`
	BaiduVerifyCode       string `json:"baiduVerifyCode"                orm:"baidu_verify_code"                  description:"百度站长平台验证码"`
	AutoPublishNews       int    `json:"autoPublishNews"                orm:"auto_publish_news"                  description:"是否展示信号源"`
	GoogleVerifyCode      string `json:"googleVerifyCode"               orm:"google_verify_code"                 description:"谷歌站长平台验证码"`
	KeyUpdatesNews        int    `json:"keyUpdatesNews"                 orm:"key_updates_news"                   description:"是否展示广告"`
	AutoPublishNewsTime   int64  `json:"autoPublishNewsTime"            orm:"auto_publish_news_time"             description:"自动发布新闻时间"`
	HasAutoPublishNewsNum int    `json:"hasAutoPublishNewsNum"          orm:"has_auto_publish_news_num"          description:"今天已自动发布新闻篇数"`
	Baidu                 uint   `json:"baidu"                          orm:"baidu"                              description:"百度收录的数量"`
	Sougou                uint   `json:"sougou"                         orm:"sougou"                             description:"搜狗收录的数量"`
	DomainJump            int    `json:"domainJump"                     orm:"domain_jump"                        description:"域名跳转(1-www跳@，2-@跳www)"`
	Jump301               string `json:"jump301"                        orm:"jump301"   `

	GroupName   string  `json:"groupName"         dc:"组名称"`
	DomainName  string  `json:"domainName"        dc:"域名"`
	TmplName    string  `json:"tmplName"          dc:"模版名称"`
	RunningTime int     `json:"runningTime"       dc:"运行时长"`
	DomainPrice float64 `json:"domainPrice"     dc:"域名价格"`
	TdkName     string  `json:"tdkName"          dc:"tdk名称"`
	UrlName     string  `json:"urlName"          dc:"url名称"`
	ServerId    int     `json:"serverId"          dc:"服务器id"`

	VideoProductsCtrlSecMobile     int    `json:"videoProductsCtrlSecMobile"     orm:"video_products_ctrl_sec_mobile"     description:"开赛前多少分钟"`
	VideoProductsCtrlSec           int    `json:"videoProductsCtrlSec"           orm:"video_products_ctrl_sec"            description:"开赛前多少分钟"`
	UseProducts                    int    `json:"useProducts" dc:"是否设置直播产品"`
	VideoProducts                  string `json:"videoProducts" dc:"直播源产品列表"`
	VideoProductsShowPc            int    `json:"videoProductsShowPc" dc:"直播产品是否显示pc端(1:是 2:否)"`
	VideoProductsShowMobile        int    `json:"videoProductsShowMobile" dc:"直播产品是否显示移动端(1:是 2:否)"`
	OpenReferer                    int    `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrl          int    `json:"openVideoProductsCtrl" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMin           int    `json:"videoProductsCtrlMin" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEnd        int    `json:"videoProductsCtrlMinEnd" dc:"开赛后多少分钟"`
	PublicWelfareShow              int    `json:"publicWelfareShow" dc:"是否展示公益信号(1:是 2:否)"`
	Language                       string `json:"language"          dc:"语言"`
	OpenSignalDomain               int    `json:"openSignalDomain" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSetting       string `json:"videoProductsShowSetting" dc:"直播产品显示设置"`
	NewsUpdatesNum                 int    `json:"newsUpdatesNum" dc:"新闻更新数量"`
	OpenRefererMobile              int    `json:"openRefererMobile" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrlMobile    int    `json:"openVideoProductsCtrlMobile" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMinMobile     int    `json:"videoProductsCtrlMinMobile" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEndMobile  int    `json:"videoProductsCtrlMinEndMobile" dc:"开赛后多少分钟"`
	PublicWelfareShowMobile        int    `json:"publicWelfareShowMobile" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomainMobile         int    `json:"openSignalDomainMobile" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSettingMobile string `json:"videoProductsShowSettingMobile" dc:"直播产品显示设置"`
}

type SiteAddReq struct {
	g.Meta            `path:"/site/add" tags:"站点管理" method:"post" summary:"添加站点"`
	Name              string `v:"required" json:"name"              dc:"名称"`
	Language          string `v:"required" json:"language"          dc:"语言"`
	GroupId           uint   `v:"required" json:"groupId"           dc:"组id"`
	DomainId          uint   `v:"required" json:"domainId"          dc:"域名id"`
	Status            int    `v:"required" json:"status"            dc:"状态(1:启用 2:关闭)" d:"1"`
	TmplId            uint   `v:"required" json:"tmplId"            dc:"模版id"`
	Remark            string `json:"remark"            dc:"备注"`
	Creater           string `json:"creater"           dc:"创建者"`
	Icp               string `json:"icp"               dc:"备案号"`
	Picp              string `json:"picp"              dc:"公安备案号"`
	SeoTitle          string `v:"required" json:"seoTitle"          dc:"首页title"`
	SeoKeyword        string `v:"required" json:"seoKeyword"       dc:"首页关键词"`
	SeoDesc           string `v:"required" json:"seoDesc"            dc:"首页描述"`
	StatisticsCode    string `json:"statisticsCode"    dc:"统计代码"`
	BaiduPush         string `json:"baiduPush"         dc:"百度推送"`
	ShenmaPushAccount string `json:"shenmaPushAccount" dc:"神马推送-站长平台账号"`
	ShenmaPushAuthkey string `json:"shenmaPushAuthkey" dc:"神马推送-域名authkey"`
	ToutiaoPush       string `json:"toutiaoPush"       dc:"今日头条推送"`
	Logo              string `json:"logo"              dc:"站点logo"`
	Favicon           string `json:"favicon"           dc:"网站图标"`
	TdkId             uint   `v:"required" json:"tdkId"           dc:"tdkId"`
	UrlId             uint   `v:"required" json:"urlId"           dc:"urlId"`
	SeoStatus         int    `json:"seoStatus"   dc:"seo状态"`
	BaiduVerifyCode   string `json:"baiduVerifyCode" dc:"百度站长平台验证码"`
	GoogleVerifyCode  string `json:"googleVerifyCode" dc:"谷歌站长平台验证码"`
	AutoPublishNews   int    `json:"autoPublishNews" dc:"是否展示信号源(1:是 2:否)" d:"1"`
	KeyUpdatesNews    int    `json:"keyUpdatesNews" dc:"是否展示广告(1是 2否)" d:"1"`
	DomainJump        int    `json:"domainJump"     dc:"域名跳转(1-www跳@，2-@跳www)"`

	UseProducts                    int                      `json:"useProducts" dc:"是否设置直播产品"`
	VideoProductList               []VideoProduct           `json:"videoProductList" dc:"直播源产品列表" d:"[]"`
	VideoProductsShowPc            int                      `json:"videoProductsShowPc" dc:"直播产品是否显示pc端(1:是 2:否)"`
	VideoProductsShowMobile        int                      `json:"videoProductsShowMobile" dc:"直播产品是否显示移动端(1:是 2:否)"`
	OpenReferer                    int                      `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrl          int                      `json:"openVideoProductsCtrl" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMin           int                      `json:"videoProductsCtrlMin" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEnd        int                      `json:"videoProductsCtrlMinEnd" dc:"开赛后多少分钟"`
	PublicWelfareShow              int                      `json:"publicWelfareShow" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomain               int                      `json:"openSignalDomain" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSetting       VideoProductsShowSetting `json:"videoProductsShowSetting" dc:"直播产品显示设置"`
	OpenRefererMobile              int                      `json:"openRefererMobile" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrlMobile    int                      `json:"openVideoProductsCtrlMobile" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMinMobile     int                      `json:"videoProductsCtrlMinMobile" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEndMobile  int                      `json:"videoProductsCtrlMinEndMobile" dc:"开赛后多少分钟"`
	PublicWelfareShowMobile        int                      `json:"publicWelfareShowMobile" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomainMobile         int                      `json:"openSignalDomainMobile" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSettingMobile VideoProductsShowSetting `json:"videoProductsShowSettingMobile" dc:"直播产品显示设置"`
}

type SiteAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type SiteIncludeReq struct {
	g.Meta `path:"/site/includeQuery" tags:"站点收录查询" method:"post" summary:"站点收录查询"`
	Ids    []uint `v:"required" json:"ids"        dc:"站点id"`
}

type SiteIncludeData struct {
	Id     uint   `json:"id" dc:"站点id"`
	Domain string `json:"domain" dc:"网站url"`
	Baidu  uint   `json:"baidu" dc:"baidu收录指数"`
	Sougou uint   `json:"sougou" dc:"sougou收录指数"`
}

type SiteIncludeRes struct {
	IncludeList []SiteIncludeData `json:"includeList" dc:"搜索引擎及收录指数"`
}

type SiteEditReq struct {
	g.Meta            `path:"/site/edit" tags:"站点管理" method:"post" summary:"编辑站点"`
	Id                uint    `v:"required" json:"id"         dc:"id"`
	Name              *string `json:"name"              dc:"名称"`
	Language          *string `json:"language"          dc:"语言"`
	GroupId           *uint   `json:"groupId"           dc:"组id"`
	DomainId          *uint   `json:"domainId"          dc:"域名id"`
	Status            *int    `json:"status"            dc:"状态(1:启用 2:关闭)"`
	TmplId            *uint   `json:"tmplId"            dc:"模版id"`
	Remark            *string `json:"remark"            dc:"备注"`
	Creater           *string `json:"creater"           dc:"创建者"`
	Icp               *string `json:"icp"               dc:"备案号"`
	Picp              *string `json:"picp"              dc:"公安备案号"`
	SeoTitle          *string `json:"seoTitle"          dc:"首页title"`
	SeoKeyword        *string `json:"seoKeyword"       dc:"首页关键词"`
	SeoDesc           *string `json:"seoDesc"            dc:"首页描述"`
	StatisticsCode    *string `json:"statisticsCode"    dc:"统计代码"`
	BaiduPush         *string `json:"baiduPush"         dc:"百度推送"`
	ShenmaPushAccount *string `json:"shenmaPushAccount" dc:"神马推送-站长平台账号"`
	ShenmaPushAuthkey *string `json:"shenmaPushAuthkey" dc:"神马推送-域名authkey"`
	ToutiaoPush       *string `json:"toutiaoPush"       dc:"今日头条推送"`
	Logo              *string `json:"logo"              dc:"站点logo"`
	Favicon           *string `json:"favicon"           dc:"网站图标"`
	TdkId             *int    `json:"tdkId"           dc:"tdkId"`
	UrlId             *int    `json:"urlId"           dc:"urlId"`
	BaiduVerifyCode   *string `json:"baiduVerifyCode" dc:"百度站长平台验证码"`
	GoogleVerifyCode  *string `json:"googleVerifyCode" dc:"谷歌站长平台验证码"`
	SeoStatus         *int    `json:"seoStatus"   dc:"seo状态"`
	AutoPublishNews   *int    `json:"autoPublishNews" dc:"是否展示信号源(1:是 2:否)" d:"1"`
	KeyUpdatesNews    *int    `json:"keyUpdatesNews" dc:"是否展示广告(1是 2否)" d:"1"`
	DomainJump        *int    `json:"domainJump"     dc:"域名跳转(1-www跳@，2-@跳www)"`

	UseProducts                    *int                      `json:"useProducts" dc:"是否设置直播产品"`
	VideoProductList               *[]VideoProduct           `json:"videoProductList" dc:"直播源产品列表"`
	VideoProductsShowPc            *int                      `json:"videoProductsShowPc" dc:"直播产品是否显示pc端(1:是 2:否)"`
	VideoProductsShowMobile        *int                      `json:"videoProductsShowMobile" dc:"直播产品是否显示移动端(1:是 2:否)"`
	OpenReferer                    *int                      `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrl          *int                      `json:"openVideoProductsCtrl" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMin           *int                      `json:"videoProductsCtrlMin" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEnd        *int                      `json:"videoProductsCtrlMinEnd" dc:"开赛后多少分钟"`
	PublicWelfareShow              *int                      `json:"publicWelfareShow" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomain               *int                      `json:"openSignalDomain" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSetting       *VideoProductsShowSetting `json:"videoProductsShowSetting" dc:"直播产品显示设置"`
	OpenRefererMobile              *int                      `json:"openRefererMobile" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrlMobile    *int                      `json:"openVideoProductsCtrlMobile" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMinMobile     *int                      `json:"videoProductsCtrlMinMobile" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEndMobile  *int                      `json:"videoProductsCtrlMinEndMobile" dc:"开赛后多少分钟"`
	PublicWelfareShowMobile        *int                      `json:"publicWelfareShowMobile" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomainMobile         *int                      `json:"openSignalDomainMobile" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSettingMobile *VideoProductsShowSetting `json:"videoProductsShowSettingMobile" dc:"直播产品显示设置"`
}

type SiteDeleteReq struct {
	g.Meta `path:"/site/delete" tags:"站点管理" method:"post" summary:"删除站点/批量删除"`
	Id     uint   `json:"id"         dc:"id"`
	Ids    []uint `json:"ids"         dc:"ids"`
}

type SiteOneReq struct {
	g.Meta `path:"/site/one" method:"post" tags:"站点管理" summary:"站点->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type SiteOneRes struct {
	SiteItem
}

type SiteOptionsReq struct {
	g.Meta  `path:"/site/options" method:"post" tags:"站点管理" summary:"站点->选项（供选择器）"`
	GroupId *uint `json:"groupId"    dc:"所属分组"`
}

type SiteOptionsRes struct {
	Options []model.SelectOption `json:"options" dc:"选择器选项数站点"`
}

type SiteEditBatchReq struct {
	g.Meta                 `path:"/site/editBatch" tags:"站点管理" method:"post" summary:"批量更新"`
	Ids                    []uint  `v:"required" json:"ids"        dc:"id"`
	GroupId                *uint   `json:"groupId"           dc:"组id"`
	Status                 *int    `json:"status"            dc:"状态(1:启用 2:关闭)"`
	TmplId                 *uint   `json:"tmplId"            dc:"模版id"`
	StatisticsCode         *string `json:"statisticsCode"    dc:"统计代码"`
	TdkId                  *int    `json:"tdkId"           dc:"tdkId"`
	UrlId                  *int    `json:"urlId"           dc:"urlId"`
	Language               *string `json:"language"          dc:"语言"`
	Creater                *string `json:"creater"           dc:"创建者"`
	AutoPublishNews        *int    `json:"autoPublishNews" dc:"是否展示信号源(1:是 2:否)" d:"1"`
	KeyUpdatesNews         *int    `json:"keyUpdatesNews" dc:"是否展示广告(1是 2否)" d:"1"`
	SeoStatus              *int    `json:"seoStatus"   dc:"seo状态"`
	DomainJump             *int    `json:"domainJump"     dc:"域名跳转(1-www跳@，2-@跳www)"`
	StatisticsCodeIsAppend *bool   `json:"statisticsCodeIsAppend" dc:"统计代码是否追加"`
	BaiduVerifyCode        *string `json:"baiduVerifyCode" dc:"百度站长平台验证码"`
	GoogleVerifyCode       *string `json:"googleVerifyCode" dc:"谷歌站长平台验证码"`

	UseProducts                    *int                      `json:"useProducts" dc:"是否设置直播产品"`
	VideoProductList               *[]VideoProduct           `json:"videoProductList" dc:"直播源产品列表"`
	VideoProductsShowPc            *int                      `json:"videoProductsShowPc" dc:"直播产品是否显示pc端(1:是 2:否)"`
	VideoProductsShowMobile        *int                      `json:"videoProductsShowMobile" dc:"直播产品是否显示移动端(1:是 2:否)"`
	OpenReferer                    *int                      `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrl          *int                      `json:"openVideoProductsCtrl" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMin           *int                      `json:"videoProductsCtrlMin" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEnd        *int                      `json:"videoProductsCtrlMinEnd" dc:"开赛后多少分钟"`
	PublicWelfareShow              *int                      `json:"publicWelfareShow" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomain               *int                      `json:"openSignalDomain" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSetting       *VideoProductsShowSetting `json:"videoProductsShowSetting" dc:"直播产品显示设置"`
	OpenRefererMobile              *int                      `json:"openRefererMobile" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrlMobile    *int                      `json:"openVideoProductsCtrlMobile" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMinMobile     *int                      `json:"videoProductsCtrlMinMobile" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEndMobile  *int                      `json:"videoProductsCtrlMinEndMobile" dc:"开赛后多少分钟"`
	PublicWelfareShowMobile        *int                      `json:"publicWelfareShowMobile" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomainMobile         *int                      `json:"openSignalDomainMobile" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSettingMobile *VideoProductsShowSetting `json:"videoProductsShowSettingMobile" dc:"直播产品显示设置"`
}

type SiteImportReq struct {
	g.Meta `path:"/site/import" method:"post" mime:"multipart/form-data" tags:"站点管理" summary:"批量新建"`
	File   *ghttp.UploadFile `json:"file" type:"file" dc:"选择导入文件"`
}

type SiteExportDomainReq struct {
	g.Meta `path:"/site/exportDomain" method:"post" tags:"站点管理" summary:"导出域名"`
	Ids    []uint `v:"required" json:"ids"        dc:"id"`
}

type SiteExportReq struct {
	g.Meta `path:"/site/export" method:"post" tags:"站点管理" summary:"导出站点"`
	Ids    []uint `v:"required" json:"ids"        dc:"id"`
}

// 批量修改tdk和统计代码
type SiteEditTdkBatchReq struct {
	g.Meta `path:"/site/editTdkBatch" method:"post" mime:"multipart/form-data" tags:"站点管理" summary:"批量修改tdk和统计代码"`
	File   *ghttp.UploadFile `json:"file" type:"file" dc:"选择导入文件"`
}

type SiteEditSignalBatchReq struct {
	g.Meta                         `path:"/site/editSignalBatch" tags:"站点管理" method:"post" summary:"批量更新信号源"`
	Ids                            []uint                    `v:"required" json:"ids"        dc:"id"`
	UseProducts                    *int                      `json:"useProducts" dc:"是否设置直播产品"`
	VideoProductList               *[]VideoProduct           `json:"videoProductList" dc:"直播源产品列表"`
	VideoProductsShowPc            *int                      `json:"videoProductsShowPc" dc:"直播产品是否显示pc端(1:是 2:否)"`
	VideoProductsShowMobile        *int                      `json:"videoProductsShowMobile" dc:"直播产品是否显示移动端(1:是 2:否)"`
	OpenReferer                    *int                      `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrl          *int                      `json:"openVideoProductsCtrl" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMin           *int                      `json:"videoProductsCtrlMin" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEnd        *int                      `json:"videoProductsCtrlMinEnd" dc:"开赛后多少分钟"`
	PublicWelfareShow              *int                      `json:"publicWelfareShow" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomain               *int                      `json:"openSignalDomain" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSetting       *VideoProductsShowSetting `json:"videoProductsShowSetting" dc:"直播产品显示设置"`
	OpenRefererMobile              *int                      `json:"openRefererMobile" dc:"是否开启搜索来路校验(1:是 2:否)"`
	OpenVideoProductsCtrlMobile    *int                      `json:"openVideoProductsCtrlMobile" dc:"是否开启直播产品控制(1:是 2:否)"`
	VideoProductsCtrlMinMobile     *int                      `json:"videoProductsCtrlMinMobile" dc:"开赛前多少分钟"`
	VideoProductsCtrlMinEndMobile  *int                      `json:"videoProductsCtrlMinEndMobile" dc:"开赛后多少分钟"`
	PublicWelfareShowMobile        *int                      `json:"publicWelfareShowMobile" dc:"是否展示公益信号(1:是 2:否)"`
	OpenSignalDomainMobile         *int                      `json:"openSignalDomainMobile" dc:"是否开启信号域名跳转(1:是 2:否)"`
	VideoProductsShowSettingMobile *VideoProductsShowSetting `json:"videoProductsShowSettingMobile" dc:"直播产品显示设置"`
}
