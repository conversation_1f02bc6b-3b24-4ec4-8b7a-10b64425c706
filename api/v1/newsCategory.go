package v1

import "github.com/gogf/gf/v2/frame/g"

type NewsCategory struct {
	ParentId  uint                      `json:"parent_id" dc:"父级id"`
	Sort      uint                      `v:"required|between:0,9999" json:"sort" dc:"排序值"`
	CoverImgs string                    `v:"required" json:"cover_imgs" dc:"封面图"`
	NameArr   []NewsCategoryNameArrItem `v:"required" json:"name_arr" dc:"标题数组"`
	IsAppShow int                       `v:"required|between:0,1" json:"is_app_show" dc:"是否显示在app中 1:是 0:否"`
	Remark    string                    `json:"remark" dc:"备注"`
	Status    int                       `v:"required|between:0,1" json:"status" dc:"状态 1:启用 0:停用"`
}

type NewsCategoryNameArrItem struct {
	LanguageType int    `v:"required|between:0,2" json:"language_type" dc:"语言类型 0：中文 1：英文 2：印尼语"`
	CategoryName string `v:"required" json:"category_name" dc:"分类名称"`
}

type NewsCategoryAddReq struct {
	g.Meta `path:"/newsCategory/add" method:"post" tags:"咨询分类" summary:"新增"`
	NewsCategory
}

type NewsCategoryAddRes struct{}

type NewsCategoryEditReq struct {
	g.Meta `path:"/newsCategory/edit" method:"post" tags:"咨询分类" summary:"修改"`
	Id     uint `v:"required" json:"id" dc:"分类id"`
	NewsCategory
}

type NewsCategoryEditRes struct{}

type NewsCategoryListReq struct {
	g.Meta `path:"/newsCategory/list" method:"get,post" tags:"咨询分类" summary:"列表"`
	ListReq
	CategoryName string `json:"category_name" dc:"分类名称"`
	Status       int    `json:"status" d:"-1" dc:"状态 1:启用 0:停用 默认-1：全部"`
}

type NewsCategoryListRes struct {
	List []NewsCategoryListItem `json:"list"`
	ListRes
}

type NewsCategoryListItem struct {
	Id           uint                    `json:"id" dc:"分类ID"`
	Sort         uint                    `v:"required|between:0,9999" json:"sort" dc:"排序值"`
	Status       int                     `json:"status" dc:"状态 1:启用 0:停用"`
	StatusText   string                  `json:"status_text" dc:"状态文案"`
	CategoryName string                  `json:"category_name" dc:"分类名称"`
	LanguageArr  []LanguageArrItem       `json:"language_arr" dc:"语言列表"`
	Children     []*NewsCategoryListItem `json:"children" dc:"子级列表"`
}

type NewsCategoryInfoReq struct {
	g.Meta `path:"/newsCategory/info" method:"get,post" tags:"咨询分类" summary:"详情"`
	Id     uint `v:"required" json:"id" dc:"分类id"`
}

type NewsCategoryInfoRes struct {
	ParentId  uint                      `json:"parent_id" dc:"父级id"`
	Sort      uint                      `json:"sort" dc:"排序值"`
	CoverImgs string                    `json:"cover_imgs" dc:"封面图"`
	NameArr   []NewsCategoryNameArrItem `json:"name_arr" dc:"标题数组"`
	IsAppShow uint                      `json:"is_app_show" dc:"是否显示在app中 1:是 0:否"`
	Remark    string                    `json:"remark" dc:"备注"`
	Status    int                       `json:"status" dc:"状态 1:启用 0:停用"`
}

type NewsCategoryDeleteReq struct {
	g.Meta `path:"/newsCategory/delete" method:"post" tags:"分类管理" summary:"删除（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"分类id数组"`
}

type NewsCategoryDeleteRes struct{}
