package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type VideoProductListReq struct {
	g.Meta       `path:"/videoProduct/list" tags:"直播产品管理" method:"post" summary:"直播产品列表"`
	Name         *string `json:"name" dc:"名称"`
	Status       *int    `json:"status"       dc:"状态(1:启用 2:禁用)"`
	BelongUserId *uint   `json:"belongUserId"         dc:"所属用户id"`
	ListReq
}

type VideoProductClicksReq struct {
	g.Meta     `path:"/videoProduct/clicks" tags:"直播产品点击统计" method:"post" summary:"直播产品点击统计"`
	StartTime  int64  `json:"startTime"    dc:"开始时间"`
	EndTime    int64  `json:"endTime"    dc:"结束时间"`
	SignalType string `json:"signalType"    dc:"类型"`
	ListReq
}

type VideoProductClicksRes struct {
	ListRes
	List []VideoProductClicksItem `json:"list" dc:"点击统计列表"`
}

type VideoProductClicksExportReq struct {
	g.Meta     `path:"/videoProduct/clicksExport" tags:"直播产品点击统计导出" method:"post" summary:"直播产品点击统计导出"`
	StartTime  int64  `json:"startTime"    dc:"开始时间"`
	EndTime    int64  `json:"endTime"    dc:"结束时间"`
	SignalType string `json:"signalType"    dc:"类型"`

	ListReq
}

type VideoProductClicksItem struct {
	Id        int    `json:"id" dc:"直播产品id"`
	Name      string `json:"name" dc:"直播产品名称"`
	Today     int    `json:"today" dc:"今天"`
	Yesterday int    `json:"yesterday" dc:"昨天"`
	Week      int    `json:"week" dc:"本周"`
	LastWeek  int    `json:"lastWeek" dc:"上周"`
	Month     int    `json:"month" dc:"本月"`
	LastMonth int    `json:"lastMonth" dc:"上月"`
}

//type VideoProductClicksExportItem struct {
//	Id     int    `json:"id" dc:"直播产品id"`
//	Name   string `json:"name" dc:"直播产品名称"`
//	Clicks int    `json:"clicks" dc:"总点击量"`
//}

type VideoProductItem struct {
	//entity.VideoProduct
	BelongUserName string `json:"belongUserName" dc:"所属用户名称"`
}

type VideoProductListRes struct {
	ListRes
	List []VideoProductItem `json:"list" dc:"直播产品列表"`
}

type VideoProductAddReq struct {
	g.Meta               `path:"/videoProduct/add" tags:"直播产品管理" method:"post" summary:"添加直播产品"`
	Name                 string `json:"name"                 dc:"产品名称"`
	Url                  string `json:"url"                  dc:"跳转url"`
	Sort                 int    `json:"sort"                 dc:"排序"`
	Status               int    `json:"status"                 dc:"状态(1:显示 2:隐藏)" d:"1"`
	AllowWildcardDomain  int    `json:"allowWildcardDomain"  dc:"是否支持泛域名(1:是 2:否)"`
	WildcardDomainLength int    `json:"wildcardDomainLength" dc:"泛域名长度(字母+数字)"`
	BelongUserId         uint   `v:"required" json:"belongUserId"         dc:"所属用户id"`
	IsVideoRecord        int    `json:"isVideoRecord"         dc:"录像跳转域名(1是 2否)"`
	IsJoinSuff           int    `json:"isJoinSuff"         dc:"是否拼接直播间后缀(1:是 2:否)"`
	Type                 int    `json:"type"                 dc:"类型"`
}

type VideoProductAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type VideoProductEditReq struct {
	g.Meta               `path:"/videoProduct/edit" tags:"直播产品管理" method:"post" summary:"编辑直播产品"`
	Id                   uint    `v:"required" json:"id"         dc:"id"`
	Name                 *string `json:"name"                 dc:"产品名称"`
	Url                  *string `json:"url"                  dc:"跳转url"`
	Sort                 *int    `json:"sort"                 dc:"排序"`
	Status               *int    `json:"status"                 dc:"状态(1:显示 2:隐藏)"`
	AllowWildcardDomain  *int    `json:"allowWildcardDomain"  dc:"是否支持泛域名(1:是 2:否)"`
	WildcardDomainLength *int    `json:"wildcardDomainLength" dc:"泛域名长度(字母+数字)"`
	BelongUserId         *uint   `json:"belongUserId"         dc:"所属用户id"`
	IsVideoRecord        *int    `json:"isVideoRecord"         dc:"录像跳转域名(1是 2否)"`
	IsJoinSuff           *int    `json:"isJoinSuff"         dc:"是否拼接直播间后缀(1:是 2:否)"`
	Type                 *int    `json:"type"                 dc:"类型"`
}

type VideoProductDeleteReq struct {
	g.Meta `path:"/videoProduct/delete" tags:"直播产品管理" method:"post" summary:"删除直播产品"`
	Id     uint `json:"id"         dc:"id"`
}

type VideoProductOneReq struct {
	g.Meta `path:"/videoProduct/one" method:"post" tags:"直播产品管理" summary:"直播产品->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type VideoProductOneRes struct {
	//entity.VideoProduct
}

type VideoProductOptionsReq struct {
	g.Meta `path:"/videoProduct/options" method:"post" tags:"直播产品管理" summary:"直播产品->选项（供选择器）"`
}

type VideoProductOptionsRes struct {
	List []VideoProductOptionItem `json:"list" dc:"选择器选项数组"`
}

type VideoProductOptionItem struct {
	Id                   uint   `json:"id"                   dc:""`
	Status               int    `json:"status"               dc:"状态(1:显示 2:隐藏)"`
	Name                 string `json:"name"                 dc:"产品名称"`
	Sort                 int    `json:"sort"                 dc:"排序"`
	AllowWildcardDomain  int    `json:"allowWildcardDomain"  dc:"是否支持泛域名(1:是 2:否)"`
	WildcardDomainLength int    `json:"wildcardDomainLength" dc:"泛域名长度(字母+数字)"`
	IsJoinSuff           int    `json:"isJoinSuff" dc:"是否拼接直播间后缀(1:是 2:否)"`
	Type                 int    `json:"type"                 dc:"类型"`
}
