package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	model "gtcms/internal/model/admin"
)

type AccountAuditLogReq struct {
	g.Meta `path:"/auditLog/list" method:"post" tags:"管理员操作" summary:"操作日志"`
	ListReq
	Account   *string `v:"" json:"account" dc:"操作人员" `
	Content   *string `v:"" json:"content" dc:"操作内容 模糊匹配"  `
	NodeID    *int    `v:"" json:"nodeID" dc:"操作节点ID"  `
	StartTime int64   `json:"startTime" dc:"开始时间"`
	EndTime   int64   `json:"endTime" dc:"结束时间"`
}
type AccountAuditLogRes struct {
	ListRes
	List []*model.AdminModifyLogVo `json:"list"`
}

type CmsNodeReq struct {
	g.Meta `path:"/auditLog/nodeList" method:"post" tags:"管理员操作" summary:"操作节点"`
}
type CmsNodeRes struct {
	List []*model.NodeInfo `json:"list"`
}
