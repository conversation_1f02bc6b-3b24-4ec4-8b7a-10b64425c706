package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	model "gtcms/internal/model/admin"
)

type DomainListReq struct {
	g.Meta            `path:"/domain/list" tags:"域名管理" method:"post" summary:"域名列表"`
	Name              *[]string `json:"name" dc:"域名"`
	BelongUserId      *uint     `json:"belongUserId"         dc:"所属用户id"`
	Used              *int      `json:"used"         dc:"使用情况(1:未建站 2:已建站)"`
	Status            *int      `json:"status"       dc:"状态(1:正常 2:过户中)"`
	Suffix            *string   `json:"suffix"       dc:"域名后缀"`
	StartTime         *int64    `json:"startTime"    dc:"过期开始时间"`
	EndTime           *int64    `json:"endTime"    dc:"过期结束时间"`
	Language          *string   `json:"language"       dc:"语言"`
	RegisterTimeStart *int64    `json:"registerTimeStart"    dc:"注册时间开始"`
	RegisterTimeEnd   *int64    `json:"registerTimeEnd"    dc:"注册时间结束"`
	ServerId          *int      `json:"serverId"         dc:"服务器id"`
	ListReq
}

type DomainListRes struct {
	ListRes
	List []DomainItem `json:"list" dc:"域名列表"`
}

type DomainItem struct {
	BelongUserName string `json:"belongUserName" dc:"所属用户名称"`
	RegistrarName  string `json:"registrarName" dc:"注册商名称"`
}

type DomainAddReq struct {
	g.Meta       `path:"/domain/add" tags:"域名管理" method:"post" summary:"添加域名"`
	BelongUserId uint    `v:"required" json:"belongUserId"         dc:"所属用户id"`
	Name         string  `v:"required|domain"    json:"name"       dc:"域名"`
	RegisterTime int64   `v:"required" json:"registerTime" dc:"注册时间"`
	ExpireTime   int64   `v:"required" json:"expireTime"   dc:"到期时间"`
	Registrar    uint    `json:"registrar"    dc:"注册商"`
	Account      string  `json:"account"      dc:"平台账号"`
	Status       int     `v:"required" json:"status"       dc:"状态(1:正常 2:过户中)" d:"1"`
	Used         int     `v:"required|in:1,2" json:"used"         dc:"使用情况(1:未建站 2:已建站)" d:"2"`
	Remark       string  `json:"remark"     dc:"备注"`
	Price        float64 `json:"price"     dc:"价格"`
	Language     string  `v:"required" json:"language"       dc:"语言"`
	ServerId     int     `json:"serverId"         dc:"服务器id"`
}

type DomainAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type DomainEditReq struct {
	g.Meta       `path:"/domain/edit" tags:"域名管理" method:"post" summary:"编辑域名"`
	Id           uint     `v:"required" json:"id"         dc:"id"`
	BelongUserId *uint    `v:"required" json:"belongUserId"         dc:"所属用户id"`
	Name         *string  `v:"domain"    json:"name"       dc:"域名"`
	RegisterTime *int64   `json:"registerTime" dc:"注册时间"`
	ExpireTime   *int64   `json:"expireTime"   dc:"到期时间"`
	Registrar    *string  `json:"registrar"    dc:"注册商"`
	Account      *string  `json:"account"      dc:"平台账号"`
	Status       *int     `json:"status"       dc:"状态(1:正常 2:过户中)"`
	Used         *int     `json:"used"         dc:"使用情况(1:未建站 2:已建站)"`
	Remark       *string  `json:"remark"     dc:"备注"`
	Price        *float64 `json:"price"     dc:"价格"`
	Language     *string  `json:"language"       dc:"语言"`
	ServerId     *int     `json:"serverId"         dc:"服务器id"`
}

type DomainDeleteReq struct {
	g.Meta `path:"/domain/delete" tags:"域名管理" method:"post" summary:"删除域名"`
	Id     uint   `json:"id"         dc:"id"`
	Ids    []uint `json:"ids"         dc:"ids"`
}

type DomainOneReq struct {
	g.Meta `path:"/domain/one" method:"post" tags:"域名管理" summary:"域名->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type DomainOneRes struct {
	DomainItem
}

type DomainOptionsReq struct {
	g.Meta   `path:"/domain/options" method:"post" tags:"域名管理" summary:"域名->选项（供选择器）"`
	Language *string `json:"language"       dc:"语言"`
}

type DomainOptions2Req struct {
	g.Meta `path:"/domain/options2" method:"post" tags:"域名管理" summary:"域名->选项（供蜘蛛日志使用）"`
}

type DomainOptionsRes struct {
	Options []model.SelectOption `json:"options" dc:"选择器选项数组"`
}

type DomainImportReq struct {
	g.Meta `path:"/domain/import" method:"post" mime:"multipart/form-data" tags:"域名管理" summary:"批量新建"`
	File   *ghttp.UploadFile `json:"file" type:"file" dc:"选择导入文件"`
}

type DomainExportReq struct {
	g.Meta       `path:"/domain/export" method:"post" tags:"域名管理" summary:"导出excel"`
	Ids          *[]uint `json:"ids"`
	BelongUserId *uint   `json:"belongUserId"         dc:"所属用户id"`
	Used         *int    `json:"used"         dc:"使用情况(1:未建站 2:已建站)"`
	Status       *int    `json:"status"       dc:"状态(1:正常 2:过户中)"`
	Suffix       *string `json:"suffix"       dc:"域名后缀"`
	StartTime    *int64  `json:"startTime"    dc:"过期开始时间"`
	EndTime      *int64  `json:"endTime"    dc:"过期结束时间"`
	Language     *string `json:"language"       dc:"语言"`
}

type DomainEditBatchReq struct {
	g.Meta       `path:"/domain/editBatch" method:"post" tags:"域名管理" summary:"批量更新"`
	Ids          []uint   `v:"required" json:"ids"         dc:"ids"`
	RegisterTime *int64   `json:"registerTime" dc:"注册时间"`
	ExpireTime   *int64   `json:"expireTime"   dc:"到期时间"`
	Registrar    *string  `json:"registrar"    dc:"注册商"`
	Account      *string  `json:"account"      dc:"平台账号"`
	Status       *int     `json:"status"       dc:"状态(1:正常 2:过户中)"`
	Used         *int     `json:"used"         dc:"使用情况(1:未建站 2:已建站)"`
	Price        *float64 `json:"price"     dc:"价格"`
	BelongUserId *uint    `json:"belongUserId"         dc:"所属用户id"`
	Language     *string  `json:"language"       dc:"语言"`
	ServerId     *int     `json:"serverId"         dc:"服务器id"`
}

type DomainExportTxtReq struct {
	g.Meta       `path:"/domain/exportTxt" method:"post" tags:"站点管理" summary:"导出txt"`
	BelongUserId *uint   `json:"belongUserId"         dc:"所属用户id"`
	Used         *int    `json:"used"         dc:"使用情况(1:未建站 2:已建站)"`
	Status       *int    `json:"status"       dc:"状态(1:正常 2:过户中)"`
	Suffix       *string `json:"suffix"       dc:"域名后缀"`
	StartTime    *int64  `json:"startTime"    dc:"过期开始时间"`
	EndTime      *int64  `json:"endTime"    dc:"过期结束时间"`
	Language     *string `json:"language"       dc:"语言"`
}

// 续期
type DomainRenewalReq struct {
	g.Meta `path:"/domain/renewal" method:"post" tags:"域名管理" summary:"续期"`
	Ids    []uint `json:"ids"         dc:"ids"`
}
