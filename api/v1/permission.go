package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type PermissionAddReq struct {
	g.<PERSON>a         `path:"/permission/add" method:"post" tags:"权限管理" summary:"新增权限"`
	PId            uint    `v:"required" json:"pId" dc:"父权限id，顶级为0"`
	Name           string  `v:"required" json:"name" dc:"菜单编号（权限编号）"`
	Label          string  `v:"required" json:"label" dc:"显示名称"`
	OrderBy        int     `json:"orderBy" dc:"排序，默认0"`
	SensitiveWords *string `json:"sensitiveWords" dc:"需要脱敏的字段"`
	Interface      *string `json:"interface" dc:"页面需要调用的接口"`
	Status         uint    `v:"required" json:"status" dc:"状态:1.启用;2.禁用"`
}
type PermissionAddRes struct {
}

type PermissionEditReq struct {
	g.<PERSON>a    `path:"/permission/edit" method:"post" tags:"权限管理" summary:"修改权限"`
	Id        uint    `v:"required|min:0" json:"id"`
	Name      *string `json:"name" dc:"菜单编号（权限编号）"`
	Label     *string `json:"label" dc:"显示名称"`
	OrderBy   *int    `json:"orderBy" dc:"排序"`
	Interface *string `json:"interface" dc:"页面需要调用的接口"`
	Status    *uint   `v:"required" json:"status" dc:"状态:1.启用;2.禁用"`
}
type PermissionEditRes struct {
}

type PermissionDeleteReq struct {
	g.Meta `path:"/permission/delete" method:"post" tags:"权限管理" summary:"删除权限"`
	Id     uint `v:"required" json:"id" dc:"权限id"`
}
type PermissionDeleteRes struct {
}

type PermissionListReq struct {
	g.Meta `path:"/permission/list" method:"post" tags:"权限管理" summary:"获取权限列表"`
	ListReq
}
type PermissionListRes struct {
	ListRes
	//List []*entity.Permission `json:"list" dc:"权限列表"`
}

type PermissionOneReq struct {
	g.Meta `path:"/permission/one" method:"post" tags:"权限管理" summary:"获取权限详情"`
	Id     uint `v:"required" json:"id" dc:"权限id"`
}
type PermissionOneRes struct {
	//*entity.Permission
}

type PermissionSubsReq struct {
	g.Meta `path:"/permission/subs" method:"post" tags:"权限管理" summary:"获取子权限"`
	PId    uint `v:"required" json:"pId" dc:"权限父id"`
}

//type PermissionSubsRes []entity.Permission

type PermissionTreeReq struct {
	g.Meta `path:"/permission/tree" method:"post" tags:"权限管理" summary:"获取子权限树"`
	PId    uint `v:"required" json:"pId" dc:"权限父id"`
}
type PermissionItem struct {
	//entity.Permission
	Subs []*PermissionItem `json:"subs" dc:"子权限"`
}
type PermissionTreeRes []PermissionItem
