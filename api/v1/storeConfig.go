package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/logic/file/libfile"
	model "gtcms/internal/model/admin"
)

type StoreConfigTypesReq struct {
	g.Meta `path:"/storeBaseConfig/types" method:"post" tags:"存储配置" summary:"存储类型->选项（供选择器）"`
}

type StoreConfigTypesRes struct {
	Options []model.SelectOption `json:"options" dc:"选择器选项数组"`
}

type StoreBaseConfigDetailReq struct {
	g.Meta `path:"/storeBaseConfig/detail" method:"post" tags:"存储配置" summary:"基础配置-详情"`
}
type StoreBaseConfigDetailRes struct {
	StoreBaseConfigVo
}

type StoreBaseConfigEditReq struct {
	g.Meta `path:"/storeBaseConfig/edit" method:"post" tags:"存储配置" summary:"基础配置-编辑"`
	Type   string `json:"type"             dc:"存储类型(具体请参照存储类型->选项)" v:"required|in:aws"`
	/*ThumbnailLargeWidth   int    `json:"thumbnailLargeWidth"   dc:"缩略大图宽"`
	ThumbnailLargeHeight  int    `json:"thumbnailLargeHeight"  dc:"缩略大图高"`
	ThumbnailMediumWidth  int    `json:"thumbnailMediumWidth"  dc:"缩略中图宽"`
	ThumbnailMediumHeight int    `json:"thumbnailMediumHeight" dc:"缩略中图高"`
	ThumbnailSmallWidth   int    `json:"thumbnailSmallWidth"   dc:"缩略小图宽"`
	ThumbnailSmallHeight  int    `json:"thumbnailSmallHeight"  dc:"缩略小图高"`
	IsOpenWatermark       int    `json:"isOpenWatermark"       dc:"是否开启水印： 1:开 2:关" v:"required|in:1,2"`
	WatermarkType         int    `json:"watermarkType"         dc:"水印类型： 1:图片 2:文字" v:"required|in:1,2"`
	WatermarkContent      string `json:"watermarkContent"      dc:"水印图片或水印文字"`
	WatermarkLocation     int    `json:"watermarkLocation"     dc:"水印位置"`
	WatermarkOpacity      int    `json:"watermarkOpacity"      dc:"水印透明度"`
	WatermarkRotation     int    `json:"watermarkRotation"     dc:"水印倾斜度"`
	WatermarkHorizontal   int    `json:"watermarkHorizontal"   dc:"水印横坐标偏移量"`
	WatermarkVertical     int    `json:"watermarkVertical"     dc:"水印纵坐标偏移量"`*/
}

type StoreBaseConfigVo struct {
	Type string `json:"type"                  dc:"存储类型(具体请参照存储类型->选项)"`
	/*ThumbnailLargeWidth   int    `json:"thumbnailLargeWidth"   dc:"缩略大图宽"`
	ThumbnailLargeHeight  int    `json:"thumbnailLargeHeight"  dc:"缩略大图高"`
	ThumbnailMediumWidth  int    `json:"thumbnailMediumWidth"  dc:"缩略中图宽"`
	ThumbnailMediumHeight int    `json:"thumbnailMediumHeight" dc:"缩略中图高"`
	ThumbnailSmallWidth   int    `json:"thumbnailSmallWidth"   dc:"缩略小图宽"`
	ThumbnailSmallHeight  int    `json:"thumbnailSmallHeight"  dc:"缩略小图高"`
	IsOpenWatermark       int    `json:"isOpenWatermark"       dc:"是否开启水印： 1:开 2:关"`
	WatermarkType         int    `json:"watermarkType"         dc:"水印类型： 1:图片 2:文字"`
	WatermarkContent      string `json:"watermarkContent"      dc:"水印图片或水印文字"`
	WatermarkLocation     int    `json:"watermarkLocation"     dc:"水印位置"`
	WatermarkOpacity      int    `json:"watermarkOpacity"      dc:"水印透明度"`
	WatermarkRotation     int    `json:"watermarkRotation"     dc:"水印倾斜度"`
	WatermarkHorizontal   int    `json:"watermarkHorizontal"   dc:"水印横坐标偏移量"`
	WatermarkVertical     int    `json:"watermarkVertical"     dc:"水印纵坐标偏移量"`*/
}

type StoreCloudConfigDetailReq struct {
	g.Meta `path:"/storeCloudConfig/detail" method:"post" tags:"存储配置" summary:"云配置-详情"`
	Type   string `json:"type"             dc:"存储类型(具体请参照存储类型->选项)" v:"required|in:aws"`
}

type StoreCloudConfigDetailRes struct {
	Aws libfile.AwsS3Config `json:"aws,omitempty"`
}

type StoreCloudConfigEditReq struct {
	g.Meta `path:"/storeCloudConfig/edit" method:"post" tags:"存储配置" summary:"云配置-编辑"`
	Type   string              `json:"type"             dc:"存储类型(具体请参照存储类型->选项)" v:"required|in:aws"`
	Aws    libfile.AwsS3Config `json:"aws,omitempty"`
}
