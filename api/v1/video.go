package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type Video struct {
	Author        string           `json:"author" dc:"创建人"`
	CategoryId    uint             `v:"required" json:"category_id" dc:"分类id"`
	PublishTime   string           `json:"publish_time" dc:"发布时间，日期格式，如：2025-08-04 00:00:00"`
	CoverImgs     string           `v:"required" json:"cover_imgs"      dc:"封面图"`
	VideoUrl      string           `v:"required" json:"video_url"      dc:"视频链接"`
	VideoTitleArr []VideoTitleItem `v:"required" json:"video_title_arr" dc:"视频标题数组"`
	IsRecommend   uint             `v:"required|between:0,1" json:"is_recommend"      dc:"1:推荐 0:不推荐"`
	IsDraft       uint             `v:"required|between:0,1" json:"is_draft"      dc:"是否保存草稿 1:草稿 0:否"`
	IsPublish     uint             `v:"required|between:0,1" json:"is_publish"      dc:"是否发布 1:是 0:否"`
}

type VideoTitleItem struct {
	LanguageType int    `v:"required|between:0,2" json:"language_type" dc:"语言类型 0：中文 1：英文 2：印尼语"`
	VideoTitle   string `json:"video_title" dc:"视频标题"`
	Description  string `v:"required" json:"description" dc:"视频简介"`
}

type VideoAddReq struct {
	g.Meta `path:"/video/add" method:"post" tags:"视频管理" summary:"新增"`
	Video
}

type VideoEditReq struct {
	g.Meta `path:"/video/edit" method:"post" tags:"视频管理" summary:"编辑"`
	Id     uint `v:"required" json:"id" dc:"视频id"`
	Video
}

type VideoInfoReq struct {
	g.Meta `path:"/video/info" method:"get,post" tags:"视频管理" summary:"详情"`
	Id     uint `v:"required" json:"id" dc:"视频id"`
}

type VideoInfoRes struct {
	Video
}

type VideoListReq struct {
	g.Meta `path:"/video/list" method:"get,post" tags:"视频管理" summary:"列表"`
	ListReq
	CategoryId      int    `json:"category_id" d:"-1" dc:"分类id 默认-1：全部"`
	VideoStatus     int    `json:"video_status" d:"-1" dc:"状态 1:已发布 2:已下线  默认-1：全部"`
	IsRecommend     int    `json:"is_recommend" d:"-1" dc:"是否推荐 1：是 0：否 默认-1：全部"`
	IsDraft         int    `json:"is_draft" d:"0" dc:"是否草稿 1:是 0:否 默认0：否"`
	VideoName       string `json:"video_name" dc:"视频名称"`
	CreateTimeBegin string `json:"create_time_begin" dc:"创建开始时间 如：2025-08-04 00:00:00"`
	CreateTimeEnd   string `json:"create_time_end" dc:"创建结束时间 如：2025-08-04 00:00:00"`
	UpdateTimeBegin string `json:"update_time_begin" dc:"更新开始时间 如：2025-08-04 00:00:00"`
	UpdateTimeEnd   string `json:"update_time_end" dc:"更新结束时间 如：2025-08-04 00:00:00"`
}

type VideoListRes struct {
	List []VideoListItem `json:"list"`
	ListRes
}

type VideoListItem struct {
	Id              uint              `json:"id" dc:"文章id"`
	CategoryId      uint              `json:"category_id" dc:"分类id"`
	CategoryName    string            `json:"category_name" dc:"分类名称"`
	VideoName       string            `json:"video_name" dc:"视频标题"`
	Author          string            `json:"author" dc:"创建人"`
	CreateTime      string            `json:"create_time" dc:"创建时间"`
	PublishTime     string            `json:"publish_time" dc:"发布时间"`
	VideoStatus     int               `json:"video_status" dc:"视频状态 1:已发布 2:已下线"`
	VideoStatusText string            `json:"video_status_text" dc:"视频状态文案"`
	IsRecommend     int               `json:"is_recommend" dc:"是否推荐 1：是 0：否"`
	IsRecommendText string            `json:"is_recommend_text" dc:"是否推荐文案"`
	LanguageArr     []LanguageArrItem `json:"language_arr" dc:"语言列表"`
	CollectNum      int               `json:"collect_num" dc:"收藏数"`
	ShareNum        int               `json:"share_num" dc:"分享数"`
	ViewNum         int               `json:"view_num" dc:"浏览数"`
	SerialNum       int               `json:"serial_num" dc:"序号"`
}

type VideoDeleteReq struct {
	g.Meta `path:"/video/delete" method:"post" tags:"文章管理" summary:"删除（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type VideoSetRecommendReq struct {
	g.Meta `path:"/video/setRecommend" method:"post" tags:"文章管理" summary:"设置推荐（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type VideoSetNotRecommendReq struct {
	g.Meta `path:"/video/setNotRecommend" method:"post" tags:"文章管理" summary:"设置不推荐（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type VideoSetOfflineReq struct {
	g.Meta `path:"/video/setOffline" method:"post" tags:"文章管理" summary:"下线（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type VideoSetOnlineReq struct {
	g.Meta `path:"/video/setOnline" method:"post" tags:"文章管理" summary:"上线（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type VideoDraftReq struct {
	g.Meta `path:"/video/draft" method:"get" tags:"文章管理" summary:"草稿箱"`
	ListReq
	VideoName       string `json:"video_name" dc:"视频标题"`
	CreateTimeBegin string `json:"create_time_begin" dc:"创建开始时间 如：2025-08-04 00:00:00"`
	CreateTimeEnd   string `json:"create_time_end" dc:"创建结束时间 如：2025-08-04 00:00:00"`
	UpdateTimeBegin string `json:"update_time_begin" dc:"更新开始时间 如：2025-08-04 00:00:00"`
	UpdateTimeEnd   string `json:"update_time_end" dc:"更新结束时间 如：2025-08-04 00:00:00"`
}

type VideoDraftItem struct {
	Id              uint              `json:"id" dc:"文章id"`
	VideoName       string            `json:"video_name" dc:"视频标题"`
	CreateTime      string            `json:"create_time" dc:"创建时间"`
	UpdateTime      string            `json:"update_time" dc:"修改时间"`
	VideoStatus     int               `json:"video_status" dc:"视频状态 1:已发布 2:已下线"`
	VideoStatusText string            `json:"video_status_text" dc:"视频状态文案"`
	LanguageArr     []LanguageArrItem `json:"language_arr" dc:"语言列表"`
}

type VideoDraftRes struct {
	List []VideoDraftItem `json:"list"`
	ListRes
}
