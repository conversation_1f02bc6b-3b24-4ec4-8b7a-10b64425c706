package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	model "gtcms/internal/model/admin"
)

type TdkTmplListReq struct {
	g.Meta   `path:"/tdkTmpl/list" tags:"tdk模版管理" method:"post" summary:"tdk模版列表"`
	Belong   int     `v:"required" json:"belong" dc:"所属功能(0:基础库 1:组 2:站点)"`
	BelongId *uint   `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id"`
	Status   *int    `json:"status"       dc:"状态(1:启用 2:禁用)"`
	Name     *string `json:"name"       dc:"名称"`
	Language string  `json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
	ListReq
}

type TdkTmplTypeListReq struct {
	g.Meta `path:"/tdkTmplType/list" tags:"tdk模版类型管理" method:"post" summary:"tdk模版类型列表"`
	ListReq
}

type TdkTmplListRes struct {
	ListRes
	List []TdkTmplItem `json:"list" dc:"tdk模版列表"`
}

type TdkTmplItem struct {
	Id               uint            `json:"id"           dc:""`
	Name             string          `json:"name" dc:"名称"`
	Belong           int             `json:"belong" dc:"所属功能(0:基础库 1:组 2:站点)"`
	BelongId         uint            `json:"belongId" dc:"所属分组/站点id"`
	TRemark          string          `json:"tRemark"       dc:"标题模版说明"`
	TConfig          []TdkTmplConfig `json:"tConfig"       dc:"标题模版内容"`
	KwRemark         string          `json:"kwRemark"      dc:"关键词模版说明"`
	KwConfig         []TdkTmplConfig `json:"kwConfig"      dc:"关键词模版内容"`
	DescRemark       string          `json:"descRemark"    dc:"描述模版说明"`
	DescConfig       []TdkTmplConfig `json:"descConfig"    dc:"描述模版内容"`
	Status           int             `json:"status"        dc:"状态(1:启用 2:禁用)"`
	CreateTime       int64           `json:"createTime"    dc:"创建时间"`
	SelfId           uint            `json:"selfId"       dc:"自身id(如果belong非0,那这个指向的是id)"`
	ChooseStatus     int             `json:"chooseStatus" dc:"选中状态(1:选中 2:未选中)"`
	Creater          uint            `json:"creater"      dc:"创建者"`
	IsDefault        int             `json:"isDefault"     dc:"是否默认(1:是 2:否)"`
	UpdateTime       int64           `json:"updateTime"    dc:"更新时间"`
	Demo             string          `json:"demo"          dc:"演示例子"`
	Language         string          `json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
	OtherLanguageIds string          `json:"otherLanguageIds" dc:"其他语言id，用,分割"`
}

type TdkTmplAddReq struct {
	g.Meta           `path:"/tdkTmpl/add" tags:"tdk模版管理" method:"post" summary:"添加tdk模版"`
	Name             string          `v:"required" json:"name" dc:"名称"`
	Belong           int             `v:"required" json:"belong" dc:"所属功能(0:基础库 1:组 2:站点)"`
	BelongId         *uint           `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
	TRemark          string          `json:"tRemark"       dc:"标题模版说明"`
	TConfig          []TdkTmplConfig `json:"tConfig"       dc:"标题模版内容" d:"[]"`
	KwRemark         string          `json:"kwRemark"      dc:"关键词模版说明"`
	KwConfig         []TdkTmplConfig `json:"kwConfig"      dc:"关键词模版内容" d:"[]"`
	DescRemark       string          `json:"descRemark"    dc:"描述模版说明"`
	DescConfig       []TdkTmplConfig `json:"descConfig"    dc:"描述模版内容" d:"[]"`
	Status           int             `json:"status"        dc:"状态(1:启用 2:禁用)"`
	IsDefault        int             `json:"isDefault"     dc:"是否默认(1:是 2:否)"`
	Demo             string          `json:"demo"          dc:"演示例子"`
	Language         string          `v:"required" json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
	OtherLanguageIds string          `json:"otherLanguageIds" dc:"其他语言id，用,分割"`
}

type TdkTmplConfig struct {
	Category int                 `json:"category" dc:"页面类型(1:赛程栏目 2:录像栏目 3:新闻栏目 4:标签 5:赛程详情页 6:录像详情页 7:新闻详情页 8:球队 9:球员 10:集锦栏目 12:集锦详情页 13:积分榜 14:射手榜 15:专题 16:资料库 17:资料库详情 18:电视频道 19:电视频道详情 20:赛果 21:赛果详情)"`
	Body     []TdkTmplConfigBody `json:"body" dc:"内容"`
}

type TdkTmplConfigBody struct {
	Content string `json:"content" dc:"内容"`
	Default int    `json:"default" dc:"是否默认(1:是 2:否)"`
}

type TdkTmplAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type TdkTmplEditReq struct {
	g.Meta           `path:"/tdkTmpl/edit" tags:"tdk模版管理" method:"post" summary:"编辑tdk模版"`
	Id               uint             `v:"required" json:"id"         dc:"id"`
	Name             *string          `json:"name" dc:"名称"`
	TConfig          *[]TdkTmplConfig `json:"tConfig"       dc:"标题模版内容"`
	KwRemark         *string          `json:"kwRemark"      dc:"关键词模版说明"`
	KwConfig         *[]TdkTmplConfig `json:"kwConfig"      dc:"关键词模版内容"`
	DescRemark       *string          `json:"descRemark"    dc:"描述模版说明"`
	DescConfig       *[]TdkTmplConfig `json:"descConfig"    dc:"描述模版内容"`
	Status           *int             `json:"status"        dc:"状态(1:启用 2:禁用)"`
	IsDefault        *int             `json:"isDefault"     dc:"是否默认(1:是 2:否)"`
	Demo             *string          `json:"demo"          dc:"演示例子"`
	Language         *string          `json:"language"      dc:"语言(cn:中文 en:英文 id:印尼)" v:"in:id,cn,en"`
	OtherLanguageIds *string          `json:"otherLanguageIds" dc:"其他语言id，用,分割"`
}

type TdkTmplDeleteReq struct {
	g.Meta `path:"/tdkTmpl/delete" tags:"tdk模版管理" method:"post" summary:"删除tdk模版"`
	Id     uint `v:"required" json:"id"         dc:"id"`
}

type TdkTmplOneReq struct {
	g.Meta   `path:"/tdkTmpl/one" method:"post" tags:"tdk模版管理" summary:"tdk模版->获取"`
	Belong   uint `v:"required" json:"belong" dc:"所属：1分组 2站点"`
	BelongId uint `v:"required" json:"belongGroupId" dc:"所属分组/站点id"`
	Type     int  `v:"required" json:"type" dc:"类型(1:标题 2:关键词 3:描述)"`
	Category int  `v:"required" json:"category" dc:"页面类型(1:赛程栏目 2:录像栏目 3:新闻栏目 4:标签 5:赛程详情页 6:录像详情页 7:新闻详情页 8:球队 9:球员)"`
}
type TdkTmplOneRes struct {
	Content string `json:"content" dc:"内容"`
}

type TdkTmplFastFillReq struct {
	g.Meta   `path:"/tdkTmpl/fastFill" method:"post" tags:"tdk模版管理" summary:"使用tdk模板快速填充内容"`
	Category int  `v:"required" json:"category" dc:"页面类型(1:赛程栏目 2:录像栏目 3:新闻栏目 4:标签 5:赛程详情页 6:录像详情页 7:新闻详情页 8:球队 9:球员)"`
	SiteId   uint `json:"siteId" dc:"站点id"`
}
type TdkTmplFastFillRes struct {
	Title    string `json:"title" dc:"title数据"`
	Desc     string `json:"desc" dc:"desc数据"`
	Keywords string `json:"keywords" dc:"keywords数据"`
}

type TdkTmplGroupSiteAllListReq struct {
	g.Meta   `path:"/tdkTmpl/groupSiteAllList" tags:"tdk模版管理" method:"post" summary:"分组/站点Tdk库(全部)"`
	Belong   int   `v:"required" json:"belong" dc:"所属功能(1:组 2:站点)"`
	BelongId *uint `v:"required" json:"belongId" dc:"所属功能id"`
	ListReq
}

type TdkTmplGroupSiteAllListRes struct {
	ListRes
	List []TdkTmplItem `json:"list" dc:"列表"`
}

type TdkTmplGroupSiteAddReq struct {
	g.Meta `path:"/tdkTmpl/groupSiteAdd" tags:"tdk模版管理" method:"post" summary:"分组/站点Tdk添加"`
	model.GroupSiteAddReqIn
}

type TdkTmplImportReq struct {
	g.Meta `path:"/tdkTmpl/import" method:"post" mime:"multipart/form-data" tags:"tdk模版管理" summary:"批量新建"`
	File   *ghttp.UploadFile `json:"file" type:"file" dc:"选择导入文件"`
}

type TdkTmplExportReq struct {
	g.Meta   `path:"/tdkTmpl/export" method:"post" tags:"tdk模版管理" summary:"导出"`
	Name     *string `json:"name"       dc:"名称"`
	Language string  `json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
	Ids      []uint  `json:"ids"        dc:"ids"`
}
