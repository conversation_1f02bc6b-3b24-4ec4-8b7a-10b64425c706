package v1

import "github.com/gogf/gf/v2/frame/g"

// 列表请求参数
type FeedbackListReq struct {
	g.Meta `path:"/feedback/list" method:"get" tags:"问题反馈" summary:"反馈列表"`
	ListReq
	Keyword      string `v:"" json:"keyword" dc:"搜索关键字"`
	StartTime    int64  `v:"" json:"startTime" dc:"开始时间"`
	EndTime      int64  `v:"" json:"endTime" dc:"结束时间"`
	FeedbackType int    `v:"" json:"feedbackType" dc:"反馈类型"`
	UserId       int64  `v:"" json:"userId" dc:"用户id"`
}

// 列表返回参数
type FeedbackListRes struct {
	List []*FeedbackListItem `json:"list" dc:"列表"`
	ListRes
}

// 列表项
type FeedbackListItem struct {
	Id              int    `json:"id"              orm:"id"               description:""`
	FeedbackType    int    `json:"feedbackType"    orm:"feedback_type"    description:"反馈类型  1 系统故障 2 功能建议"`
	UserId          uint   `json:"userId"          orm:"user_id"          description:"用户id"`
	UserAccount     string `json:"userAccount"     orm:"user_account"     description:"用户账号"`
	Images          string `json:"images"          orm:"images"           description:"反馈图片 json字符串"`
	Desc            string `json:"desc"            orm:"desc"             description:"反馈内容"`
	FeedbackTime    int64  `json:"feedbackTime"    orm:"feedback_time"    description:"反馈时间ms"`
	FeedbackStatus  int    `json:"feedbackStatus"  orm:"feedback_status"  description:"反馈状态 1 未处理 2已处理"`
	FeedbackResult  int    `json:"feedbackResult"  orm:"feedback_result"  description:"反馈结果 1 无效反馈 2有效反馈"`
	CompleteTime    int64  `json:"completeTime"    orm:"complete_time"    description:"处理时间ms"`
	CompleteRemark  string `json:"completeRemark"  orm:"complete_remark"  description:"处理备注"`
	CompleteAccount string `json:"completeAccount" orm:"complete_account" description:"处理人"`
	CreateTime      int64  `json:"createTime"      orm:"create_time"      description:"创建时间"`
	UpdateTime      int64  `json:"updateTime"      orm:"update_time"      description:"更新时间"`
	DeleteTime      int64  `json:"deleteTime"      orm:"delete_time"      description:"删除时间"`
}

// 详情请求参数
type FeedbackOneReq struct {
	g.Meta `path:"/feedback/one" method:"get" tags:"问题反馈" summary:"反馈详情"`
	Id     int `v:"required" json:"id" dc:"反馈id"`
}

// 详情返回参数
type FeedbackOneRes struct {
	FeedbackListItem
}

// 删除请求参数
type FeedbackDeleteReq struct {
	g.Meta `path:"/feedback/delete" method:"delete" tags:"问题反馈" summary:"删除反馈"`
	Id     int `v:"required" json:"id" dc:"反馈id"`
}

// 删除返回参数
type FeedbackDeleteRes struct {
}

// 处理反馈
type FeedbackCompleteReq struct {
	g.Meta         `path:"/feedback/complete" method:"post" tags:"问题反馈" summary:"处理反馈"`
	Id             int    `v:"required" json:"id" dc:"反馈id"`
	FeedbackResult int    `json:"feedbackResult"    dc:"反馈结果 1 无效反馈 2有效反馈"`
	CompleteRemark string `json:"completeRemark"    dc:"处理备注"`
}

type FeedbackCompleteRes struct {
}
