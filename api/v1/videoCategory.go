package v1

import "github.com/gogf/gf/v2/frame/g"

type VideoCategory struct {
	NameArr []VideoCategoryNameArrItem `v:"required" json:"name_arr" dc:"标题数组"`
	Remark  string                     `json:"remark" dc:"备注"`
}

type VideoCategoryNameArrItem struct {
	LanguageType int    `v:"required|between:0,2" json:"language_type" dc:"语言类型 0：中文 1：英文 2：印尼语"`
	CategoryName string `v:"required" json:"category_name" dc:"分类名称"`
}

type VideoCategoryAddReq struct {
	g.Meta `path:"/videoCategory/add" method:"post" tags:"视频分类" summary:"新增"`
	VideoCategory
}

type VideoCategoryEditReq struct {
	g.Meta `path:"/videoCategory/edit" method:"post" tags:"视频分类" summary:"修改"`
	Id     uint `v:"required" json:"id" dc:"分类id"`
	VideoCategory
}

type VideoCategoryListReq struct {
	g.Meta `path:"/videoCategory/list" method:"get,post" tags:"视频分类" summary:"列表"`
	ListReq
	CategoryName string `json:"category_name" dc:"分类名称"`
}

type VideoCategoryListRes struct {
	List []VideoCategoryListItem `json:"list"`
	ListRes
}

type VideoCategoryListItem struct {
	Id           uint              `json:"id" dc:"分类ID"`
	CategoryName string            `json:"category_name" dc:"分类名称"`
	LanguageArr  []LanguageArrItem `json:"language_arr" dc:"语言列表"`
	VideoNum     uint              `json:"video_num" dc:"视频数量"`
}

type VideoCategoryInfoReq struct {
	g.Meta `path:"/videoCategory/info" method:"get,post" tags:"视频分类" summary:"详情"`
	Id     uint `v:"required" json:"id" dc:"分类id"`
}

type VideoCategoryInfoRes struct {
	Id      uint                       `json:"id" dc:"分类id"`
	NameArr []VideoCategoryNameArrItem `json:"name_arr" dc:"标题数组"`
	Remark  string                     `json:"remark" dc:"备注"`
}

type VideoCategoryDeleteReq struct {
	g.Meta `path:"/videoCategory/delete" method:"post" tags:"视频分类" summary:"删除（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"分类id数组"`
}
