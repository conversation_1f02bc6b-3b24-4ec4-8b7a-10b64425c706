package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type AddResolutionReq struct {
	g.Meta `path:"/add/solution" method:"post"  tags:"新增解析" summary:"新增解析"`
	Ym     string `json:"ym" dc:"ym"`
	Jlz    string `json:"jlz" dc:"jlz"` //记录值
}
type AddResolutionRes struct {
	Jxid uint `json:"jxid" dc:"jxid"` //解析id
}

type DeleteResolutionReq struct {
	g.Meta `path:"/delete/solution" method:"post" tags:"删除解析" summary:"删除解析"`
	Ym     string `json:"ym" dc:"ym"`
}

type GetDomainsReq struct {
	g.Meta `path:"/domains" tags:"域名列表" method:"post" summary:"域名列表"`
}
type GetDomainsRes struct {
	List []string `json:"list" dc:"域名列表"`
}

type FileUploadReq struct {
	g.Meta `path:"/file" method:"post" mime:"multipart/form-data" tags:"工具" summary:"上传文件"`
	File   *ghttp.UploadFile `json:"file" type:"file" dc:"选择上传文件"`
	//FilePrefix string            `json:"filePrefix" d:"pub" dc:"目录前缀: pub 公开| private私有 | in 内部 | tmp 临时"`                                                                                 // 默认值 pub
	//FileModule string            `json:"fileModule" d:"default" dc:"上传模块: avatar 默认头像 |avatarCustom 客户设置头像| feedback 意见反馈  | sys 系统内置 | phoneAreaCode 区号|file 文件|userAppSettings 推送提示-声音"` // 默认值 default
}

type FileUploadRes struct {
	Name       string `json:"name" dc:"图片的名称"`
	Key        string `json:"key" dc:"对象KEY"`
	BackendUrl string `json:"url" dc:"图片的后台访问地址"`
}

type MultiUploadReq struct { // 批量上传 // multiple
	g.Meta `path:"/multi_upload" method:"post" mime:"multipart/form-data" tags:"工具" summary:"多个上传文件"`
	Files  []*ghttp.UploadFile `json:"files" type:"file" dc:"选择上传文件"`
	//FilePrefix string              `json:"filePrefix" d:"pub" dc:"目录前缀: pub 公开| private私有 | in 内部 | tmp 临时"`                                                                                 // 默认值 pub
	//FileModule string              `json:"fileModule" d:"default" dc:"上传模块: avatar 默认头像 |avatarCustom 客户设置头像| feedback 意见反馈  | sys 系统内置 | phoneAreaCode 区号|file 文件|userAppSettings 推送提示-声音"` // 默认值 default
}

type MultiUploadRes struct {
	List []*FileUploadRes
}

type SeoRankReq struct {
	g.Meta   `path:"/seoRankAdd" method:"post" mime:"multipart/form-data" tags:"排名查询" summary:"排名查询"`
	Url      string            `json:"url" dc:"url"`
	Keywords string            `json:"keywords" dc:"关键词"`
	Type     int               `json:"type" dc:"0-pc, 1-mobile"`
	File     *ghttp.UploadFile `json:"file" type:"file" dc:"选择上传文件"`
}

type SeoRankGetReq struct {
	g.Meta `path:"/seoRankGet" method:"post" mime:"multipart/form-data" tags:"排名查询" summary:"排名查询"`
	Taskid int `json:"taskid" dc:"任务id"`
	Type   int `json:"type" dc:"0-pc, 1-mobile"`
}

type SeoRankRes struct {
	Id int64 `json:"id" dc:"id"`
}

type SeoRankListReq struct {
	g.Meta        `path:"/seoRank/list" tags:"seo排名" method:"post" summary:"排名列表"`
	AddUrl        *string `json:"addUrl" dc:"新增域名"`
	AddKeywords   *string `json:"addKeywords" dc:"新增关键词"`
	QueryUrl      *string `json:"queryUrl" dc:"查询域名"`
	QueryKeywords *string `json:"queryKeywords" dc:"查询关键词"`
	Type          *int    `json:"type" dc:"0-pc, 1-mobile"`
	Uuid          *string `json:"uuid" dc:"唯一id"`

	IsRank  int `json:"isRank" dc:"1-排名查询，2-排名监控"`
	IsToday int `json:"isToday" dc:"1-今天，2-昨天"`
	ListReq
}

type SeoRankListRes struct {
	ListRes
	List []SeoRankItem `json:"list" dc:"列表"`
}

type SeoRankItem struct {
	Area         string `json:"area"  description:"区域"`
	SearchEngine string `json:"searchEngine"  description:"搜索引擎"`
	Rank         string `json:"rank"      description:" 排名"`
	AddTime      string `json:"addTime"      description:"提交时间"`
}

type SeoRankDeleteReq struct {
	g.Meta `path:"/seoRank/delete" tags:"seo排名" method:"post" summary:"删除seo排名/批量删除seo排名"`
	Id     uint   `json:"id"         dc:"id"`
	Ids    []uint `json:"ids"         dc:"ids"`
}

type SeoRankImportReq struct {
	g.Meta `path:"/seoRank/import" method:"post" mime:"multipart/form-data" tags:"seo排名" summary:"批量新建seo排名"`
	File   *ghttp.UploadFile `json:"file" type:"file" dc:"选择导入文件"`
}

type SeoRankExportReq struct {
	g.Meta        `path:"/seoRank/export" method:"post" tags:"seo排名" summary:"导出seo排名"`
	Ids           []uint `json:"ids"        dc:"id"`
	QueryUrl      string `json:"queryUrl" dc:"查询域名"`
	QueryKeywords string `json:"queryKeywords" dc:"查询关键词"`
	Type          *int   `json:"type" dc:"0-pc, 1-mobile"`
	Uuid          string `json:"uuid" dc:"唯一id"`

	IsRank  int `json:"isRank" dc:"1-排名查询，2-排名监控"`
	IsToday int `json:"isToday" dc:"1-今天，2-昨天"`
}
