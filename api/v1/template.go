package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type TemplateListReq struct {
	g.<PERSON>a   `path:"/template/list" tags:"模版管理" method:"post" summary:"模版列表"`
	Name     *string  `json:"name"       dc:"名称"`
	Type     *int     `json:"type"       dc:"类型(1:自适应 2:pc 3:h5)"`
	Status   *int     `json:"status"     dc:"状态(1:启用 2:禁用)"`
	Language []string `json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
	ListReq
}

type TemplateListRes struct {
	ListRes
	List []TemplateItem `json:"list" dc:"模版列表"`
}

type TemplateItem struct {
	Id               uint     `json:"id"           dc:""`
	Name             string   `json:"name"       dc:"名称"`
	Type             int      `json:"type"       dc:"类型(1:自适应 2:pc 3:h5)"`
	Path             string   `json:"path"       dc:"模版所在文件夹"`
	Status           int      `json:"status"     dc:"状态(1:启用 2:关闭)"`
	Cover            string   `json:"cover"      dc:"封面"`
	Remark           string   `json:"remark"     dc:"备注"`
	CreateTime       int64    `json:"createTime" dc:"创建时间"`
	SiteUsageCount   int      `json:"siteUsageCount" dc:"使用站点数"`
	IsDefault        int      `json:"isDefault"     dc:"是否默认模版(1:是 2:否)"`
	Language         []string `json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
	OtherLanguageIds string   `json:"otherLanguageIds" dc:"其他语言id，用,分割"`
	DesignId         int      `json:"designId" dc:"设计id"`
}

type TemplateAddReq struct {
	g.Meta           `path:"/template/add" tags:"模版管理" method:"post" summary:"添加模版"`
	Name             string   `v:"required" json:"name"       dc:"名称"`
	Type             int      `json:"type"       dc:"类型(1:自适应 2:pc 3:h5)"`
	Path             string   `v:"required" json:"path"  dc:"模版所在文件夹"`
	Status           int      `json:"status"     dc:"状态(1:启用 2:关闭)"`
	Cover            string   `json:"cover"      dc:"封面"`
	Remark           string   `json:"remark"     dc:"备注"`
	IsDefault        int      `json:"isDefault"     dc:"是否默认模版(1:是 2:否)" d:"2"`
	Language         []string `v:"required" json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
	OtherLanguageIds string   `json:"otherLanguageIds" dc:"其他语言id，用,分割"`
	DesignId         int      `json:"designId" dc:"设计id"`
}

type TemplateAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type TemplateEditReq struct {
	g.Meta           `path:"/template/edit" tags:"模版管理" method:"post" summary:"编辑模版"`
	Id               uint     `v:"required" json:"id"         dc:"id"`
	Name             *string  `json:"name"       dc:"名称"`
	Type             *int     `json:"type"       dc:"类型(1:自适应 2:pc 3:h5)"`
	Path             *string  `json:"path"       dc:"模版所在文件夹"`
	Status           *int     `json:"status"     dc:"状态(1:启用 2:关闭)"`
	Cover            *string  `json:"cover"      dc:"封面"`
	Remark           *string  `json:"remark"     dc:"备注"`
	IsDefault        *int     `json:"isDefault"     dc:"是否默认模版(1:是 2:否)"`
	Language         []string `v:"foreach" json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
	OtherLanguageIds *string  `json:"otherLanguageIds" dc:"其他语言id，用,分割"`
	DesignId         *int     `json:"designId" dc:"设计id"`
}

type TemplateDeleteReq struct {
	g.Meta `path:"/template/delete" tags:"模版管理" method:"post" summary:"删除模版"`
	Id     uint `v:"required" json:"id"         dc:"id"`
}

type TemplateOneReq struct {
	g.Meta `path:"/template/one" method:"post" tags:"模版管理" summary:"模版->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type TemplateOneRes struct {
	TemplateItem
}

type TemplateOptionsReq struct {
	g.Meta   `path:"/template/options" method:"post" tags:"模版管理" summary:"模版->选项（供选择器）"`
	Language string `json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
}

type TemplateOptionsRes struct {
}

// =================================================================================
//design

type TemplateDesignListReq struct {
	g.Meta      `path:"/templateDesign/list" tags:"模版设计列表" method:"post" summary:"模版设计列表"`
	SettingName *string `json:"settingName"       dc:"名称"`

	SettingType *int `json:"settingType"       dc:"类型"`
	ListReq
}

type TemplateDesignListRes struct {
	ListRes
	List []TemplateDesignItem `json:"list" dc:"模版设计列表"`
}

type TemplateDesignItem struct {
	Id          uint   `json:"id"           dc:""`
	SettingName string `json:"settingName"       dc:"名称"`
	Image       string `json:"image"       dc:"image"`
	SettingType *int   `json:"settingType"       dc:"类型"`
	Remark      string `json:"remark"     dc:"备注"`
	Config      string `json:"config"     dc:"配置"`
}

type TemplateDesignAddReq struct {
	g.Meta      `path:"/templateDesign/add" tags:"添加模版设计" method:"post" summary:"添加模版设计"`
	SettingName *string `json:"settingName"       dc:"名称"`
	Image       *string `json:"image"       dc:"image"`
	SettingType *int    `json:"settingType"       dc:"类型"`
	Remark      *string `json:"remark"     dc:"备注"`
	Config      *string `json:"config"     dc:"配置"`
}

type TemplateDesignAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type TemplateDesignEditReq struct {
	g.Meta      `path:"/templateDesign/edit" tags:"编辑模版设计" method:"post" summary:"编辑模版设计"`
	Id          uint    `v:"required" json:"id"         dc:"id"`
	SettingName *string `json:"settingName"       dc:"名称"`
	Image       *string `json:"image"       dc:"image"`

	SettingType *int    `json:"settingType"       dc:"类型"`
	Remark      *string `json:"remark"     dc:"备注"`
	Config      *string `json:"config"     dc:"配置"`
}

type TemplateDesignDeleteReq struct {
	g.Meta `path:"/templateDesign/delete" tags:"删除模版设计" method:"post" summary:"删除模版设计"`
	Id     uint `v:"required" json:"id"         dc:"id"`
}

//
//type TemplateOneReq struct {
//	g.Meta `path:"/template/one" method:"post" tags:"模版管理" summary:"模版->获取"`
//	Id     uint `v:"required" json:"id" dc:"id"`
//}
//type TemplateOneRes struct {
//	TemplateItem
//}
//
//type TemplateOptionsReq struct {
//	g.Meta   `path:"/template/options" method:"post" tags:"模版管理" summary:"模版->选项（供选择器）"`
//	Language string `json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
//}
//
//type TemplateOptionsRes struct {
//	Options []model.SelectOption `json:"options" dc:"选择器选项数组"`
//}
