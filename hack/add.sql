

DROP TABLE IF EXISTS `news_category`;
CREATE TABLE `news_category` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `parent_id` int unsigned NOT NULL DEFAULT 0 COMMENT '上级id，0表示顶级',
    `is_zh` int unsigned NOT NULL DEFAULT 0 COMMENT '是否中文，0-否，1-是',
    `is_en` int unsigned NOT NULL DEFAULT 0 COMMENT '是否英文，0-否，1-是',
    `is_id` int unsigned NOT NULL DEFAULT 0 COMMENT '是否印尼文，0-否，1-是',
    `status` int unsigned NOT NULL DEFAULT 0 COMMENT '状态，1启用，0关闭',
    `sort` int unsigned NOT NULL DEFAULT 0 COMMENT '排序，数字越小，排序越靠前',
    `admin_id` int unsigned NOT NULL DEFAULT 0 COMMENT '分类负责人id',
    `cover_imgs` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '封面图',
    `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
    `create_name` varchar(255) NOT NULL DEFAULT '' COMMENT '创建者',
    `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
    `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='资讯分类';

DROP TABLE IF EXISTS `news_category_language`;
CREATE TABLE `news_category_language` (
       `id` int unsigned NOT NULL AUTO_INCREMENT,
       `category_id` int unsigned NOT NULL DEFAULT 0 COMMENT '资讯分类id',
       `language_id` int unsigned NOT NULL DEFAULT 0 COMMENT '语言id,0-中文，1-英文，2-印尼语',
       `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '名称',
       `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
       `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
       `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='资讯分类-语言表';






DROP TABLE IF EXISTS `news_topic`;
CREATE TABLE `news_topic` (
     `id` int unsigned NOT NULL AUTO_INCREMENT,
     `counts` int unsigned NOT NULL DEFAULT 0 COMMENT '文章数量',
     `is_zh` int unsigned NOT NULL DEFAULT 0 COMMENT '是否中文，0-否，1-是',
     `is_en` int unsigned NOT NULL DEFAULT 0 COMMENT '是否英文，0-否，1-是',
     `is_id` int unsigned NOT NULL DEFAULT 0 COMMENT '是否印尼文，0-否，1-是',
     `status` int unsigned NOT NULL DEFAULT 0 COMMENT '是否显示，1启用，0关闭',
     `sort` int unsigned NOT NULL DEFAULT 0 COMMENT '排序，数字越小，排序越靠前',
     `admin_id` int unsigned NOT NULL DEFAULT 0 COMMENT '分类负责人id',
     `topic_imgs` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '专题图片',
     `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
     `create_name` varchar(255) NOT NULL DEFAULT '' COMMENT '创建者',
     `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
     `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
     `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='专题';


DROP TABLE IF EXISTS `news_topic_language`;
CREATE TABLE `news_topic_language` (
     `id` int unsigned NOT NULL AUTO_INCREMENT,
     `topic_id` int unsigned NOT NULL DEFAULT 0 COMMENT '专题id',
     `language_id` int unsigned NOT NULL DEFAULT 0 COMMENT '语言id,0-中文，1-英文，2-印尼语',
     `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '名称',
     `short_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '名称',
     `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
     `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
     `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='专题-语言表';



DROP TABLE IF EXISTS `news_topic_article`;
CREATE TABLE `news_topic_article` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `topic_id` int unsigned NOT NULL DEFAULT 0 COMMENT 'topic id',
  `article_id` int unsigned NOT NULL DEFAULT 0 COMMENT '文章id',
  `article_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文章name',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='资讯分类';





DROP TABLE IF EXISTS `news_article`;
CREATE TABLE `news_article` (
      `id` int unsigned NOT NULL AUTO_INCREMENT,
      `is_zh` int unsigned NOT NULL DEFAULT 0 COMMENT '是否中文，0-否，1-是',
      `is_en` int unsigned NOT NULL DEFAULT 0 COMMENT '是否英文，0-否，1-是',
      `is_id` int unsigned NOT NULL DEFAULT 0 COMMENT '是否印尼文，0-否，1-是',
      `category_id` int unsigned NOT NULL DEFAULT 0 COMMENT '分类id',
      `admin_id` int unsigned NOT NULL DEFAULT 0 COMMENT '分类负责人id',
      `cover_imgs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专题图片',
      `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
      `create_name` varchar(255) NOT NULL DEFAULT '' COMMENT '后台创建者',
      `author` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人',
      `is_top` int unsigned NOT NULL DEFAULT 0 COMMENT '是否加入头条，1启用，0关闭',
      `is_recommend` int unsigned NOT NULL DEFAULT 0 COMMENT '是否推荐，1启用，0关闭',
      `is_publish` int unsigned NOT NULL DEFAULT 0 COMMENT '是否发布，1启用，0关闭',
      `is_draft` int unsigned NOT NULL DEFAULT 0 COMMENT '是否草稿状态，1是，0否',
      `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
      `publish_time` bigint NOT NULL DEFAULT '0' COMMENT '发布时间',
      `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
      `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='文章表';

DROP TABLE IF EXISTS `news_article_language`;
CREATE TABLE `news_article_language` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
    `article_id` int unsigned NOT NULL DEFAULT 0 COMMENT '文章id',
    `language_id` int unsigned NOT NULL DEFAULT 0 COMMENT '语言id,0-中文，1-英文，2-印尼语',
    `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
    `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '正文',
    `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
    `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='文章-语言表';





DROP TABLE IF EXISTS `surah_read_record`;
create table surah_read_record(
    id                       int unsigned auto_increment primary key,
    user_id                  int unsigned default '0' not null comment '用户id',
    ayah_id                  int unsigned default '0' not null comment 'ayah_id节id',
    surah_name               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
    is_user_op                  int unsigned default '0' not null comment '是否用户操作，1是 0否',
    create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
    update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment '用户阅读记录表' collate = utf8mb4_general_ci;

DROP TABLE IF EXISTS `surah_read_collect`;
create table  surah_read_collect(
        id                       int unsigned auto_increment primary key,
        user_id                  int unsigned default '0' not null comment '用户id',
        ayah_id                  int unsigned default '0' not null comment 'ayah_id节id',
        surah_name               varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '名称',
        create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
        update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment '用户收藏表' collate = utf8mb4_general_ci;





alter table surat_ayat add column juz INT NOT NULL default  0 COMMENT 'juz编号';
alter table surat_ayat add column page INT NOT NULL default  0 COMMENT '所在页码';
alter table surat_daftar add column is_popular INT NOT NULL default  0 COMMENT '是否热门章节 0否 1是';

alter table news_article add column author_logo varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '创建人头像';
alter table news_article add column author_auth_status INT NOT NULL default  0 COMMENT '作者认证状态 0未认证 1已认证';




CREATE TABLE `news_article_collect` (
      `id` int unsigned NOT NULL AUTO_INCREMENT,
      `user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
      `article_id` int unsigned NOT NULL DEFAULT '0' COMMENT '文章id',
      `article_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '文章名称',
      `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间（注册时间）',
      `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间，0代表创建后未更新',
      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='文章收藏表';



DROP TABLE IF EXISTS `news_article_collect`;
create table  news_article_collect(
        id                       int unsigned auto_increment primary key,
        user_id                  int unsigned default '0' not null comment '用户id',
        article_id                  int unsigned default '0' not null comment 'article_id',
        article_name               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
        create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
        update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment '文章收藏表' collate = utf8mb4_general_ci;


DROP TABLE IF EXISTS `news_article_share`;
create table  news_article_share(
                                 id                       int unsigned auto_increment primary key,
                                 user_id                  int unsigned default '0' not null comment '用户id',
                                 article_id                  int unsigned default '0' not null comment 'article_id',
                                 article_name               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
                                 create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
                                 update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment '文章分享表' collate = utf8mb4_general_ci;



DROP TABLE IF EXISTS `news_article_view`;
create table  news_article_view(
                                 id                       int unsigned auto_increment primary key,
                                 user_id                  int unsigned default '0' not null comment '用户id',
                                 article_id                  int unsigned default '0' not null comment 'article_id',
                                 article_name               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
                                 create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
                                 update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment '文章浏览表' collate = utf8mb4_general_ci;


DROP TABLE IF EXISTS `news_tahlil`;
create table  news_tahlil(
   id                       int unsigned auto_increment primary key,
   name               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
   `content1` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '内容1',
   `content2` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '内容2',
   `content3` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '内容3',
   create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
   update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment '祈祷词表' collate = utf8mb4_general_ci;




DROP TABLE IF EXISTS `news_doa`;
create table  news_doa(
                             id                       int unsigned auto_increment primary key,
                             name               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
                             bacaans                  int unsigned default '0' not null comment '数量',
                             create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
                             update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment 'doa表' collate = utf8mb4_general_ci;


DROP TABLE IF EXISTS `news_doa_bacaan`;
create table  news_doa_bacaan(
                          id                       int unsigned auto_increment primary key,
                          doa_id                  int unsigned default '0' not null comment 'doa_id',
                          name               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
                          `content1` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '页码',
                          `content2` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '经文',
                          `content3` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '解释',
                          create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
                          update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment 'doa小节表' collate = utf8mb4_general_ci;




DROP TABLE IF EXISTS `news_wirid`;
create table  news_wirid(
                          id                       int unsigned auto_increment primary key,
                          name               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
                          bacaans                  int unsigned default '0' not null comment '数量',
                          create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
                          update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment 'doa表' collate = utf8mb4_general_ci;


DROP TABLE IF EXISTS `news_wirid_bacaan`;
create table  news_wirid_bacaan(
                                 id                       int unsigned auto_increment primary key,
                                 wirid_id                  int unsigned default '0' not null comment 'wirid_id',
                                 wirid_id                  int unsigned default '0' not null comment 'wirid_id',
                                 name               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
                                 `content1` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '页码',
                                 `content2` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '内容2',
                                 `content3` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '内容3',
                                 create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
                                 update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment 'doa小节表' collate = utf8mb4_general_ci;


alter table news_wirid_bacaan add column pid INT NOT NULL default  0 COMMENT '父级id';
alter table news_doa_bacaan add column pid INT NOT NULL default  0 COMMENT '父级id';

DROP TABLE IF EXISTS `doa_read_collect`;
create table  doa_read_collect(
    id                       int unsigned auto_increment primary key,
    user_id                  int unsigned default '0' not null comment '用户id',
    types                  int unsigned default '0' not null comment '类型 1-doa,2-wirid',
    p_id                  int unsigned default '0' not null comment '父级id',
    baccan_id                  int unsigned default '0' not null comment 'baccan_id',
    p_name               varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '父级名称',
    baccan_name               varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '名称',
    create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
    update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment 'doa&wirid收藏表' collate = utf8mb4_general_ci;


-----------------------------------------example-------------
create table if not exists user
(
    id                       int unsigned auto_increment
    primary key,
    agent_id                 int unsigned default '0' not null comment '上级（代理）的id',
    agent_code               varchar(32)  default ''  not null comment '注册时填写的代理码（选填）',
    account                  varchar(32)  default ''  not null comment '账号',
    password                 varchar(64)  default ''  not null comment '密码',
    pay_password             varchar(64)  default ''  not null comment '支付密码',
    password_modify_time     bigint       default 0   not null comment '登录密码支付密码最近修改时间',
    area_code                char(6)      default ''  not null comment '手机国际区号，如：86',
    phone_num                char(16)     default ''  not null comment '手机号',
    bind_phone_time          bigint       default 0   not null comment '手机号绑定时间',
    email                    varchar(64)  default ''  not null comment '邮箱地址',
    bind_email_time          bigint       default 0   not null comment '邮箱绑定时间',
    bind_real_name_time      bigint       default 0   not null comment '真实姓名绑定时间',
    vip_level                int          default 0   not null comment 'vip等级',
    level_id                 int unsigned default '0' not null comment '会员层级id',
    is_banned                tinyint      default 1   not null comment '账号封号状态： 1 正常 2 封号',
    is_prohibit              tinyint      default 1   not null comment '提取状态：1 正常 2 禁提',
    is_online                tinyint      default 2   not null comment '是否在线：1是  2 否',
    online_duration          bigint       default 0   not null comment '在线时长（单位：秒）',
    signin_count             int          default 0   not null comment '登录次数',
    last_signin_time         bigint       default 0   not null comment '最后一次登录时间',
    last_signin_ip           varchar(64)  default ''  not null comment '最后登录ip',
    last_signin_device_id    varchar(64)  default ''  not null comment '最后登录设备号',
    last_signin_app_type     tinyint      default 0   not null comment '最近登录应用类型（1:android  2: ios，3:h5，4:web，5:其他）',
    last_signin_app_version  varchar(32)  default ''  not null comment '最近登录应用类型版本号',
    signup_ip                varchar(64)  default ''  not null comment '注册ip',
    signup_ip_region         varchar(64)  default ''  not null comment '注册IP地理区域',
    signup_device_id         varchar(64)  default ''  not null comment '注册设备号（设备指纹）',
    signup_device_os         varchar(32)  default ''  not null comment '注册设备系统（android,ios,windows,mac,...）',
    signup_device_os_version varchar(32)  default ''  not null comment '注册设备系统版本号',
    signup_device_type       tinyint      default 0   not null comment '注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）',
    signup_app_type          tinyint      default 0   not null comment '应用类型（1:android  2: ios，3:h5，4:web，5:其他）',
    signup_app_version       varchar(32)  default ''  not null comment '注册应用类型版本号',
    signup_host              varchar(64)  default ''  not null comment '注册域名(接口域名)',
    signup_domain            varchar(64)  default ''  not null comment '注册域名(页面原始域名)',
    signup_domain2           varchar(64)  default ''  not null comment '注册域名(页面域名)',
    last_signin_log_id       int          default 0   not null comment '最后一次登录日志id（如果有分表的话，要考虑时间）',
    device_token_ios         varchar(256) default ''  not null comment 'IOS推送token',
    device_token_android     varchar(256) default ''  not null comment 'android推送token(FCM)',
    security_password        varchar(64)  default ''  not null comment '安全密码，修改个人绑定信息时要验证',
    version                  int          default 1   null comment '该记录的版本号',
    is_test                  int          default 0   not null comment '测试账号：  1 是 ，其他值：否',
    limit_start_time         bigint       default 0   not null comment '限制登录开始时间',
    limit_end_time           bigint       default 0   not null comment '限制登录结束时间',
    create_time              bigint       default 0   not null comment '创建时间（注册时间）',
    update_time              bigint       default 0   not null comment '更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）',
    create_account           varchar(32)  default ''  not null comment '创建者账号',
    create_type              tinyint      default 0   not null comment '创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）',
    update_account           varchar(32)  default ''  not null comment '更新者账号',
    update_type              tinyint      default 0   not null comment '更新者来源',
    invite_code              varchar(32)  default ''  not null comment '邀请码',
    transfer_code            varchar(16)  default ''  not null comment '转线码',
    noob_task_finish_time    bigint       default 0   not null comment '新手任务完成时间',
    data_type                tinyint      default 1   not null comment '数据类型:1正式数据;2测试数据',
    pixel_id                 varchar(255) default ''  not null comment '像素id',
    source                   tinyint      default 0   not null comment '注册来源( 1直客，2代理，3邀请，4后台）',
    constraint account_idx
    unique (account) comment '账号'
    )
    comment '会员表（部分可根据需要考虑分表出来）' collate = utf8mb4_general_ci;
